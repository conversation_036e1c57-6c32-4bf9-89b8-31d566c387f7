/* Reset e base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  min-height: 100vh;
  color: white;
  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);
  background-attachment: fixed;
}

/* Utility Classes */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.min-h-screen { min-height: 100vh; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.leading-relaxed { line-height: 1.625; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.mb-2 { margin-bottom: 0.25rem; }
.mb-4 { margin-bottom: 0.5rem; }
.mb-6 { margin-bottom: 0.75rem; }
.space-y-3 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 0.75rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }
.text-xl { font-size: 1rem; line-height: 1.25rem; }
.text-sm { font-size: 0.75rem; line-height: 1rem; }
.text-xs { font-size: 0.625rem; line-height: 0.875rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.text-white { color: white; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-80 { opacity: 0.8; }
.rounded-2xl { border-radius: 1rem; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.transition-all { transition: all 0.15s ease; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.hover\:scale-105:hover { transform: scale(1.05); }
.animate-spin { animation: spin 1s linear infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.flex-1 { flex: 1 1 0%; }
.overflow-y-auto { overflow-y: auto; }
.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.border-t { border-top-width: 1px; }
.hidden { display: none; }
.block { display: block; }

/* Responsive visibility */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
}

/* Component Classes */
.glass-effect {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-radius: 1rem;
}

.chat-bubble {
  padding: 0.75rem;
  border-radius: 0.75rem;
  max-width: 16rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  animation: slideInFromBottom 0.3s ease-out;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-size: 0.875rem;
  line-height: 1.25rem;
  display: flex;
  align-items: center;
  text-align: left;
}

.chat-bubble-user {
  background: rgba(37, 99, 235, 0.8);
  margin-left: auto;
}

.chat-bubble-ai {
  background: rgba(255, 255, 255, 0.1);
  margin-right: auto;
}

.aura-animation {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  filter: blur(1px);
}

.input-field {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 0.5rem 0.75rem;
  color: white;
  transition: all 0.3s ease;
  width: 100%;
  resize: none;
  min-height: 40px;
  max-height: 80px;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-field:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: scale(0.95);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:disabled:hover {
  transform: none;
}

/* Layout responsive per chat */
.chat-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 100vw;
  overflow: hidden;
}

.chat-header {
  flex-shrink: 0;
  padding: 0.5rem;
}

.chat-main-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.glass-frame-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: scroll !important;
  overflow-x: hidden;
  padding: 0 0.75rem;
  min-height: 0;
  max-height: calc(100vh - 200px);
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

.chat-messages::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

.chat-input-area {
  flex-shrink: 0;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile First Responsive */
@media (max-width: 640px) {
  .chat-header { padding: 0.25rem; }
  .chat-messages { padding: 0 0.5rem; }
  .chat-input-area { padding: 0.5rem; }
  .text-3xl { font-size: 1rem; line-height: 1.25rem; }
  .text-xl { font-size: 0.875rem; line-height: 1rem; }
  .p-6 { padding: 0.75rem; }
  .mb-6 { margin-bottom: 0.5rem; }
  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; line-height: 1.2; }
}

@media (max-width: 480px) {
  .chat-header { padding: 0.125rem; }
  .chat-messages { padding: 0 0.25rem; }
  .chat-input-area { padding: 0.25rem; }
  .text-3xl { font-size: 0.875rem; line-height: 1rem; }
  .space-y-3 > * + * { margin-top: 0.25rem; }
  .space-y-4 > * + * { margin-top: 0.5rem; }
  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; line-height: 1.1; }
  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }
}

@media (min-width: 768px) {
  .chat-layout { max-width: 4xl; margin: 0 auto; }
}

@media (min-width: 1024px) {
  .chat-messages { padding: 0 2rem; }
  .chat-input-area { padding: 1.5rem 2rem; }
}

/* Keyframe Animations */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
