/* Reset e base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  min-height: 100vh;
  color: white;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  background-attachment: fixed;
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;
  animation: liquidMove 20s ease-in-out infinite;
}

/* Utility Classes */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.min-h-screen { min-height: 100vh; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.leading-relaxed { line-height: 1.625; }
.whitespace-pre-wrap { white-space: pre-wrap; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.mb-2 { margin-bottom: 0.25rem; }
.mb-4 { margin-bottom: 0.5rem; }
.mb-6 { margin-bottom: 0.75rem; }
.space-y-3 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 0.75rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }
.text-xl { font-size: 1rem; line-height: 1.25rem; }
.text-sm { font-size: 0.75rem; line-height: 1rem; }
.text-xs { font-size: 0.625rem; line-height: 0.875rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.text-white { color: white; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-80 { opacity: 0.8; }
.rounded-2xl { border-radius: 1rem; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.transition-all { transition: all 0.15s ease; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.hover\:scale-105:hover { transform: scale(1.05); }
.animate-spin { animation: spin 1s linear infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.flex-1 { flex: 1 1 0%; }
.overflow-y-auto { overflow-y: auto; }
.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.border-t { border-top-width: 1px; }
.hidden { display: none; }
.block { display: block; }

/* Responsive visibility */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
}

/* Component Classes */
.glass-effect {
  backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
}

/* Chat Messages */
.message-container {
  margin-bottom: 1rem;
  display: flex;
}

.message-container.user {
  justify-content: flex-end;
}

.message-container.assistant {
  justify-content: flex-start;
}

.message {
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  word-wrap: break-word;
  font-size: 0.95rem;
  line-height: 1.4;
}

.user-message {
  background: #007AFF;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.assistant-message {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-bottom-left-radius: 0.25rem;
}

.aura-animation {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  filter: blur(1px);
}

.input-field {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 1rem;
  resize: none;
  min-height: 44px;
  max-height: 120px;
  outline: none;
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.input-field:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.send-button {
  background: #007AFF;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-button:hover {
  background: #0056CC;
  transform: scale(1.05);
}

.send-button:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

.btn:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: scale(0.95);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:disabled:hover {
  transform: none;
}

/* Layout responsive per chat */
.chat-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 100vw;
  overflow: hidden;
}

.chat-header {
  flex-shrink: 0;
  padding: 0.5rem;
}

.chat-container {
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.chat-messages::-webkit-scrollbar {
  display: none;
}

.chat-input-area {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.input-container {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

/* Mobile First Responsive */
@media (max-width: 640px) {
  .chat-header { padding: 0.25rem; }
  .chat-messages { padding: 0 0.5rem; }
  .chat-input-area { padding: 0.5rem; }
  .text-3xl { font-size: 1rem; line-height: 1.25rem; }
  .text-xl { font-size: 0.875rem; line-height: 1rem; }
  .p-6 { padding: 0.75rem; }
  .mb-6 { margin-bottom: 0.5rem; }
  .chat-bubble {
    max-width: 85%;
    min-width: 6rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    line-height: 1.3;
  }
  .chat-main-container { max-width: 100%; margin: 0; }
}

@media (max-width: 480px) {
  .chat-header { padding: 0.125rem; }
  .chat-messages { padding: 0 0.25rem; }
  .chat-input-area { padding: 0.25rem; }
  .text-3xl { font-size: 0.875rem; line-height: 1rem; }
  .space-y-3 > * + * { margin-top: 0.25rem; }
  .space-y-4 > * + * { margin-top: 0.5rem; }
  .chat-bubble {
    max-width: 90%;
    min-width: 5rem;
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
    line-height: 1.2;
  }
  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }
}

@media (min-width: 768px) {
  .chat-layout { max-width: 4xl; margin: 0 auto; }
}

@media (min-width: 1024px) {
  .chat-messages { padding: 0 2rem; }
  .chat-input-area { padding: 1.5rem 2rem; }
}

/* Keyframe Animations */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes liquidMove {
  0%, 100% {
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }
  25% {
    background:
      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),
      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }
  50% {
    background:
      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }
  75% {
    background:
      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),
      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }
}
