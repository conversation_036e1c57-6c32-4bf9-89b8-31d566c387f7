"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString() + Math.random(),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                setUserMemory((prev)=>({\n                        ...prev,\n                        name\n                    }));\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        age\n                    }));\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        emotions: prev.emotions.includes(emotion) ? prev.emotions : [\n                            ...prev.emotions,\n                            emotion\n                        ]\n                    }));\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            setUserMemory((prev)=>{\n                const newLevel = Math.min(prev.intimacyLevel + 1, 4);\n                // Se il livello è cambiato, mostra un feedback visivo\n                if (newLevel > prev.intimacyLevel) {\n                    console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                }\n                return {\n                    ...prev,\n                    intimacyLevel: newLevel\n                };\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoading\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_6__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setShowWelcome(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '0.875rem'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    messages.length,\n                                    \" msg\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"k80fG2EP4tiuKMDgt2GXwaVMwKY=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});