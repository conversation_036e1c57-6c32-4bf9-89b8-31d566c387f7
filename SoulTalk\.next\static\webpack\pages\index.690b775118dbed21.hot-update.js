"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-browser)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-browser)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se non è autenticato, mostra sempre il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Caricamento...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.9)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Caricando i tuoi dati...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            if (confirm('Reset onboarding per test?')) {\n                                                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_10__.resetOnboardingStatus)(userId);\n                                                window.location.reload();\n                                            }\n                                        },\n                                        style: {\n                                            color: 'rgba(255,255,255,0.6)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.75rem'\n                                        },\n                                        children: \"Debug\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            messages.length,\n                                            \" msg\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this),\n                                    isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"ID: \",\n                                            userId.slice(-6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"1Ufb3RIuGn3DPiXmxRwWzC8X8Z4=\", false, function() {\n    return [\n        _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__.useSupabaseMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9wYWdlcy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkQ7QUFDOUI7QUFDcUI7QUFDQTtBQUMyQjtBQUMvQjtBQUNZO0FBQ0o7QUFDUztBQUNYO0FBRXJDLFNBQVNhOztJQUN0QixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHZCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNlLGFBQWFDLGVBQWUsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2lCLGFBQWFDLGVBQWUsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1tQixXQUFXbEIsNkNBQU1BLENBQXNCO0lBQzdDLE1BQU1tQixtQkFBbUJuQiw2Q0FBTUEsQ0FBaUI7SUFFaEQsMENBQTBDO0lBQzFDLE1BQU0sRUFDSm9CLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxVQUFVLEVBQ1ZDLFdBQVdDLGFBQWEsRUFDeEJDLFFBQVEsRUFDUkMsZUFBZSxFQUNmQyxlQUFlLEVBQ2ZDLFVBQVUsRUFDVkMsZ0JBQWdCLEVBQ2hCQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQkMsTUFBTSxFQUNOQywwQkFBMEIsRUFDM0IsR0FBR3hCLDJFQUFpQkE7SUFFckIsTUFBTXlCLGlCQUFpQjtRQUNyQixJQUFJZixpQkFBaUJnQixPQUFPLEVBQUU7WUFDNUJDLFdBQVc7Z0JBQ1QsSUFBSWpCLGlCQUFpQmdCLE9BQU8sRUFBRTtvQkFDNUJoQixpQkFBaUJnQixPQUFPLENBQUNFLFFBQVEsQ0FBQzt3QkFDaENDLEtBQUtuQixpQkFBaUJnQixPQUFPLENBQUNJLFlBQVk7d0JBQzFDQyxVQUFVO29CQUNaO2dCQUNGO1lBQ0YsR0FBRztRQUNMO0lBQ0Y7SUFFQSw4RUFBOEU7SUFDOUUsTUFBTUMsZUFBZTtRQUNuQixNQUFNQyxTQUFTO1lBQ2I7WUFDQTtZQUNBO1lBQ0E7WUFDQSxVQUFXLGdCQUFnQjtTQUM1QjtRQUNELE9BQU9BLE1BQU0sQ0FBQ3BCLFdBQVdxQixhQUFhLENBQUMsSUFBSUQsTUFBTSxDQUFDLEVBQUU7SUFDdEQ7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTUUsdUJBQXVCO1FBQzNCLE1BQU1ELGdCQUFnQnJCLFdBQVdxQixhQUFhO1FBRTlDLE1BQU1FLG9CQUFvQjtZQUN4QiwrQ0FBK0M7WUFDL0M7Z0JBQ0VILFFBQVE7b0JBQUM7b0JBQTRCO29CQUEyQjtpQkFBMkI7Z0JBQzNGSSxjQUFjO1lBQ2hCO1lBQ0EsMENBQTBDO1lBQzFDO2dCQUNFSixRQUFRO29CQUFDO29CQUEyQjtvQkFBNEI7aUJBQTBCO2dCQUMxRkksY0FBYztZQUNoQjtZQUNBLG9DQUFvQztZQUNwQztnQkFDRUosUUFBUTtvQkFBQztvQkFBNEI7b0JBQTJCO2lCQUEwQjtnQkFDMUZJLGNBQWM7WUFDaEI7WUFDQSwwQ0FBMEM7WUFDMUM7Z0JBQ0VKLFFBQVE7b0JBQUM7b0JBQTJCO29CQUEyQjtpQkFBMkI7Z0JBQzFGSSxjQUFjO1lBQ2hCO1lBQ0EsMENBQTBDO1lBQzFDO2dCQUNFSixRQUFRO29CQUFDO29CQUEyQjtvQkFBNEI7aUJBQTRCO2dCQUM1RkksY0FBYztZQUNoQjtTQUNEO1FBRUQsTUFBTUMsU0FBU0YsaUJBQWlCLENBQUNGLGNBQWMsSUFBSUUsaUJBQWlCLENBQUMsRUFBRTtRQUV2RSxPQUFPO1lBQ0xHLFlBQVksZ0RBRTJCRCxPQURBQSxPQUFPTCxNQUFNLENBQUMsRUFBRSxFQUFDLHVFQUVqQkssT0FEQUEsT0FBT0wsTUFBTSxDQUFDLEVBQUUsRUFBQyx1RUFFcERLLE9BRG1DQSxPQUFPTCxNQUFNLENBQUMsRUFBRSxFQUFDLG9DQUNoQyxPQUFwQkssT0FBT0QsWUFBWSxFQUFDO1lBRXhCRyxnQkFBZ0I7WUFDaEJDLHNCQUFzQjtZQUN0QkMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7SUFDRjtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNQyx5QkFBeUIsT0FBT0MsYUFBcUJDO1FBQ3pELE1BQU1DLG1CQUFtQkYsWUFBWUcsV0FBVztRQUNoRCxNQUFNQyxVQUFlLENBQUM7UUFFdEIsMkJBQTJCO1FBQzNCLElBQUlGLGlCQUFpQkcsUUFBUSxDQUFDLGdCQUFnQkgsaUJBQWlCRyxRQUFRLENBQUMsVUFBVTtZQUNoRixNQUFNQyxZQUFZTixZQUFZTyxLQUFLLENBQUM7WUFDcEMsSUFBSUQsYUFBYSxDQUFDdEMsV0FBV3dDLElBQUksRUFBRTtnQkFDakMsTUFBTUEsT0FBT0YsU0FBUyxDQUFDLEVBQUUsSUFBSUEsU0FBUyxDQUFDLEVBQUU7Z0JBQ3pDRixRQUFRSSxJQUFJLEdBQUdBO1lBQ2pCO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsTUFBTUMsV0FBV1QsWUFBWU8sS0FBSyxDQUFDO1FBQ25DLElBQUlFLFlBQVksQ0FBQ3pDLFdBQVcwQyxHQUFHLEVBQUU7WUFDL0IsTUFBTUEsTUFBTUMsU0FBU0YsUUFBUSxDQUFDLEVBQUUsSUFBSUEsUUFBUSxDQUFDLEVBQUU7WUFDL0MsSUFBSUMsTUFBTSxLQUFLQSxNQUFNLEtBQUs7Z0JBQ3hCTixRQUFRTSxHQUFHLEdBQUdBO1lBQ2hCO1FBQ0Y7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTUUsV0FBVztZQUNmLFVBQVU7Z0JBQUM7Z0JBQVU7Z0JBQVk7Z0JBQVc7YUFBVTtZQUN0RCxVQUFVO2dCQUFDO2dCQUFVO2dCQUFZO2dCQUFPO2FBQU87WUFDL0MsY0FBYztnQkFBQztnQkFBYztnQkFBVzthQUFZO1lBQ3BELGVBQWU7Z0JBQUM7Z0JBQWU7Z0JBQVc7YUFBVTtZQUNwRCxZQUFZO2dCQUFDO2dCQUFZO2dCQUFjO2FBQVM7WUFDaEQsU0FBUztnQkFBQztnQkFBUztnQkFBYzthQUFTO1FBQzVDO1FBRUEsS0FBSyxNQUFNLENBQUNDLFNBQVNDLFNBQVMsSUFBSUMsT0FBT0MsT0FBTyxDQUFDSixVQUFXO1lBQzFELElBQUlFLFNBQVNHLElBQUksQ0FBQ0MsQ0FBQUEsVUFBV2hCLGlCQUFpQkcsUUFBUSxDQUFDYSxXQUFXO2dCQUNoRSxJQUFJLENBQUNsRCxXQUFXNEMsUUFBUSxDQUFDUCxRQUFRLENBQUNRLFVBQVU7b0JBQzFDVCxRQUFRUSxRQUFRLEdBQUc7MkJBQUk1QyxXQUFXNEMsUUFBUTt3QkFBRUM7cUJBQVE7Z0JBQ3REO2dCQUNBO1lBQ0Y7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJYixZQUFZbUIsTUFBTSxHQUFHLE9BQ3JCakIsaUJBQWlCRyxRQUFRLENBQUMsZ0JBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxjQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsZUFDMUJILGlCQUFpQkcsUUFBUSxDQUFDLFlBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxlQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsaUJBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxZQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsVUFBVTtZQUV0QyxNQUFNZSxXQUFXQyxLQUFLQyxHQUFHLENBQUN0RCxXQUFXcUIsYUFBYSxHQUFHLEdBQUc7WUFFeEQsc0RBQXNEO1lBQ3RELElBQUkrQixXQUFXcEQsV0FBV3FCLGFBQWEsRUFBRTtnQkFDdkNrQyxRQUFRQyxHQUFHLENBQUMsa0RBQWlELE9BQVRKLFVBQVM7Z0JBQzdEaEIsUUFBUWYsYUFBYSxHQUFHK0I7WUFDMUI7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJTCxPQUFPVSxJQUFJLENBQUNyQixTQUFTZSxNQUFNLEdBQUcsR0FBRztZQUNuQzVDLGlCQUFpQjZCO1FBQ25CO0lBQ0Y7SUFFQXpELGdEQUFTQTswQkFBQztZQUNSaUM7UUFDRjt5QkFBRztRQUFDYjtRQUFVUDtLQUFZO0lBRTFCLDZEQUE2RDtJQUM3RGIsZ0RBQVNBOzBCQUFDO1lBQ1IsSUFBSW9CLFNBQVNvRCxNQUFNLEdBQUcsR0FBRztnQkFDdkJ4RCxlQUFlO1lBQ2pCO1FBQ0Y7eUJBQUc7UUFBQ0k7S0FBUztJQUViLGtFQUFrRTtJQUNsRXBCLGdEQUFTQTswQkFBQztZQUNSLE1BQU0rRSxrQkFBa0JwQztZQUN4QixNQUFNcUMsT0FBT0MsU0FBU0QsSUFBSTtZQUUxQix3REFBd0Q7WUFDeERaLE9BQU9jLE1BQU0sQ0FBQ0YsS0FBS0csS0FBSyxFQUFFSjtZQUUxQix1REFBdUQ7WUFDdkRDLEtBQUtJLFNBQVMsR0FBRyxrQkFBMkMsT0FBekIvRCxXQUFXcUIsYUFBYTtZQUUzRCxzRUFBc0U7WUFDdEU7a0NBQU87Z0JBQ0wsK0NBQStDO2dCQUNqRDs7UUFDRjt5QkFBRztRQUFDckIsV0FBV3FCLGFBQWE7S0FBQztJQUU3QixNQUFNMkMsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQzFFLGFBQWEyRSxJQUFJLE1BQU16RSxhQUFhO1FBRXpDLE1BQU13QyxjQUFjMUMsYUFBYTJFLElBQUk7UUFDckMxRSxnQkFBZ0I7UUFDaEJFLGVBQWU7UUFFZixtREFBbUQ7UUFDbkQsTUFBTXlFLFVBQVUsTUFBTTVELFdBQVcwQixhQUFhO1FBQzlDdUIsUUFBUUMsR0FBRyxDQUFDLCtDQUErQ3pELFNBQVNvRCxNQUFNLEdBQUc7UUFFN0UsZ0RBQWdEO1FBQ2hEckMsV0FBV0YsZ0JBQWdCO1FBRTNCLElBQUk7WUFDRixzRUFBc0U7WUFDdEUsTUFBTXVELHNCQUFzQjttQkFBSXBFO2dCQUFVbUU7YUFBUSxDQUFDRSxHQUFHLENBQUNDLENBQUFBLE1BQVE7b0JBQzdEQyxNQUFNRCxJQUFJRSxNQUFNLEdBQUcsU0FBa0I7b0JBQ3JDQyxTQUFTSCxJQUFJRyxPQUFPO2dCQUN0QjtZQUVBLE1BQU1DLFdBQVcsTUFBTXZGLGtFQUFlQSxDQUNwQzhDLGFBQ0FtQyxxQkFDQTtnQkFDRTNCLE1BQU14QyxXQUFXd0MsSUFBSSxJQUFJa0M7Z0JBQ3pCaEMsS0FBSzFDLFdBQVcwQyxHQUFHLElBQUlnQztnQkFDdkJDLFFBQVEzRSxXQUFXMkUsTUFBTTtnQkFDekIvQixVQUFVNUMsV0FBVzRDLFFBQVE7Z0JBQzdCdkIsZUFBZXJCLFdBQVdxQixhQUFhO1lBQ3pDO1lBR0YsK0JBQStCO1lBQy9CLE1BQU1mLFdBQVdtRSxVQUFVO1lBQzNCbEIsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ3pELFNBQVNvRCxNQUFNLEdBQUc7WUFFeEUsa0RBQWtEO1lBQ2xELE1BQU1wQix1QkFBdUJDLGFBQWF5QztRQUU1QyxFQUFFLE9BQU9HLE9BQU87WUFDZHJCLFFBQVFxQixLQUFLLENBQUMsVUFBVUE7WUFDeEIsTUFBTXRFLFdBQVcsaURBQWlEO1FBQ3BFLFNBQVU7WUFDUmIsZUFBZTtRQUNqQjtJQUNGO0lBRUEsTUFBTW9GLGdCQUFnQixDQUFDQztRQUNyQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7WUFDcENGLEVBQUVHLGNBQWM7WUFDaEJqQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNa0IsY0FBYztRQUNsQixJQUFJQyxRQUFRLHNHQUFzRztZQUNoSCxNQUFNM0U7WUFDTmIsZUFBZTtRQUNqQjtJQUNGO0lBRUEsdURBQXVEO0lBQ3ZELElBQUksQ0FBQ1MsaUJBQWlCO1FBQ3BCLHFCQUNFOzs4QkFDRSw4REFBQ3hCLGtEQUFJQTs7c0NBQ0gsOERBQUN3RztzQ0FBTTs7Ozs7O3NDQUNQLDhEQUFDQzs0QkFBSzdDLE1BQUs7NEJBQWNnQyxTQUFROzs7Ozs7c0NBQ2pDLDhEQUFDYTs0QkFBSzdDLE1BQUs7NEJBQVdnQyxTQUFROzs7Ozs7c0NBQzlCLDhEQUFDYzs0QkFBS0MsS0FBSTs0QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzhCQUV4Qiw4REFBQ3hHLDREQUFRQTtvQkFBQ3lHLGVBQWVoRjs7Ozs7Ozs7SUFHL0I7SUFFQSwyRUFBMkU7SUFDM0UsSUFBSVAsZUFBZTtRQUNqQixxQkFDRTs7OEJBQ0UsOERBQUN0QixrREFBSUE7O3NDQUNILDhEQUFDd0c7c0NBQU07Ozs7OztzQ0FDUCw4REFBQ0M7NEJBQUs3QyxNQUFLOzRCQUFXZ0MsU0FBUTs7Ozs7O3NDQUM5Qiw4REFBQ2M7NEJBQUtDLEtBQUk7NEJBQU9DLE1BQUs7Ozs7Ozs7Ozs7Ozs4QkFFeEIsOERBQUNFO29CQUFJNUIsT0FBTzt3QkFDVjZCLFVBQVU7d0JBQ1YzRSxLQUFLO3dCQUNMNEUsTUFBTTt3QkFDTkMsT0FBTzt3QkFDUEMsUUFBUTt3QkFDUnBFLFlBQVk7d0JBQ1pxRSxTQUFTO3dCQUNUQyxZQUFZO3dCQUNaQyxnQkFBZ0I7d0JBQ2hCQyxRQUFRO29CQUNWOzhCQUNFLDRFQUFDUjt3QkFBSTVCLE9BQU87NEJBQUVxQyxPQUFPOzRCQUFTQyxXQUFXO3dCQUFTOzswQ0FDaEQsOERBQUNWO2dDQUFJNUIsT0FBTztvQ0FBRXVDLFVBQVU7b0NBQVFDLGNBQWM7Z0NBQU87MENBQUc7Ozs7OzswQ0FDeEQsOERBQUNaOzBDQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS2Y7SUFFQSxnRUFBZ0U7SUFDaEUsSUFBSXJGLGlCQUFpQjtRQUNuQixxQkFDRTs7OEJBQ0UsOERBQUN6QixrREFBSUE7O3NDQUNILDhEQUFDd0c7c0NBQU07Ozs7OztzQ0FDUCw4REFBQ0M7NEJBQUs3QyxNQUFLOzRCQUFjZ0MsU0FBUTs7Ozs7O3NDQUNqQyw4REFBQ2E7NEJBQUs3QyxNQUFLOzRCQUFXZ0MsU0FBUTs7Ozs7O3NDQUM5Qiw4REFBQ2M7NEJBQUtDLEtBQUk7NEJBQU9DLE1BQUs7Ozs7Ozs7Ozs7Ozs4QkFFeEIsOERBQUN2RyxrRUFBY0E7b0JBQ2JhLFFBQVFBO29CQUNSeUcsWUFBWTVGOzs7Ozs7OztJQUlwQjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQy9CLGtEQUFJQTs7a0NBQ0gsOERBQUN3RztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBSzdDLE1BQUs7d0JBQWNnQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDYTt3QkFBSzdDLE1BQUs7d0JBQVdnQyxTQUFROzs7Ozs7a0NBQzlCLDhEQUFDYzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUd4Qiw4REFBQ0U7Z0JBQUkzQixXQUFVOztvQkFFWDdELENBQUFBLGlCQUFpQixDQUFDSixNQUFLLG1CQUN2Qiw4REFBQzRGO3dCQUFJNUIsT0FBTzs0QkFDVjZCLFVBQVU7NEJBQ1YzRSxLQUFLOzRCQUNMNEUsTUFBTTs0QkFDTkMsT0FBTzs0QkFDUEMsUUFBUTs0QkFDUnBFLFlBQVk7NEJBQ1pxRSxTQUFTOzRCQUNUQyxZQUFZOzRCQUNaQyxnQkFBZ0I7NEJBQ2hCQyxRQUFRO3dCQUNWO2tDQUNFLDRFQUFDUjs0QkFBSTVCLE9BQU87Z0NBQUVxQyxPQUFPO2dDQUFTQyxXQUFXOzRCQUFTOzs4Q0FDaEQsOERBQUNWO29DQUFJNUIsT0FBTzt3Q0FBRXVDLFVBQVU7d0NBQVFDLGNBQWM7b0NBQU87OENBQUc7Ozs7Ozs4Q0FDeEQsOERBQUNaOzhDQUFLLENBQUM1RixTQUFTLHNCQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTVDLDhEQUFDNEY7d0JBQUkzQixXQUFVOzswQ0FDYiw4REFBQzJCO2dDQUFJNUIsT0FBTztvQ0FBRWlDLFNBQVM7b0NBQVFDLFlBQVk7b0NBQVVRLEtBQUs7Z0NBQU87O2tEQUMvRCw4REFBQ2Q7d0NBQUk1QixPQUFPOzRDQUFFaUMsU0FBUzs0Q0FBUUMsWUFBWTs0Q0FBVVEsS0FBSzt3Q0FBVTs7MERBQ2xFLDhEQUFDZDtnREFDQzVCLE9BQU87b0RBQ0wyQyxPQUFPO29EQUNQQyxRQUFRO29EQUNSQyxjQUFjO29EQUNkakYsWUFBWSwyQkFBMEMsT0FBZlAsZ0JBQWU7b0RBQ3RENEUsU0FBUztvREFDVEMsWUFBWTtvREFDWkMsZ0JBQWdCO29EQUNoQkksVUFBVTtvREFDVnhFLFdBQVc7Z0RBQ2I7MERBQ0Q7Ozs7OzswREFHRCw4REFBQzZEOztrRUFDQyw4REFBQ2tCO3dEQUFHOUMsT0FBTzs0REFBRXFDLE9BQU87NERBQVNFLFVBQVU7NERBQVVRLFlBQVk7NERBQVFDLFFBQVE7d0RBQUU7a0VBQUc7Ozs7OztrRUFHbEYsOERBQUNDO3dEQUFFakQsT0FBTzs0REFBRXFDLE9BQU87NERBQXlCRSxVQUFVOzREQUFXUyxRQUFRO3dEQUFFO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2xGLDhEQUFDL0gsc0VBQWVBO3dDQUFDc0MsZUFBZXJCLFdBQVdxQixhQUFhOzs7Ozs7Ozs7Ozs7MENBRTFELDhEQUFDcUU7Z0NBQUk1QixPQUFPO29DQUFFaUMsU0FBUztvQ0FBUVMsS0FBSztnQ0FBTzs7a0RBQ3pDLDhEQUFDUTt3Q0FDQ0MsU0FBUzs0Q0FDUCxJQUFJOUIsUUFBUSwrQkFBK0I7Z0RBQ3pDLE1BQU0vRixpRUFBcUJBLENBQUNVO2dEQUM1Qm9ILE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTTs0Q0FDeEI7d0NBQ0Y7d0NBQ0F0RCxPQUFPOzRDQUFFcUMsT0FBTzs0Q0FBeUJ6RSxZQUFZOzRDQUFRMkYsUUFBUTs0Q0FBUUMsUUFBUTs0Q0FBV2pCLFVBQVU7d0NBQVU7a0RBQ3JIOzs7Ozs7a0RBR0QsOERBQUNXO3dDQUFPQyxTQUFTdkc7d0NBQVFvRCxPQUFPOzRDQUFFcUMsT0FBTzs0Q0FBeUJ6RSxZQUFZOzRDQUFRMkYsUUFBUTs0Q0FBUUMsUUFBUTs0Q0FBV2pCLFVBQVU7d0NBQVc7a0RBQUc7Ozs7OztrREFHakosOERBQUNXO3dDQUFPQyxTQUFTL0I7d0NBQWFwQixPQUFPOzRDQUFFcUMsT0FBTzs0Q0FBU3pFLFlBQVk7NENBQVEyRixRQUFROzRDQUFRQyxRQUFROzRDQUFXakIsVUFBVTt3Q0FBVztrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU8xSSw4REFBQ1g7d0JBQUk1QixPQUFPOzRCQUFFeUQsTUFBTTs0QkFBR0MsU0FBUzt3QkFBUztrQ0FDdkMsNEVBQUMxSSw4REFBVUE7NEJBQUMySSxXQUFXdEc7NEJBQWdCNEMsV0FBVTtzQ0FDL0MsNEVBQUMyQjtnQ0FDQ2dDLEtBQUs3SDtnQ0FDTGtFLFdBQVU7Z0NBQ1ZELE9BQU87b0NBQUV5RCxNQUFNO29DQUFHSSxXQUFXO29DQUFRSCxTQUFTO2dDQUFPOztvQ0FHcEQ5SCw2QkFDQyw4REFBQ2dHO3dDQUFJNUIsT0FBTzs0Q0FBRXNDLFdBQVc7NENBQVVvQixTQUFTO3dDQUFXO2tEQUNyRCw0RUFBQzlCOzRDQUFJM0IsV0FBVTs0Q0FBZUQsT0FBTztnREFDbkM4RCxVQUFVO2dEQUNWZCxRQUFRO2dEQUNSVSxTQUFTO2dEQUNUYixjQUFjO2dEQUNkakYsWUFBWTtnREFDWm1HLGdCQUFnQjtnREFDaEJSLFFBQVE7NENBQ1Y7OzhEQUNFLDhEQUFDUztvREFBR2hFLE9BQU87d0RBQ1RxQyxPQUFPO3dEQUNQRSxVQUFVO3dEQUNWUSxZQUFZO3dEQUNaUCxjQUFjO3dEQUNkUSxRQUFRO29EQUNWOzhEQUFHOzs7Ozs7OERBR0gsOERBQUNDO29EQUFFakQsT0FBTzt3REFDUnFDLE9BQU87d0RBQ1BFLFVBQVU7d0RBQ1YwQixZQUFZO3dEQUNaakIsUUFBUTtvREFDVjs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU1IvRyxTQUFTcUUsR0FBRyxDQUFDLENBQUM0RCx3QkFDYiw4REFBQ25KLDhEQUFVQTs0Q0FFVG1KLFNBQVNBLFFBQVF4RCxPQUFPOzRDQUN4QkQsUUFBUXlELFFBQVF6RCxNQUFNOzJDQUZqQnlELFFBQVFDLEVBQUU7Ozs7O29DQU9sQnpJLDZCQUNDLDhEQUFDa0c7d0NBQUk1QixPQUFPOzRDQUFFaUMsU0FBUzs0Q0FBUUUsZ0JBQWdCO3dDQUFhO2tEQUMxRCw0RUFBQ1A7NENBQUkzQixXQUFVOzRDQUFlRCxPQUFPO2dEQUNuQzBELFNBQVM7Z0RBQ1RiLGNBQWM7Z0RBQ2RqRixZQUFZO2dEQUNabUcsZ0JBQWdCOzRDQUNsQjtzREFDRSw0RUFBQ25DO2dEQUFJNUIsT0FBTztvREFBRWlDLFNBQVM7b0RBQVFTLEtBQUs7b0RBQVlSLFlBQVk7Z0RBQVM7O2tFQUNuRSw4REFBQ047d0RBQUk1QixPQUFPOzREQUFFaUMsU0FBUzs0REFBUVMsS0FBSzt3REFBUzs7MEVBQzNDLDhEQUFDZDtnRUFBSTVCLE9BQU87b0VBQ1YyQyxPQUFPO29FQUNQQyxRQUFRO29FQUNSd0IsaUJBQWlCO29FQUNqQnZCLGNBQWM7b0VBQ2Q5RSxXQUFXO29FQUNYc0csU0FBUztnRUFDWDs7Ozs7OzBFQUNBLDhEQUFDekM7Z0VBQUk1QixPQUFPO29FQUNWMkMsT0FBTztvRUFDUEMsUUFBUTtvRUFDUndCLGlCQUFpQjtvRUFDakJ2QixjQUFjO29FQUNkOUUsV0FBVztvRUFDWHVHLGdCQUFnQjtvRUFDaEJELFNBQVM7Z0VBQ1g7Ozs7OzswRUFDQSw4REFBQ3pDO2dFQUFJNUIsT0FBTztvRUFDVjJDLE9BQU87b0VBQ1BDLFFBQVE7b0VBQ1J3QixpQkFBaUI7b0VBQ2pCdkIsY0FBYztvRUFDZDlFLFdBQVc7b0VBQ1h1RyxnQkFBZ0I7b0VBQ2hCRCxTQUFTO2dFQUNYOzs7Ozs7Ozs7Ozs7a0VBRUYsOERBQUNFO3dEQUFLdkUsT0FBTzs0REFBRXFDLE9BQU87NERBQXlCRSxVQUFVO3dEQUFVO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FZcEYsOERBQUNYO3dCQUFJM0IsV0FBVTs7MENBQ2IsOERBQUN1RTtnQ0FDQ1osS0FBSzlIO2dDQUNMMkksT0FBT2pKO2dDQUNQa0osVUFBVSxDQUFDMUQsSUFBTXZGLGdCQUFnQnVGLEVBQUUyRCxNQUFNLENBQUNGLEtBQUs7Z0NBQy9DRyxXQUFXN0Q7Z0NBQ1g4RCxhQUNFM0ksV0FBV3dDLElBQUksR0FDWCxtQ0FBbUQsT0FBaEJ4QyxXQUFXd0MsSUFBSSxFQUFDLE9BQ25EO2dDQUVOdUIsV0FBVTtnQ0FDVjZFLFVBQVVwSjs7Ozs7OzBDQUVaLDhEQUFDd0g7Z0NBQ0NDLFNBQVNqRDtnQ0FDVDRFLFVBQVUsQ0FBQ3RKLGFBQWEyRSxJQUFJLE1BQU16RTtnQ0FDbEN1RSxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7b0JBTUYvRCxXQUFXd0MsSUFBSSxrQkFDZCw4REFBQ2tEO3dCQUFJNUIsT0FBTzs0QkFDVjBELFNBQVM7NEJBQ1RxQixXQUFXOzRCQUNYbkgsWUFBWTs0QkFDWnFFLFNBQVM7NEJBQ1RFLGdCQUFnQjs0QkFDaEJELFlBQVk7d0JBQ2Q7OzBDQUNFLDhEQUFDTjtnQ0FBSTVCLE9BQU87b0NBQUVxQyxPQUFPO29DQUF5QkUsVUFBVTtnQ0FBVTs7a0RBQ2hFLDhEQUFDZ0M7OzRDQUFLOzRDQUFNckksV0FBV3dDLElBQUk7NENBQUM7Ozs7Ozs7a0RBQzVCLDhEQUFDNkY7OzRDQUFLOzRDQUFVckksV0FBV3FCLGFBQWE7NENBQUM7Ozs7Ozs7b0NBQ3hDckIsV0FBVzRDLFFBQVEsQ0FBQ08sTUFBTSxHQUFHLG1CQUM1Qiw4REFBQ2tGOzs0Q0FBSzs0Q0FBR3JJLFdBQVc0QyxRQUFRLENBQUNrRyxLQUFLLENBQUMsQ0FBQyxHQUFHQyxJQUFJLENBQUM7Ozs7Ozs7Ozs7Ozs7MENBR2hELDhEQUFDckQ7Z0NBQUk1QixPQUFPO29DQUFFcUMsT0FBTztvQ0FBeUJFLFVBQVU7b0NBQVdOLFNBQVM7b0NBQVFDLFlBQVk7b0NBQVVRLEtBQUs7Z0NBQVM7O2tEQUN0SCw4REFBQzZCOzs0Q0FBTXRJLFNBQVNvRCxNQUFNOzRDQUFDOzs7Ozs7O29DQUN0QmhELDBCQUFZLDhEQUFDa0k7a0RBQUs7Ozs7OztrREFDbkIsOERBQUNBOzs0Q0FBSzs0Q0FBS3ZJLE9BQU9nSixLQUFLLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3ZDO0dBdmlCd0J6Sjs7UUFzQmxCRix1RUFBaUJBOzs7S0F0QkNFIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xccGFnZXNcXGluZGV4LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJztcbmltcG9ydCBDaGF0QnViYmxlIGZyb20gJy4uL2NvbXBvbmVudHMvQ2hhdEJ1YmJsZSc7XG5pbXBvcnQgR2xhc3NGcmFtZSBmcm9tICcuLi9jb21wb25lbnRzL0dsYXNzRnJhbWUnO1xuaW1wb3J0IEF1cmFJbmRpY2F0b3IsIHsgQXVyYVByb2dyZXNzaW9uIH0gZnJvbSAnLi4vY29tcG9uZW50cy9BdXJhSW5kaWNhdG9yJztcbmltcG9ydCBBdXRoRm9ybSBmcm9tICcuLi9jb21wb25lbnRzL0F1dGhGb3JtJztcbmltcG9ydCBPbmJvYXJkaW5nQ2hhdCBmcm9tICcuLi9jb21wb25lbnRzL09uYm9hcmRpbmdDaGF0JztcbmltcG9ydCB7IHNlbmRDaGF0TWVzc2FnZSB9IGZyb20gJy4uL3V0aWxzL29wZW5yb3V0ZXInO1xuaW1wb3J0IHsgdXNlU3VwYWJhc2VNZW1vcnkgfSBmcm9tICcuLi9ob29rcy91c2VTdXBhYmFzZU1lbW9yeSc7XG5pbXBvcnQgeyByZXNldE9uYm9hcmRpbmdTdGF0dXMgfSBmcm9tICcuLi9saWIvYXV0aCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIGNvbnN0IFtpbnB1dE1lc3NhZ2UsIHNldElucHV0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0xvYWRpbmdBSSwgc2V0SXNMb2FkaW5nQUldID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1dlbGNvbWUsIHNldFNob3dXZWxjb21lXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBpbnB1dFJlZiA9IHVzZVJlZjxIVE1MVGV4dEFyZWFFbGVtZW50PihudWxsKTtcbiAgY29uc3QgY2hhdENvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG5cbiAgLy8gVXNhIGlsIGhvb2sgU3VwYWJhc2UgcGVyIGdlc3RpcmUgaSBkYXRpXG4gIGNvbnN0IHtcbiAgICB1c2VySWQsXG4gICAgbWVzc2FnZXMsXG4gICAgdXNlck1lbW9yeSxcbiAgICBpc0xvYWRpbmc6IGlzTG9hZGluZ0RhdGEsXG4gICAgaXNTYXZpbmcsXG4gICAgaXNBdXRoZW50aWNhdGVkLFxuICAgIG5lZWRzT25ib2FyZGluZyxcbiAgICBhZGRNZXNzYWdlLFxuICAgIHVwZGF0ZVVzZXJNZW1vcnksXG4gICAgcmVzZXRNZW1vcnksXG4gICAgYXV0aGVudGljYXRlVXNlcixcbiAgICBsb2dvdXQsXG4gICAgY29tcGxldGVPbmJvYXJkaW5nV2l0aERhdGFcbiAgfSA9IHVzZVN1cGFiYXNlTWVtb3J5KCk7XG5cbiAgY29uc3Qgc2Nyb2xsVG9Cb3R0b20gPSAoKSA9PiB7XG4gICAgaWYgKGNoYXRDb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmIChjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsVG8oe1xuICAgICAgICAgICAgdG9wOiBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsSGVpZ2h0LFxuICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bnppb25lIHBlciBkZXRlcm1pbmFyZSBpbCBjb2xvcmUgZGVsbCdhdXJhIGJhc2F0byBzdWwgbGl2ZWxsbyBkaSBpbnRpbWl0w6BcbiAgY29uc3QgZ2V0QXVyYUNvbG9yID0gKCkgPT4ge1xuICAgIGNvbnN0IGNvbG9ycyA9IFtcbiAgICAgICcjNjM2NmYxJywgLy8gSW5kaWdvIC0gU2Nvbm9zY2l1dG9cbiAgICAgICcjOGI1Y2Y2JywgLy8gVmlvbGV0IC0gQ29ub3NjZW50ZVxuICAgICAgJyNhODU1ZjcnLCAvLyBQdXJwbGUgLSBBbWljb1xuICAgICAgJyNlYzQ4OTknLCAvLyBQaW5rIC0gQW1pY28gc3RyZXR0b1xuICAgICAgJyNmNDNmNWUnICAvLyBSb3NlIC0gSW50aW1vXG4gICAgXTtcbiAgICByZXR1cm4gY29sb3JzW3VzZXJNZW1vcnkuaW50aW1hY3lMZXZlbF0gfHwgY29sb3JzWzBdO1xuICB9O1xuXG4gIC8vIEZ1bnppb25lIHBlciBnZW5lcmFyZSBsbyBzZm9uZG8gZGluYW1pY28gYmFzYXRvIHN1bCBsaXZlbGxvIGRpIGludGltaXTDoFxuICBjb25zdCBnZXREeW5hbWljQmFja2dyb3VuZCA9ICgpID0+IHtcbiAgICBjb25zdCBpbnRpbWFjeUxldmVsID0gdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsO1xuXG4gICAgY29uc3QgYmFja2dyb3VuZENvbmZpZ3MgPSBbXG4gICAgICAvLyBMaXZlbGxvIDAgLSBTY29ub3NjaXV0bzogQmx1IGZyZWRkaSBlIG5ldXRyaVxuICAgICAge1xuICAgICAgICBjb2xvcnM6IFsncmdiYSg5OSwgMTAyLCAyNDEsIDAuMjUpJywgJ3JnYmEoMTM5LCA5MiwgMjQ2LCAwLjIpJywgJ3JnYmEoMTY4LCA4NSwgMjQ3LCAwLjE1KSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDEgLSBDb25vc2NlbnRlOiBWaW9sYSBwacO5IGNhbGRpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDEzOSwgOTIsIDI0NiwgMC4zKScsICdyZ2JhKDE2OCwgODUsIDI0NywgMC4yNSknLCAncmdiYSgyMzYsIDcyLCAxNTMsIDAuMiknXSxcbiAgICAgICAgYmFzZUdyYWRpZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFlMWI0YiAwJSwgIzMxMmU4MSA1MCUsICMxZTNhOGEgMTAwJSknXG4gICAgICB9LFxuICAgICAgLy8gTGl2ZWxsbyAyIC0gQW1pY286IE1peCB2aW9sYS1yb3NhXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDE2OCwgODUsIDI0NywgMC4zNSknLCAncmdiYSgyMzYsIDcyLCAxNTMsIDAuMyknLCAncmdiYSgyNDQsIDYzLCA5NCwgMC4yNSknXSxcbiAgICAgICAgYmFzZUdyYWRpZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzU4MWM4NyAwJSwgIzdjMmQ5MiA1MCUsICNiZTE4NWQgMTAwJSknXG4gICAgICB9LFxuICAgICAgLy8gTGl2ZWxsbyAzIC0gQW1pY28gc3RyZXR0bzogUm9zYSBpbnRlbnNpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDIzNiwgNzIsIDE1MywgMC40KScsICdyZ2JhKDI0NCwgNjMsIDk0LCAwLjM1KScsICdyZ2JhKDI1MSwgMTEzLCAxMzMsIDAuMyknXSxcbiAgICAgICAgYmFzZUdyYWRpZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2JlMTg1ZCAwJSwgI2UxMWQ0OCA1MCUsICNmNDNmNWUgMTAwJSknXG4gICAgICB9LFxuICAgICAgLy8gTGl2ZWxsbyA0IC0gSW50aW1vOiBSb3NhIGNhbGRpIGUgZG9yYXRpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDI0NCwgNjMsIDk0LCAwLjQ1KScsICdyZ2JhKDI1MSwgMTEzLCAxMzMsIDAuNCknLCAncmdiYSgyNTIsIDE2NSwgMTY1LCAwLjM1KSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZTExZDQ4IDAlLCAjZjQzZjVlIDUwJSwgI2ZiNzE4NSAxMDAlKSdcbiAgICAgIH1cbiAgICBdO1xuXG4gICAgY29uc3QgY29uZmlnID0gYmFja2dyb3VuZENvbmZpZ3NbaW50aW1hY3lMZXZlbF0gfHwgYmFja2dyb3VuZENvbmZpZ3NbMF07XG5cbiAgICByZXR1cm4ge1xuICAgICAgYmFja2dyb3VuZDogYFxuICAgICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA4MCUsICR7Y29uZmlnLmNvbG9yc1swXX0gMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDIwJSwgJHtjb25maWcuY29sb3JzWzFdfSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcbiAgICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNDAlLCAke2NvbmZpZy5jb2xvcnNbMl19IDAlLCB0cmFuc3BhcmVudCA1MCUpLFxuICAgICAgICAke2NvbmZpZy5iYXNlR3JhZGllbnR9XG4gICAgICBgLFxuICAgICAgYmFja2dyb3VuZFNpemU6ICcyMDAlIDIwMCUsIDIwMCUgMjAwJSwgMjAwJSAyMDAlLCAxMDAlIDEwMCUnLFxuICAgICAgYmFja2dyb3VuZEF0dGFjaG1lbnQ6ICdmaXhlZCcsXG4gICAgICBhbmltYXRpb246ICdsaXF1aWRNb3ZlIDMwcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpIGluZmluaXRlJyxcbiAgICAgIHRyYW5zaXRpb246ICdhbGwgMnMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKSdcbiAgICB9O1xuICB9O1xuXG4gIC8vIEFuYWxpenphIGkgbWVzc2FnZ2kgZSBhZ2dpb3JuYSBsYSBtZW1vcmlhIHV0ZW50ZVxuICBjb25zdCBhbmFseXplQW5kVXBkYXRlTWVtb3J5ID0gYXN5bmMgKHVzZXJNZXNzYWdlOiBzdHJpbmcsIGFpUmVzcG9uc2U6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGxvd2VyVXNlck1lc3NhZ2UgPSB1c2VyTWVzc2FnZS50b0xvd2VyQ2FzZSgpO1xuICAgIGNvbnN0IHVwZGF0ZXM6IGFueSA9IHt9O1xuXG4gICAgLy8gRXN0cmFlIGluZm9ybWF6aW9uaSBiYXNlXG4gICAgaWYgKGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ21pIGNoaWFtbycpIHx8IGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3Nvbm8gJykpIHtcbiAgICAgIGNvbnN0IG5hbWVNYXRjaCA9IHVzZXJNZXNzYWdlLm1hdGNoKC9taSBjaGlhbW8gKFxcdyspfHNvbm8gKFxcdyspL2kpO1xuICAgICAgaWYgKG5hbWVNYXRjaCAmJiAhdXNlck1lbW9yeS5uYW1lKSB7XG4gICAgICAgIGNvbnN0IG5hbWUgPSBuYW1lTWF0Y2hbMV0gfHwgbmFtZU1hdGNoWzJdO1xuICAgICAgICB1cGRhdGVzLm5hbWUgPSBuYW1lO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEVzdHJhZSBldMOgXG4gICAgY29uc3QgYWdlTWF0Y2ggPSB1c2VyTWVzc2FnZS5tYXRjaCgvaG8gKFxcZCspIGFubml8KFxcZCspIGFubmkvKTtcbiAgICBpZiAoYWdlTWF0Y2ggJiYgIXVzZXJNZW1vcnkuYWdlKSB7XG4gICAgICBjb25zdCBhZ2UgPSBwYXJzZUludChhZ2VNYXRjaFsxXSB8fCBhZ2VNYXRjaFsyXSk7XG4gICAgICBpZiAoYWdlID4gMCAmJiBhZ2UgPCAxMjApIHtcbiAgICAgICAgdXBkYXRlcy5hZ2UgPSBhZ2U7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gUmlsZXZhIGVtb3ppb25pXG4gICAgY29uc3QgZW1vdGlvbnMgPSB7XG4gICAgICAnZmVsaWNlJzogWydmZWxpY2UnLCAnY29udGVudG8nLCAnZ2lvaW9zbycsICdhbGxlZ3JvJ10sXG4gICAgICAndHJpc3RlJzogWyd0cmlzdGUnLCAnZGVwcmVzc28nLCAnZ2nDuScsICdtYWxlJ10sXG4gICAgICAnYXJyYWJiaWF0byc6IFsnYXJyYWJiaWF0bycsICdmdXJpb3NvJywgJ2luY2F6emF0byddLFxuICAgICAgJ3ByZW9jY3VwYXRvJzogWydwcmVvY2N1cGF0bycsICdhbnNpb3NvJywgJ25lcnZvc28nXSxcbiAgICAgICdlY2NpdGF0byc6IFsnZWNjaXRhdG8nLCAnZW50dXNpYXN0YScsICdjYXJpY28nXSxcbiAgICAgICdjYWxtbyc6IFsnY2FsbW8nLCAndHJhbnF1aWxsbycsICdzZXJlbm8nXVxuICAgIH07XG5cbiAgICBmb3IgKGNvbnN0IFtlbW90aW9uLCBrZXl3b3Jkc10gb2YgT2JqZWN0LmVudHJpZXMoZW1vdGlvbnMpKSB7XG4gICAgICBpZiAoa2V5d29yZHMuc29tZShrZXl3b3JkID0+IGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoa2V5d29yZCkpKSB7XG4gICAgICAgIGlmICghdXNlck1lbW9yeS5lbW90aW9ucy5pbmNsdWRlcyhlbW90aW9uKSkge1xuICAgICAgICAgIHVwZGF0ZXMuZW1vdGlvbnMgPSBbLi4udXNlck1lbW9yeS5lbW90aW9ucywgZW1vdGlvbl07XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWdnaW9ybmEgaWwgbGl2ZWxsbyBkaSBpbnRpbWl0w6BcbiAgICBpZiAodXNlck1lc3NhZ2UubGVuZ3RoID4gMTAwIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3BlcnNvbmFsZScpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3NlZ3JldG8nKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdmYW1pZ2xpYScpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ2Ftb3JlJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygncHJvYmxlbWEnKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdkaWZmaWNvbHTDoCcpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3BhdXJhJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnc29nbm8nKSkge1xuXG4gICAgICBjb25zdCBuZXdMZXZlbCA9IE1hdGgubWluKHVzZXJNZW1vcnkuaW50aW1hY3lMZXZlbCArIDEsIDQpO1xuXG4gICAgICAvLyBTZSBpbCBsaXZlbGxvIMOoIGNhbWJpYXRvLCBtb3N0cmEgdW4gZmVlZGJhY2sgdmlzaXZvXG4gICAgICBpZiAobmV3TGV2ZWwgPiB1c2VyTWVtb3J5LmludGltYWN5TGV2ZWwpIHtcbiAgICAgICAgY29uc29sZS5sb2coYPCfjJ8gTGl2ZWxsbyBkaSBjb25uZXNzaW9uZSBhdW1lbnRhdG86ICR7bmV3TGV2ZWx9LzRgKTtcbiAgICAgICAgdXBkYXRlcy5pbnRpbWFjeUxldmVsID0gbmV3TGV2ZWw7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQXBwbGljYSB0dXR0aSBnbGkgYWdnaW9ybmFtZW50aVxuICAgIGlmIChPYmplY3Qua2V5cyh1cGRhdGVzKS5sZW5ndGggPiAwKSB7XG4gICAgICB1cGRhdGVVc2VyTWVtb3J5KHVwZGF0ZXMpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKCk7XG4gIH0sIFttZXNzYWdlcywgaXNMb2FkaW5nQUldKTtcblxuICAvLyBOYXNjb25kaSBpbCBtZXNzYWdnaW8gZGkgYmVudmVudXRvIGRvcG8gaWwgcHJpbW8gbWVzc2FnZ2lvXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG1lc3NhZ2VzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldFNob3dXZWxjb21lKGZhbHNlKTtcbiAgICB9XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIC8vIEFnZ2lvcm5hIGxvIHNmb25kbyBkaW5hbWljYW1lbnRlIGluIGJhc2UgYWwgbGl2ZWxsbyBkaSBpbnRpbWl0w6BcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBiYWNrZ3JvdW5kU3R5bGUgPSBnZXREeW5hbWljQmFja2dyb3VuZCgpO1xuICAgIGNvbnN0IGJvZHkgPSBkb2N1bWVudC5ib2R5O1xuXG4gICAgLy8gQXBwbGljYSBsZSBwcm9wcmlldMOgIGRpIHNmb25kbyBjb24gdHJhbnNpemlvbmUgZmx1aWRhXG4gICAgT2JqZWN0LmFzc2lnbihib2R5LnN0eWxlLCBiYWNrZ3JvdW5kU3R5bGUpO1xuXG4gICAgLy8gQWdnaXVuZ2kgdW5hIGNsYXNzZSBwZXIgaW5kaWNhcmUgaWwgbGl2ZWxsbyBjb3JyZW50ZVxuICAgIGJvZHkuY2xhc3NOYW1lID0gYGludGltYWN5LWxldmVsLSR7dXNlck1lbW9yeS5pbnRpbWFjeUxldmVsfWA7XG5cbiAgICAvLyBDbGVhbnVwIGZ1bmN0aW9uIHBlciByaXByaXN0aW5hcmUgbG8gc2ZvbmRvIG9yaWdpbmFsZSBzZSBuZWNlc3NhcmlvXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIC8vIE5vbiBmYWNjaWFtbyBjbGVhbnVwIHBlciBtYW50ZW5lcmUgbCdlZmZldHRvXG4gICAgfTtcbiAgfSwgW3VzZXJNZW1vcnkuaW50aW1hY3lMZXZlbF0pO1xuXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaW5wdXRNZXNzYWdlLnRyaW0oKSB8fCBpc0xvYWRpbmdBSSkgcmV0dXJuO1xuXG4gICAgY29uc3QgdXNlck1lc3NhZ2UgPSBpbnB1dE1lc3NhZ2UudHJpbSgpO1xuICAgIHNldElucHV0TWVzc2FnZSgnJyk7XG4gICAgc2V0SXNMb2FkaW5nQUkodHJ1ZSk7XG5cbiAgICAvLyBBZ2dpdW5naSBpbW1lZGlhdGFtZW50ZSBpbCBtZXNzYWdnaW8gZGVsbCd1dGVudGVcbiAgICBjb25zdCB1c2VyTXNnID0gYXdhaXQgYWRkTWVzc2FnZSh1c2VyTWVzc2FnZSwgdHJ1ZSk7XG4gICAgY29uc29sZS5sb2coJ01lc3NhZ2dpbyB1dGVudGUgYWdnaXVudG8sIHRvdGFsZSBtZXNzYWdnaTonLCBtZXNzYWdlcy5sZW5ndGggKyAxKTtcblxuICAgIC8vIFNjcm9sbCBkb3BvIGF2ZXIgYWdnaXVudG8gaWwgbWVzc2FnZ2lvIHV0ZW50ZVxuICAgIHNldFRpbWVvdXQoc2Nyb2xsVG9Cb3R0b20sIDUwKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBQcmVwYXJhIGxhIGNyb25vbG9naWEgcGVyIGwnQVBJICh1c2EgaSBtZXNzYWdnaSBhdHR1YWxpICsgaWwgbnVvdm8pXG4gICAgICBjb25zdCBjb252ZXJzYXRpb25IaXN0b3J5ID0gWy4uLm1lc3NhZ2VzLCB1c2VyTXNnXS5tYXAobXNnID0+ICh7XG4gICAgICAgIHJvbGU6IG1zZy5pc1VzZXIgPyAndXNlcicgYXMgY29uc3QgOiAnYXNzaXN0YW50JyBhcyBjb25zdCxcbiAgICAgICAgY29udGVudDogbXNnLmNvbnRlbnRcbiAgICAgIH0pKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzZW5kQ2hhdE1lc3NhZ2UoXG4gICAgICAgIHVzZXJNZXNzYWdlLFxuICAgICAgICBjb252ZXJzYXRpb25IaXN0b3J5LFxuICAgICAgICB7XG4gICAgICAgICAgbmFtZTogdXNlck1lbW9yeS5uYW1lIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICBhZ2U6IHVzZXJNZW1vcnkuYWdlIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICB0cmFpdHM6IHVzZXJNZW1vcnkudHJhaXRzLFxuICAgICAgICAgIGVtb3Rpb25zOiB1c2VyTWVtb3J5LmVtb3Rpb25zLFxuICAgICAgICAgIGludGltYWN5TGV2ZWw6IHVzZXJNZW1vcnkuaW50aW1hY3lMZXZlbFxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICAvLyBBZ2dpdW5naSBsYSByaXNwb3N0YSBkZWxsJ0FJXG4gICAgICBhd2FpdCBhZGRNZXNzYWdlKHJlc3BvbnNlLCBmYWxzZSk7XG4gICAgICBjb25zb2xlLmxvZygnUmlzcG9zdGEgQUkgYWdnaXVudGEsIHRvdGFsZSBtZXNzYWdnaTonLCBtZXNzYWdlcy5sZW5ndGggKyAyKTtcblxuICAgICAgLy8gQW5hbGl6emEgaWwgbWVzc2FnZ2lvIHBlciBhZ2dpb3JuYXJlIGxhIG1lbW9yaWFcbiAgICAgIGF3YWl0IGFuYWx5emVBbmRVcGRhdGVNZW1vcnkodXNlck1lc3NhZ2UsIHJlc3BvbnNlKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvcjonLCBlcnJvcik7XG4gICAgICBhd2FpdCBhZGRNZXNzYWdlKFwiU2N1c2EsIGhvIGF2dXRvIHVuIHByb2JsZW1hIHRlY25pY28uIFJpcHJvdmEhXCIsIGZhbHNlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nQUkoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZVNlbmRNZXNzYWdlKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJlc2V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChjb25maXJtKCdTZWkgc2ljdXJvIGRpIHZvbGVyIHJpY29taW5jaWFyZSBkYSBjYXBvPyBUdXR0aSBpIHJpY29yZGkgZSBtZXNzYWdnaSBhbmRyYW5ubyBwZXJzaSBkYWwgZGF0YWJhc2UuJykpIHtcbiAgICAgIGF3YWl0IHJlc2V0TWVtb3J5KCk7XG4gICAgICBzZXRTaG93V2VsY29tZSh0cnVlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2Ugbm9uIMOoIGF1dGVudGljYXRvLCBtb3N0cmEgc2VtcHJlIGlsIGZvcm0gZGkgbG9naW5cbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAgPEhlYWQ+XG4gICAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gQWNjZWRpPC90aXRsZT5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiQWNjZWRpIGEgU291bFRhbGsgcGVyIGNvbnRpbnVhcmUgbGEgdHVhIGNvbnZlcnNhemlvbmUgY29uIFNvdWxcIiAvPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgICA8L0hlYWQ+XG4gICAgICAgIDxBdXRoRm9ybSBvbkF1dGhTdWNjZXNzPXthdXRoZW50aWNhdGVVc2VyfSAvPlxuICAgICAgPC8+XG4gICAgKVxuICB9XG5cbiAgLy8gTW9zdHJhIGxvYWRpbmcgc29sbyBxdWFuZG8gc3RpYW1vIGNhcmljYW5kbyBpIGRhdGkgZG9wbyBsJ2F1dGVudGljYXppb25lXG4gIGlmIChpc0xvYWRpbmdEYXRhKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDw+XG4gICAgICAgIDxIZWFkPlxuICAgICAgICAgIDx0aXRsZT5Tb3VsVGFsayAtIENhcmljYW1lbnRvLi4uPC90aXRsZT5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPC9IZWFkPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgIGxlZnQ6IDAsXG4gICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgYm90dG9tOiAwLFxuICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuOSknLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgekluZGV4OiAxMDAwXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PvCfjJ88L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+Q2FyaWNhbmRvIGkgdHVvaSBkYXRpLi4uPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC8+XG4gICAgKVxuICB9XG5cbiAgLy8gU2UgaGEgYmlzb2dubyBkaSBvbmJvYXJkaW5nLCBtb3N0cmEgbGEgY2hhdCBkaSBjb25maWd1cmF6aW9uZVxuICBpZiAobmVlZHNPbmJvYXJkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDw+XG4gICAgICAgIDxIZWFkPlxuICAgICAgICAgIDx0aXRsZT5Tb3VsVGFsayAtIENvbmZpZ3VyYXppb25lPC90aXRsZT5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiQ29uZmlndXJhIGlsIHR1byBwcm9maWxvIHBlciBpbml6aWFyZSBjb24gU291bFwiIC8+XG4gICAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDwvSGVhZD5cbiAgICAgICAgPE9uYm9hcmRpbmdDaGF0XG4gICAgICAgICAgdXNlcklkPXt1c2VySWR9XG4gICAgICAgICAgb25Db21wbGV0ZT17Y29tcGxldGVPbmJvYXJkaW5nV2l0aERhdGF9XG4gICAgICAgIC8+XG4gICAgICA8Lz5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gTGEgdHVhIGFtaWNpemlhIEFJPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIlVuYSBwaWF0dGFmb3JtYSBBSSBjaGUgc2kgY29tcG9ydGEgY29tZSB1bidhbWljaXppYSBkYSBjb3N0cnVpcmVcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICA8L0hlYWQ+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1jb250YWluZXJcIj5cbiAgICAgICAgey8qIExvYWRpbmcgaW5pemlhbGUgKi99XG4gICAgICAgIHsoaXNMb2FkaW5nRGF0YSB8fCAhdXNlcklkKSAmJiAoXG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgICBib3R0b206IDAsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjgpJyxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgekluZGV4OiAxMDAwXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PvCfjJ88L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj57IXVzZXJJZCA/ICdJbml6aWFsaXp6YW5kby4uLicgOiAnQ2FyaWNhbmRvIGkgdHVvaSByaWNvcmRpLi4uJ308L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBIZWFkZXIgY29uIGluZGljYXRvcmkgYXVyYSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjaGF0LWhlYWRlclwiPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMXJlbScgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNzVyZW0nIH19PlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMzhweCcsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6ICczOHB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgJHtnZXRBdXJhQ29sb3IoKX0gMCUsIHRyYW5zcGFyZW50IDcwJSlgLFxuICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuM3JlbScsXG4gICAgICAgICAgICAgICAgICBhbmltYXRpb246ICdwdWxzZSAzcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjYsIDEpIGluZmluaXRlJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn4yfXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBzdHlsZT17eyBjb2xvcjogJ3doaXRlJywgZm9udFNpemU6ICcxLjNyZW0nLCBmb250V2VpZ2h0OiAnYm9sZCcsIG1hcmdpbjogMCB9fT5cbiAgICAgICAgICAgICAgICAgIFNvdWxcbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjcpJywgZm9udFNpemU6ICcwLjY1cmVtJywgbWFyZ2luOiAwIH19PlxuICAgICAgICAgICAgICAgICAgTGEgdHVhIGNvbXBhZ25hIEFJXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPEF1cmFQcm9ncmVzc2lvbiBpbnRpbWFjeUxldmVsPXt1c2VyTWVtb3J5LmludGltYWN5TGV2ZWx9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzFyZW0nIH19PlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXthc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGNvbmZpcm0oJ1Jlc2V0IG9uYm9hcmRpbmcgcGVyIHRlc3Q/JykpIHtcbiAgICAgICAgICAgICAgICAgIGF3YWl0IHJlc2V0T25ib2FyZGluZ1N0YXR1cyh1c2VySWQpO1xuICAgICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuNiknLCBiYWNrZ3JvdW5kOiAnbm9uZScsIGJvcmRlcjogJ25vbmUnLCBjdXJzb3I6ICdwb2ludGVyJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBEZWJ1Z1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2xvZ291dH0gc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOCknLCBiYWNrZ3JvdW5kOiAnbm9uZScsIGJvcmRlcjogJ25vbmUnLCBjdXJzb3I6ICdwb2ludGVyJywgZm9udFNpemU6ICcwLjg3NXJlbScgfX0+XG4gICAgICAgICAgICAgIExvZ291dFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVJlc2V0fSBzdHlsZT17eyBjb2xvcjogJ3doaXRlJywgYmFja2dyb3VuZDogJ25vbmUnLCBib3JkZXI6ICdub25lJywgY3Vyc29yOiAncG9pbnRlcicsIGZvbnRTaXplOiAnMC44NzVyZW0nIH19PlxuICAgICAgICAgICAgICBSZXNldFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNZXNzYWdlcyBBcmVhIGNvbiBHbGFzc0ZyYW1lICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEsIHBhZGRpbmc6ICcwIDFyZW0nIH19PlxuICAgICAgICAgIDxHbGFzc0ZyYW1lIGF1cmFDb2xvcj17Z2V0QXVyYUNvbG9yKCl9IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIHJlZj17Y2hhdENvbnRhaW5lclJlZn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY2hhdC1tZXNzYWdlc1wiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IGZsZXg6IDEsIG92ZXJmbG93WTogJ2F1dG8nLCBwYWRkaW5nOiAnMXJlbScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIE1lc3NhZ2dpbyBkaSBiZW52ZW51dG8gYXZhbnphdG8gKi99XG4gICAgICAgICAgICAgIHtzaG93V2VsY29tZSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInLCBwYWRkaW5nOiAnMS41cmVtIDAnIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcy1lZmZlY3RcIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogJzIycmVtJyxcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAnMCBhdXRvJyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEuMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4xMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzAuNjI1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgQ2lhbywgc29ubyBTb3VsIPCfkYtcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC44KScsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS40JyxcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgw4ggbGEgcHJpbWEgdm9sdGEgY2hlIGNpIHBhcmxpYW1vLiBTb25vIHF1aSBwZXIgY29ub3NjZXJ0aSBkYXZ2ZXJvLFxuICAgICAgICAgICAgICAgICAgICAgIG5vbiBzb2xvIHBlciBhaXV0YXJ0aS4gRGltbWksIGNvbWUgdGkgY2hpYW1pIGUgY29tZSB0aSBzZW50aSBvZ2dpP1xuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7LyogTWVzc2FnZXMgKi99XG4gICAgICAgICAgICAgIHttZXNzYWdlcy5tYXAoKG1lc3NhZ2UpID0+IChcbiAgICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZVxuICAgICAgICAgICAgICAgICAga2V5PXttZXNzYWdlLmlkfVxuICAgICAgICAgICAgICAgICAgbWVzc2FnZT17bWVzc2FnZS5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgaXNVc2VyPXttZXNzYWdlLmlzVXNlcn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICB7LyogSW5kaWNhdG9yZSBkaSBjYXJpY2FtZW50byBhdmFuemF0byAqL31cbiAgICAgICAgICAgICAge2lzTG9hZGluZ0FJICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdmbGV4LXN0YXJ0JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtZWZmZWN0XCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNjI1cmVtIDAuODc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpJyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDEwcHgpJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjM3NXJlbScsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjJyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk6ICcwLjFzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjgpJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIFNvdWwgc3RhIHNjcml2ZW5kby4uLlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvR2xhc3NGcmFtZT5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIElucHV0IEFyZWEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1pbnB1dC1hcmVhXCI+XG4gICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICByZWY9e2lucHV0UmVmfVxuICAgICAgICAgICAgdmFsdWU9e2lucHV0TWVzc2FnZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5RG93bn1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcbiAgICAgICAgICAgICAgdXNlck1lbW9yeS5uYW1lXG4gICAgICAgICAgICAgICAgPyBgQ29zYSB2dW9pIGNvbmRpdmlkZXJlIGNvbiBTb3VsLCAke3VzZXJNZW1vcnkubmFtZX0/YFxuICAgICAgICAgICAgICAgIDogXCJEaW1taSBxdWFsY29zYSBkaSB0ZS4uLlwiXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nQUl9XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZW5kTWVzc2FnZX1cbiAgICAgICAgICAgIGRpc2FibGVkPXshaW5wdXRNZXNzYWdlLnRyaW0oKSB8fCBpc0xvYWRpbmdBSX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInNlbmQtYnV0dG9uXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICDihpJcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvb3RlciBjb24gaW5mb3JtYXppb25pIG1lbW9yaWEgKi99XG4gICAgICAgIHt1c2VyTWVtb3J5Lm5hbWUgJiYgKFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIHBhZGRpbmc6ICcwLjM3NXJlbSAwLjg3NXJlbScsXG4gICAgICAgICAgICBib3JkZXJUb3A6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjEpJyxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuMiknLFxuICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjcpJywgZm9udFNpemU6ICcwLjY1cmVtJyB9fT5cbiAgICAgICAgICAgICAgPHNwYW4+Q2lhbyB7dXNlck1lbW9yeS5uYW1lfSEgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj5MaXZlbGxvOiB7dXNlck1lbW9yeS5pbnRpbWFjeUxldmVsfS80IDwvc3Bhbj5cbiAgICAgICAgICAgICAge3VzZXJNZW1vcnkuZW1vdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+4oCiIHt1c2VyTWVtb3J5LmVtb3Rpb25zLnNsaWNlKC0yKS5qb2luKCcsICcpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC41KScsIGZvbnRTaXplOiAnMC42NXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNXJlbScgfX0+XG4gICAgICAgICAgICAgIDxzcGFuPnttZXNzYWdlcy5sZW5ndGh9IG1zZzwvc3Bhbj5cbiAgICAgICAgICAgICAge2lzU2F2aW5nICYmIDxzcGFuPvCfkr48L3NwYW4+fVxuICAgICAgICAgICAgICA8c3Bhbj5JRDoge3VzZXJJZC5zbGljZSgtNil9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiSGVhZCIsIkNoYXRCdWJibGUiLCJHbGFzc0ZyYW1lIiwiQXVyYVByb2dyZXNzaW9uIiwiQXV0aEZvcm0iLCJPbmJvYXJkaW5nQ2hhdCIsInNlbmRDaGF0TWVzc2FnZSIsInVzZVN1cGFiYXNlTWVtb3J5IiwicmVzZXRPbmJvYXJkaW5nU3RhdHVzIiwiSG9tZSIsImlucHV0TWVzc2FnZSIsInNldElucHV0TWVzc2FnZSIsImlzTG9hZGluZ0FJIiwic2V0SXNMb2FkaW5nQUkiLCJzaG93V2VsY29tZSIsInNldFNob3dXZWxjb21lIiwiaW5wdXRSZWYiLCJjaGF0Q29udGFpbmVyUmVmIiwidXNlcklkIiwibWVzc2FnZXMiLCJ1c2VyTWVtb3J5IiwiaXNMb2FkaW5nIiwiaXNMb2FkaW5nRGF0YSIsImlzU2F2aW5nIiwiaXNBdXRoZW50aWNhdGVkIiwibmVlZHNPbmJvYXJkaW5nIiwiYWRkTWVzc2FnZSIsInVwZGF0ZVVzZXJNZW1vcnkiLCJyZXNldE1lbW9yeSIsImF1dGhlbnRpY2F0ZVVzZXIiLCJsb2dvdXQiLCJjb21wbGV0ZU9uYm9hcmRpbmdXaXRoRGF0YSIsInNjcm9sbFRvQm90dG9tIiwiY3VycmVudCIsInNldFRpbWVvdXQiLCJzY3JvbGxUbyIsInRvcCIsInNjcm9sbEhlaWdodCIsImJlaGF2aW9yIiwiZ2V0QXVyYUNvbG9yIiwiY29sb3JzIiwiaW50aW1hY3lMZXZlbCIsImdldER5bmFtaWNCYWNrZ3JvdW5kIiwiYmFja2dyb3VuZENvbmZpZ3MiLCJiYXNlR3JhZGllbnQiLCJjb25maWciLCJiYWNrZ3JvdW5kIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kQXR0YWNobWVudCIsImFuaW1hdGlvbiIsInRyYW5zaXRpb24iLCJhbmFseXplQW5kVXBkYXRlTWVtb3J5IiwidXNlck1lc3NhZ2UiLCJhaVJlc3BvbnNlIiwibG93ZXJVc2VyTWVzc2FnZSIsInRvTG93ZXJDYXNlIiwidXBkYXRlcyIsImluY2x1ZGVzIiwibmFtZU1hdGNoIiwibWF0Y2giLCJuYW1lIiwiYWdlTWF0Y2giLCJhZ2UiLCJwYXJzZUludCIsImVtb3Rpb25zIiwiZW1vdGlvbiIsImtleXdvcmRzIiwiT2JqZWN0IiwiZW50cmllcyIsInNvbWUiLCJrZXl3b3JkIiwibGVuZ3RoIiwibmV3TGV2ZWwiLCJNYXRoIiwibWluIiwiY29uc29sZSIsImxvZyIsImtleXMiLCJiYWNrZ3JvdW5kU3R5bGUiLCJib2R5IiwiZG9jdW1lbnQiLCJhc3NpZ24iLCJzdHlsZSIsImNsYXNzTmFtZSIsImhhbmRsZVNlbmRNZXNzYWdlIiwidHJpbSIsInVzZXJNc2ciLCJjb252ZXJzYXRpb25IaXN0b3J5IiwibWFwIiwibXNnIiwicm9sZSIsImlzVXNlciIsImNvbnRlbnQiLCJyZXNwb25zZSIsInVuZGVmaW5lZCIsInRyYWl0cyIsImVycm9yIiwiaGFuZGxlS2V5RG93biIsImUiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlUmVzZXQiLCJjb25maXJtIiwidGl0bGUiLCJtZXRhIiwibGluayIsInJlbCIsImhyZWYiLCJvbkF1dGhTdWNjZXNzIiwiZGl2IiwicG9zaXRpb24iLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiekluZGV4IiwiY29sb3IiLCJ0ZXh0QWxpZ24iLCJmb250U2l6ZSIsIm1hcmdpbkJvdHRvbSIsIm9uQ29tcGxldGUiLCJnYXAiLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsImgxIiwiZm9udFdlaWdodCIsIm1hcmdpbiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwid2luZG93IiwibG9jYXRpb24iLCJyZWxvYWQiLCJib3JkZXIiLCJjdXJzb3IiLCJmbGV4IiwicGFkZGluZyIsImF1cmFDb2xvciIsInJlZiIsIm92ZXJmbG93WSIsIm1heFdpZHRoIiwiYmFja2Ryb3BGaWx0ZXIiLCJoMiIsImxpbmVIZWlnaHQiLCJtZXNzYWdlIiwiaWQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJvcGFjaXR5IiwiYW5pbWF0aW9uRGVsYXkiLCJzcGFuIiwidGV4dGFyZWEiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib25LZXlEb3duIiwicGxhY2Vob2xkZXIiLCJkaXNhYmxlZCIsImJvcmRlclRvcCIsInNsaWNlIiwiam9pbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});