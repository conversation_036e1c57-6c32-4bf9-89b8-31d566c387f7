"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio. Come ti chiami?\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri\";\n                if (input.trim().length > 50) return \"Il nome è troppo lungo\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"Che bel nome, \".concat(name, \"! Mi piace molto. \\uD83D\\uDE0A\"),\n            waitForInput: false\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni.\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk\";\n                if (age > 120) return \"Inserisci un'età realistica\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>\"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei in un'et\\xe0 interessante. Ho tante cose da condividere con te! ✨\"),\n            waitForInput: false\n        },\n        {\n            message: \"Ora che ci conosciamo meglio, possiamo iniziare la nostra conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Sei pronto/a?\",\n            waitForInput: false,\n            isLast: true\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        if (typeof step.message === 'function') {\n            const message = step.message(userData.name, userData.age);\n            addMessage(message, false);\n        } else {\n            addMessage(step.message, false);\n        }\n        if (step.isLast) {\n            // Completa l'onboarding\n            setTimeout(async ()=>{\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.completeOnboarding)(userId);\n                onComplete(userData);\n            }, 2000);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizia l'onboarding solo al primo step\n            if (currentStep === 0) {\n                setTimeout({\n                    \"OnboardingChat.useEffect\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect\"], 1000);\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '1.25rem',\n                            fontWeight: '600',\n                            margin: 0,\n                            marginBottom: '0.5rem'\n                        },\n                        children: \"Configurazione Iniziale\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.7)',\n                            fontSize: '0.875rem',\n                            margin: 0\n                        },\n                        children: \"Soul vuole conoscerti meglio\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        message: message.content,\n                        isUser: message.isUser\n                    }, message.id, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)',\n                    display: 'flex',\n                    gap: '0.5rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: inputMessage,\n                        onChange: (e)=>setInputMessage(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: \"Scrivi la tua risposta...\",\n                        style: {\n                            flex: 1,\n                            padding: '0.75rem',\n                            borderRadius: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.3)',\n                            background: 'rgba(255,255,255,0.1)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            outline: 'none'\n                        },\n                        autoFocus: true\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleUserInput,\n                        disabled: !inputMessage.trim(),\n                        style: {\n                            padding: '0.75rem 1.5rem',\n                            borderRadius: '0.5rem',\n                            border: 'none',\n                            background: inputMessage.trim() ? '#6366f1' : 'rgba(99, 102, 241, 0.5)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            cursor: inputMessage.trim() ? 'pointer' : 'not-allowed'\n                        },\n                        children: \"→\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"BgYmd8936T+AALXVVemgbWuUn0U=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09uYm9hcmRpbmdDaGF0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBMkQ7QUFDckI7QUFDVztBQWVqRCxNQUFNTSxpQkFBZ0Q7UUFBQyxFQUFFQyxNQUFNLEVBQUVDLFVBQVUsRUFBRTs7SUFDM0UsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdULCtDQUFRQSxDQUFzQixFQUFFO0lBQ2hFLE1BQU0sQ0FBQ1UsYUFBYUMsZUFBZSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNZLGNBQWNDLGdCQUFnQixHQUFHYiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNjLFdBQVdDLGFBQWEsR0FBR2YsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQUM7UUFBRWtCLE1BQU07UUFBSUMsS0FBSztJQUFFO0lBQzVELE1BQU1DLG1CQUFtQmxCLDZDQUFNQSxDQUFpQjtJQUVoRCxNQUFNbUIsa0JBQWtCO1FBQ3RCO1lBQ0VDLFNBQVM7WUFDVEMsY0FBYztRQUNoQjtRQUNBO1lBQ0VELFNBQVM7WUFDVEMsY0FBYztZQUNkQyxXQUFXLENBQUNDO2dCQUNWLElBQUlBLE1BQU1DLElBQUksR0FBR0MsTUFBTSxHQUFHLEdBQUcsT0FBTztnQkFDcEMsSUFBSUYsTUFBTUMsSUFBSSxHQUFHQyxNQUFNLEdBQUcsSUFBSSxPQUFPO2dCQUNyQyxPQUFPO1lBQ1Q7WUFDQUMsV0FBVyxDQUFDSDtnQkFDVlIsWUFBWVksQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFWCxNQUFNTyxNQUFNQyxJQUFJO29CQUFHO1lBQ3JEO1FBQ0Y7UUFDQTtZQUNFSixTQUFTLENBQUNKLE9BQWlCLGlCQUFzQixPQUFMQSxNQUFLO1lBQ2pESyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRUQsU0FBUztZQUNUQyxjQUFjO1lBQ2RDLFdBQVcsQ0FBQ0M7Z0JBQ1YsTUFBTU4sTUFBTVcsU0FBU0w7Z0JBQ3JCLElBQUlNLE1BQU1aLE1BQU0sT0FBTztnQkFDdkIsSUFBSUEsTUFBTSxJQUFJLE9BQU87Z0JBQ3JCLElBQUlBLE1BQU0sS0FBSyxPQUFPO2dCQUN0QixPQUFPO1lBQ1Q7WUFDQVMsV0FBVyxDQUFDSDtnQkFDVlIsWUFBWVksQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFVixLQUFLVyxTQUFTTDtvQkFBTztZQUN2RDtRQUNGO1FBQ0E7WUFDRUgsU0FBUyxDQUFDSixNQUFjQyxNQUN0QixZQUFxQkEsT0FBVEQsTUFBSyxNQUFRLE9BQUpDLEtBQUk7WUFDM0JJLGNBQWM7UUFDaEI7UUFDQTtZQUNFRCxTQUFTO1lBQ1RDLGNBQWM7WUFDZFMsUUFBUTtRQUNWO0tBQ0Q7SUFFRCxNQUFNQyxpQkFBaUI7UUFDckIsSUFBSWIsaUJBQWlCYyxPQUFPLEVBQUU7WUFDNUJDLFdBQVc7Z0JBQ1QsSUFBSWYsaUJBQWlCYyxPQUFPLEVBQUU7b0JBQzVCZCxpQkFBaUJjLE9BQU8sQ0FBQ0UsUUFBUSxDQUFDO3dCQUNoQ0MsS0FBS2pCLGlCQUFpQmMsT0FBTyxDQUFDSSxZQUFZO3dCQUMxQ0MsVUFBVTtvQkFDWjtnQkFDRjtZQUNGLEdBQUc7UUFDTDtJQUNGO0lBRUEsTUFBTUMsYUFBYSxDQUFDQyxTQUFpQkM7UUFDbkMsTUFBTUMsYUFBZ0M7WUFDcENDLElBQUksT0FBcUJDLE9BQWRDLEtBQUtDLEdBQUcsSUFBRyxLQUEyQyxPQUF4Q0YsS0FBS0csTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7WUFDOURUO1lBQ0FDO1lBQ0FTLFdBQVcsSUFBSUw7UUFDakI7UUFDQXJDLFlBQVlvQixDQUFBQSxPQUFRO21CQUFJQTtnQkFBTWM7YUFBVztRQUN6Q1IsV0FBV0YsZ0JBQWdCO0lBQzdCO0lBRUEsTUFBTW1CLGNBQWM7UUFDbEIsTUFBTUMsT0FBT2hDLGVBQWUsQ0FBQ1gsWUFBWTtRQUV6QyxJQUFJLE9BQU8yQyxLQUFLL0IsT0FBTyxLQUFLLFlBQVk7WUFDdEMsTUFBTUEsVUFBVStCLEtBQUsvQixPQUFPLENBQUNOLFNBQVNFLElBQUksRUFBRUYsU0FBU0csR0FBRztZQUN4RHFCLFdBQVdsQixTQUFTO1FBQ3RCLE9BQU87WUFDTGtCLFdBQVdhLEtBQUsvQixPQUFPLEVBQUU7UUFDM0I7UUFFQSxJQUFJK0IsS0FBS3JCLE1BQU0sRUFBRTtZQUNmLHdCQUF3QjtZQUN4QkcsV0FBVztnQkFDVCxNQUFNL0IsNkRBQWtCQSxDQUFDRTtnQkFDekJDLFdBQVdTO1lBQ2IsR0FBRztZQUNIO1FBQ0Y7UUFFQSxJQUFJLENBQUNxQyxLQUFLOUIsWUFBWSxFQUFFO1lBQ3RCWSxXQUFXO2dCQUNUeEIsZUFBZWtCLENBQUFBLE9BQVFBLE9BQU87WUFDaEMsR0FBRztRQUNMLE9BQU87WUFDTGQsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNdUMsa0JBQWtCO1FBQ3RCLElBQUksQ0FBQzFDLGFBQWFjLElBQUksTUFBTSxDQUFDWixXQUFXO1FBRXhDLE1BQU11QyxPQUFPaEMsZUFBZSxDQUFDWCxZQUFZO1FBRXpDLG9CQUFvQjtRQUNwQixJQUFJMkMsS0FBSzdCLFNBQVMsRUFBRTtZQUNsQixNQUFNK0IsUUFBUUYsS0FBSzdCLFNBQVMsQ0FBQ1osYUFBYWMsSUFBSTtZQUM5QyxJQUFJNkIsT0FBTztnQkFDVGYsV0FBV2UsT0FBTztnQkFDbEI7WUFDRjtRQUNGO1FBRUEsNEJBQTRCO1FBQzVCZixXQUFXNUIsYUFBYWMsSUFBSSxJQUFJO1FBRWhDLG1CQUFtQjtRQUNuQixJQUFJMkIsS0FBS3pCLFNBQVMsRUFBRTtZQUNsQnlCLEtBQUt6QixTQUFTLENBQUNoQixhQUFhYyxJQUFJO1FBQ2xDO1FBRUFiLGdCQUFnQjtRQUNoQkUsYUFBYTtRQUViLHVCQUF1QjtRQUN2Qm9CLFdBQVc7WUFDVHhCLGVBQWVrQixDQUFBQSxPQUFRQSxPQUFPO1FBQ2hDLEdBQUc7SUFDTDtJQUVBLE1BQU0yQixnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCTjtRQUNGO0lBQ0Y7SUFFQXJELGdEQUFTQTtvQ0FBQztZQUNSLHlDQUF5QztZQUN6QyxJQUFJUyxnQkFBZ0IsR0FBRztnQkFDckJ5QjtnREFBVzt3QkFDVGlCO29CQUNGOytDQUFHO1lBQ0w7UUFDRjttQ0FBRyxFQUFFO0lBRUxuRCxnREFBU0E7b0NBQUM7WUFDUixJQUFJUyxjQUFjLEtBQUtBLGNBQWNXLGdCQUFnQk0sTUFBTSxFQUFFO2dCQUMzRHlCO1lBQ0Y7UUFDRjttQ0FBRztRQUFDMUM7S0FBWTtJQUVoQlQsZ0RBQVNBO29DQUFDO1lBQ1JnQztRQUNGO21DQUFHO1FBQUN6QjtLQUFTO0lBRWIscUJBQ0UsOERBQUNxRDtRQUFJQyxPQUFPO1lBQ1ZDLFVBQVU7WUFDVjFCLEtBQUs7WUFDTDJCLE1BQU07WUFDTkMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWkMsU0FBUztZQUNUQyxlQUFlO1lBQ2ZDLFFBQVE7UUFDVjs7MEJBRUUsOERBQUNUO2dCQUFJQyxPQUFPO29CQUNWUyxTQUFTO29CQUNUQyxjQUFjO29CQUNkTCxZQUFZO29CQUNaTSxXQUFXO2dCQUNiOztrQ0FDRSw4REFBQ0M7d0JBQUdaLE9BQU87NEJBQ1RhLE9BQU87NEJBQ1BDLFVBQVU7NEJBQ1ZDLFlBQVk7NEJBQ1pDLFFBQVE7NEJBQ1JDLGNBQWM7d0JBQ2hCO2tDQUFHOzs7Ozs7a0NBR0gsOERBQUNDO3dCQUFFbEIsT0FBTzs0QkFDUmEsT0FBTzs0QkFDUEMsVUFBVTs0QkFDVkUsUUFBUTt3QkFDVjtrQ0FBRzs7Ozs7Ozs7Ozs7OzBCQU1MLDhEQUFDakI7Z0JBQ0NvQixLQUFLN0Q7Z0JBQ0wwQyxPQUFPO29CQUNMb0IsTUFBTTtvQkFDTkMsV0FBVztvQkFDWFosU0FBUztvQkFDVEgsU0FBUztvQkFDVEMsZUFBZTtvQkFDZmUsS0FBSztnQkFDUDswQkFFQzVFLFNBQVM2RSxHQUFHLENBQUMsQ0FBQy9ELHdCQUNiLDhEQUFDbkIsbURBQVVBO3dCQUVUbUIsU0FBU0EsUUFBUW1CLE9BQU87d0JBQ3hCQyxRQUFRcEIsUUFBUW9CLE1BQU07dUJBRmpCcEIsUUFBUXNCLEVBQUU7Ozs7Ozs7Ozs7WUFRcEI5QiwyQkFDQyw4REFBQytDO2dCQUFJQyxPQUFPO29CQUNWUyxTQUFTO29CQUNUZSxXQUFXO29CQUNYbkIsWUFBWTtvQkFDWkMsU0FBUztvQkFDVGdCLEtBQUs7Z0JBQ1A7O2tDQUNFLDhEQUFDM0Q7d0JBQ0M4RCxNQUFLO3dCQUNMQyxPQUFPNUU7d0JBQ1A2RSxVQUFVLENBQUNoQyxJQUFNNUMsZ0JBQWdCNEMsRUFBRWlDLE1BQU0sQ0FBQ0YsS0FBSzt3QkFDL0NHLFdBQVduQzt3QkFDWG9DLGFBQVk7d0JBQ1o5QixPQUFPOzRCQUNMb0IsTUFBTTs0QkFDTlgsU0FBUzs0QkFDVHNCLGNBQWM7NEJBQ2RDLFFBQVE7NEJBQ1IzQixZQUFZOzRCQUNaUSxPQUFPOzRCQUNQQyxVQUFVOzRCQUNWbUIsU0FBUzt3QkFDWDt3QkFDQUMsU0FBUzs7Ozs7O2tDQUVYLDhEQUFDQzt3QkFDQ0MsU0FBUzVDO3dCQUNUNkMsVUFBVSxDQUFDdkYsYUFBYWMsSUFBSTt3QkFDNUJvQyxPQUFPOzRCQUNMUyxTQUFTOzRCQUNUc0IsY0FBYzs0QkFDZEMsUUFBUTs0QkFDUjNCLFlBQVl2RCxhQUFhYyxJQUFJLEtBQUssWUFBWTs0QkFDOUNpRCxPQUFPOzRCQUNQQyxVQUFVOzRCQUNWd0IsUUFBUXhGLGFBQWFjLElBQUksS0FBSyxZQUFZO3dCQUM1QztrQ0FDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT1g7R0E1UU1yQjtLQUFBQTtBQThRTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXGNvbXBvbmVudHNcXE9uYm9hcmRpbmdDaGF0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IENoYXRCdWJibGUgZnJvbSAnLi9DaGF0QnViYmxlJztcbmltcG9ydCB7IGNvbXBsZXRlT25ib2FyZGluZyB9IGZyb20gJy4uL2xpYi9hdXRoJztcblxuaW50ZXJmYWNlIE9uYm9hcmRpbmdDaGF0UHJvcHMge1xuICB1c2VySWQ6IHN0cmluZztcbiAgb25Db21wbGV0ZTogKHVzZXJEYXRhOiB7IG5hbWU6IHN0cmluZzsgYWdlOiBudW1iZXIgfSkgPT4gdm9pZDtcbn1cblxuaW50ZXJmYWNlIE9uYm9hcmRpbmdNZXNzYWdlIHtcbiAgaWQ6IHN0cmluZztcbiAgY29udGVudDogc3RyaW5nO1xuICBpc1VzZXI6IGJvb2xlYW47XG4gIHRpbWVzdGFtcDogRGF0ZTtcbiAgaXNUeXBpbmc/OiBib29sZWFuO1xufVxuXG5jb25zdCBPbmJvYXJkaW5nQ2hhdDogUmVhY3QuRkM8T25ib2FyZGluZ0NoYXRQcm9wcz4gPSAoeyB1c2VySWQsIG9uQ29tcGxldGUgfSkgPT4ge1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE9uYm9hcmRpbmdNZXNzYWdlW10+KFtdKTtcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2lucHV0TWVzc2FnZSwgc2V0SW5wdXRNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzV2FpdGluZywgc2V0SXNXYWl0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3VzZXJEYXRhLCBzZXRVc2VyRGF0YV0gPSB1c2VTdGF0ZSh7IG5hbWU6ICcnLCBhZ2U6IDAgfSk7XG4gIGNvbnN0IGNoYXRDb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIGNvbnN0IG9uYm9hcmRpbmdTdGVwcyA9IFtcbiAgICB7XG4gICAgICBtZXNzYWdlOiBcIkNpYW8hIFNvbm8gU291bCwgbGEgdHVhIGNvbXBhZ25hIEFJLiDDiCB1biBwaWFjZXJlIGNvbm9zY2VydGkhIPCfjJ9cIixcbiAgICAgIHdhaXRGb3JJbnB1dDogZmFsc2VcbiAgICB9LFxuICAgIHtcbiAgICAgIG1lc3NhZ2U6IFwiUHJpbWEgZGkgaW5pemlhcmUgbGEgbm9zdHJhIGF2dmVudHVyYSBpbnNpZW1lLCB2b3JyZWkgY29ub3NjZXJ0aSBtZWdsaW8uIENvbWUgdGkgY2hpYW1pP1wiLFxuICAgICAgd2FpdEZvcklucHV0OiB0cnVlLFxuICAgICAgdmFsaWRhdG9yOiAoaW5wdXQ6IHN0cmluZykgPT4ge1xuICAgICAgICBpZiAoaW5wdXQudHJpbSgpLmxlbmd0aCA8IDIpIHJldHVybiBcIklsIG5vbWUgZGV2ZSBlc3NlcmUgZGkgYWxtZW5vIDIgY2FyYXR0ZXJpXCI7XG4gICAgICAgIGlmIChpbnB1dC50cmltKCkubGVuZ3RoID4gNTApIHJldHVybiBcIklsIG5vbWUgw6ggdHJvcHBvIGx1bmdvXCI7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfSxcbiAgICAgIHByb2Nlc3NvcjogKGlucHV0OiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0VXNlckRhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBpbnB1dC50cmltKCkgfSkpO1xuICAgICAgfVxuICAgIH0sXG4gICAge1xuICAgICAgbWVzc2FnZTogKG5hbWU6IHN0cmluZykgPT4gYENoZSBiZWwgbm9tZSwgJHtuYW1lfSEgTWkgcGlhY2UgbW9sdG8uIPCfmIpgLFxuICAgICAgd2FpdEZvcklucHV0OiBmYWxzZVxuICAgIH0sXG4gICAge1xuICAgICAgbWVzc2FnZTogXCJPcmEgZGltbWksIHF1YW50aSBhbm5pIGhhaT8gUXVlc3RvIG1pIGFpdXRlcsOgIGEgY2FwaXJ0aSBtZWdsaW8gZSBhZCBhZGF0dGFyZSBsZSBub3N0cmUgY29udmVyc2F6aW9uaS5cIixcbiAgICAgIHdhaXRGb3JJbnB1dDogdHJ1ZSxcbiAgICAgIHZhbGlkYXRvcjogKGlucHV0OiBzdHJpbmcpID0+IHtcbiAgICAgICAgY29uc3QgYWdlID0gcGFyc2VJbnQoaW5wdXQpO1xuICAgICAgICBpZiAoaXNOYU4oYWdlKSkgcmV0dXJuIFwiUGVyIGZhdm9yZSBpbnNlcmlzY2kgdW4gbnVtZXJvIHZhbGlkb1wiO1xuICAgICAgICBpZiAoYWdlIDwgMTMpIHJldHVybiBcIkRldmkgYXZlcmUgYWxtZW5vIDEzIGFubmkgcGVyIHVzYXJlIFNvdWxUYWxrXCI7XG4gICAgICAgIGlmIChhZ2UgPiAxMjApIHJldHVybiBcIkluc2VyaXNjaSB1bidldMOgIHJlYWxpc3RpY2FcIjtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9LFxuICAgICAgcHJvY2Vzc29yOiAoaW5wdXQ6IHN0cmluZykgPT4ge1xuICAgICAgICBzZXRVc2VyRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGFnZTogcGFyc2VJbnQoaW5wdXQpIH0pKTtcbiAgICAgIH1cbiAgICB9LFxuICAgIHtcbiAgICAgIG1lc3NhZ2U6IChuYW1lOiBzdHJpbmcsIGFnZTogbnVtYmVyKSA9PiBcbiAgICAgICAgYFBlcmZldHRvICR7bmFtZX0hICR7YWdlfSBhbm5pLi4uIHNlaSBpbiB1bidldMOgIGludGVyZXNzYW50ZS4gSG8gdGFudGUgY29zZSBkYSBjb25kaXZpZGVyZSBjb24gdGUhIOKcqGAsXG4gICAgICB3YWl0Rm9ySW5wdXQ6IGZhbHNlXG4gICAgfSxcbiAgICB7XG4gICAgICBtZXNzYWdlOiBcIk9yYSBjaGUgY2kgY29ub3NjaWFtbyBtZWdsaW8sIHBvc3NpYW1vIGluaXppYXJlIGxhIG5vc3RyYSBjb252ZXJzYXppb25lLiBSaWNvcmRlcsOyIHR1dHRvIHF1ZWxsbyBjaGUgbWkgZGlyYWkgZSBjcmVzY2Vyw7IgaW5zaWVtZSBhIHRlLiBTZWkgcHJvbnRvL2E/XCIsXG4gICAgICB3YWl0Rm9ySW5wdXQ6IGZhbHNlLFxuICAgICAgaXNMYXN0OiB0cnVlXG4gICAgfVxuICBdO1xuXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tID0gKCkgPT4ge1xuICAgIGlmIChjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQpIHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBpZiAoY2hhdENvbnRhaW5lclJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgY2hhdENvbnRhaW5lclJlZi5jdXJyZW50LnNjcm9sbFRvKHtcbiAgICAgICAgICAgIHRvcDogY2hhdENvbnRhaW5lclJlZi5jdXJyZW50LnNjcm9sbEhlaWdodCxcbiAgICAgICAgICAgIGJlaGF2aW9yOiAnc21vb3RoJ1xuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9LCAxMDApO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBhZGRNZXNzYWdlID0gKGNvbnRlbnQ6IHN0cmluZywgaXNVc2VyOiBib29sZWFuKSA9PiB7XG4gICAgY29uc3QgbmV3TWVzc2FnZTogT25ib2FyZGluZ01lc3NhZ2UgPSB7XG4gICAgICBpZDogYG9uYl8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWAsXG4gICAgICBjb250ZW50LFxuICAgICAgaXNVc2VyLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpXG4gICAgfTtcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBuZXdNZXNzYWdlXSk7XG4gICAgc2V0VGltZW91dChzY3JvbGxUb0JvdHRvbSwgNTApO1xuICB9O1xuXG4gIGNvbnN0IHByb2Nlc3NTdGVwID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHN0ZXAgPSBvbmJvYXJkaW5nU3RlcHNbY3VycmVudFN0ZXBdO1xuICAgIFxuICAgIGlmICh0eXBlb2Ygc3RlcC5tZXNzYWdlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBjb25zdCBtZXNzYWdlID0gc3RlcC5tZXNzYWdlKHVzZXJEYXRhLm5hbWUsIHVzZXJEYXRhLmFnZSk7XG4gICAgICBhZGRNZXNzYWdlKG1lc3NhZ2UsIGZhbHNlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgYWRkTWVzc2FnZShzdGVwLm1lc3NhZ2UsIGZhbHNlKTtcbiAgICB9XG5cbiAgICBpZiAoc3RlcC5pc0xhc3QpIHtcbiAgICAgIC8vIENvbXBsZXRhIGwnb25ib2FyZGluZ1xuICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7XG4gICAgICAgIGF3YWl0IGNvbXBsZXRlT25ib2FyZGluZyh1c2VySWQpO1xuICAgICAgICBvbkNvbXBsZXRlKHVzZXJEYXRhKTtcbiAgICAgIH0sIDIwMDApO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmICghc3RlcC53YWl0Rm9ySW5wdXQpIHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXRDdXJyZW50U3RlcChwcmV2ID0+IHByZXYgKyAxKTtcbiAgICAgIH0sIDIwMDApO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRJc1dhaXRpbmcodHJ1ZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVVzZXJJbnB1dCA9ICgpID0+IHtcbiAgICBpZiAoIWlucHV0TWVzc2FnZS50cmltKCkgfHwgIWlzV2FpdGluZykgcmV0dXJuO1xuXG4gICAgY29uc3Qgc3RlcCA9IG9uYm9hcmRpbmdTdGVwc1tjdXJyZW50U3RlcF07XG4gICAgXG4gICAgLy8gVmFsaWRhemlvbmUgaW5wdXRcbiAgICBpZiAoc3RlcC52YWxpZGF0b3IpIHtcbiAgICAgIGNvbnN0IGVycm9yID0gc3RlcC52YWxpZGF0b3IoaW5wdXRNZXNzYWdlLnRyaW0oKSk7XG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgYWRkTWVzc2FnZShlcnJvciwgZmFsc2UpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWdnaXVuZ2kgbWVzc2FnZ2lvIHV0ZW50ZVxuICAgIGFkZE1lc3NhZ2UoaW5wdXRNZXNzYWdlLnRyaW0oKSwgdHJ1ZSk7XG4gICAgXG4gICAgLy8gUHJvY2Vzc2EgbCdpbnB1dFxuICAgIGlmIChzdGVwLnByb2Nlc3Nvcikge1xuICAgICAgc3RlcC5wcm9jZXNzb3IoaW5wdXRNZXNzYWdlLnRyaW0oKSk7XG4gICAgfVxuXG4gICAgc2V0SW5wdXRNZXNzYWdlKCcnKTtcbiAgICBzZXRJc1dhaXRpbmcoZmFsc2UpO1xuICAgIFxuICAgIC8vIFZhaSBhbCBwcm9zc2ltbyBzdGVwXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzZXRDdXJyZW50U3RlcChwcmV2ID0+IHByZXYgKyAxKTtcbiAgICB9LCAxMDAwKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZVVzZXJJbnB1dCgpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEluaXppYSBsJ29uYm9hcmRpbmcgc29sbyBhbCBwcmltbyBzdGVwXG4gICAgaWYgKGN1cnJlbnRTdGVwID09PSAwKSB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgcHJvY2Vzc1N0ZXAoKTtcbiAgICAgIH0sIDEwMDApO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRTdGVwID4gMCAmJiBjdXJyZW50U3RlcCA8IG9uYm9hcmRpbmdTdGVwcy5sZW5ndGgpIHtcbiAgICAgIHByb2Nlc3NTdGVwKCk7XG4gICAgfVxuICB9LCBbY3VycmVudFN0ZXBdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKCk7XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17e1xuICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICB0b3A6IDAsXG4gICAgICBsZWZ0OiAwLFxuICAgICAgcmlnaHQ6IDAsXG4gICAgICBib3R0b206IDAsXG4gICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjk1KScsXG4gICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICAgIHpJbmRleDogMTUwMFxuICAgIH19PlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgcGFkZGluZzogJzFyZW0nLFxuICAgICAgICBib3JkZXJCb3R0b206ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjEpJyxcbiAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwwLDAsMC4zKScsXG4gICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgIH19PlxuICAgICAgICA8aDIgc3R5bGU9e3sgXG4gICAgICAgICAgY29sb3I6ICd3aGl0ZScsIFxuICAgICAgICAgIGZvbnRTaXplOiAnMS4yNXJlbScsIFxuICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLCBcbiAgICAgICAgICBtYXJnaW46IDAsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiAnMC41cmVtJ1xuICAgICAgICB9fT5cbiAgICAgICAgICBDb25maWd1cmF6aW9uZSBJbml6aWFsZVxuICAgICAgICA8L2gyPlxuICAgICAgICA8cCBzdHlsZT17eyBcbiAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC43KScsIFxuICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLCBcbiAgICAgICAgICBtYXJnaW46IDAgXG4gICAgICAgIH19PlxuICAgICAgICAgIFNvdWwgdnVvbGUgY29ub3NjZXJ0aSBtZWdsaW9cbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNZXNzYWdlcyAqL31cbiAgICAgIDxkaXYgXG4gICAgICAgIHJlZj17Y2hhdENvbnRhaW5lclJlZn1cbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICBmbGV4OiAxLFxuICAgICAgICAgIG92ZXJmbG93WTogJ2F1dG8nLFxuICAgICAgICAgIHBhZGRpbmc6ICcxcmVtJyxcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICAgICAgZ2FwOiAnMXJlbSdcbiAgICAgICAgfX1cbiAgICAgID5cbiAgICAgICAge21lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxuICAgICAgICAgIDxDaGF0QnViYmxlXG4gICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XG4gICAgICAgICAgICBtZXNzYWdlPXttZXNzYWdlLmNvbnRlbnR9XG4gICAgICAgICAgICBpc1VzZXI9e21lc3NhZ2UuaXNVc2VyfVxuICAgICAgICAgIC8+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBJbnB1dCAqL31cbiAgICAgIHtpc1dhaXRpbmcgJiYgKFxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzFyZW0nLFxuICAgICAgICAgIGJvcmRlclRvcDogJzFweCBzb2xpZCByZ2JhKDI1NSwyNTUsMjU1LDAuMSknLFxuICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuMyknLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBnYXA6ICcwLjVyZW0nXG4gICAgICAgIH19PlxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e2lucHV0TWVzc2FnZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5RG93bn1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2NyaXZpIGxhIHR1YSByaXNwb3N0YS4uLlwiXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBmbGV4OiAxLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMC43NXJlbScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuNXJlbScsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDI1NSwyNTUsMjU1LDAuMyknLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsMjU1LDI1NSwwLjEpJyxcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgIG91dGxpbmU6ICdub25lJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGF1dG9Gb2N1c1xuICAgICAgICAgIC8+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVXNlcklucHV0fVxuICAgICAgICAgICAgZGlzYWJsZWQ9eyFpbnB1dE1lc3NhZ2UudHJpbSgpfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgcGFkZGluZzogJzAuNzVyZW0gMS41cmVtJyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMC41cmVtJyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGlucHV0TWVzc2FnZS50cmltKCkgPyAnIzYzNjZmMScgOiAncmdiYSg5OSwgMTAyLCAyNDEsIDAuNSknLFxuICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgY3Vyc29yOiBpbnB1dE1lc3NhZ2UudHJpbSgpID8gJ3BvaW50ZXInIDogJ25vdC1hbGxvd2VkJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDihpJcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgT25ib2FyZGluZ0NoYXQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsIkNoYXRCdWJibGUiLCJjb21wbGV0ZU9uYm9hcmRpbmciLCJPbmJvYXJkaW5nQ2hhdCIsInVzZXJJZCIsIm9uQ29tcGxldGUiLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsImlucHV0TWVzc2FnZSIsInNldElucHV0TWVzc2FnZSIsImlzV2FpdGluZyIsInNldElzV2FpdGluZyIsInVzZXJEYXRhIiwic2V0VXNlckRhdGEiLCJuYW1lIiwiYWdlIiwiY2hhdENvbnRhaW5lclJlZiIsIm9uYm9hcmRpbmdTdGVwcyIsIm1lc3NhZ2UiLCJ3YWl0Rm9ySW5wdXQiLCJ2YWxpZGF0b3IiLCJpbnB1dCIsInRyaW0iLCJsZW5ndGgiLCJwcm9jZXNzb3IiLCJwcmV2IiwicGFyc2VJbnQiLCJpc05hTiIsImlzTGFzdCIsInNjcm9sbFRvQm90dG9tIiwiY3VycmVudCIsInNldFRpbWVvdXQiLCJzY3JvbGxUbyIsInRvcCIsInNjcm9sbEhlaWdodCIsImJlaGF2aW9yIiwiYWRkTWVzc2FnZSIsImNvbnRlbnQiLCJpc1VzZXIiLCJuZXdNZXNzYWdlIiwiaWQiLCJNYXRoIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwidGltZXN0YW1wIiwicHJvY2Vzc1N0ZXAiLCJzdGVwIiwiaGFuZGxlVXNlcklucHV0IiwiZXJyb3IiLCJoYW5kbGVLZXlEb3duIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJzdHlsZSIsInBvc2l0aW9uIiwibGVmdCIsInJpZ2h0IiwiYm90dG9tIiwiYmFja2dyb3VuZCIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwiekluZGV4IiwicGFkZGluZyIsImJvcmRlckJvdHRvbSIsInRleHRBbGlnbiIsImgyIiwiY29sb3IiLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJtYXJnaW4iLCJtYXJnaW5Cb3R0b20iLCJwIiwicmVmIiwiZmxleCIsIm92ZXJmbG93WSIsImdhcCIsIm1hcCIsImJvcmRlclRvcCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib25LZXlEb3duIiwicGxhY2Vob2xkZXIiLCJib3JkZXJSYWRpdXMiLCJib3JkZXIiLCJvdXRsaW5lIiwiYXV0b0ZvY3VzIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiY3Vyc29yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});