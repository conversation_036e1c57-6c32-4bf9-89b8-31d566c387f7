/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(pages-dir-node)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(pages-dir-node)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"(pages-dir-node)/./src/pages/index.tsx\");\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"(pages-dir-node)/./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"(pages-dir-node)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(pages-dir-node)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(pages-dir-node)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(pages-dir-node)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/index\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean((_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__[\"default\"] || _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        await routeModule.onRequestError(req, err, {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                isRevalidate: hasStaticProps,\n                isOnDemandRevalidate\n            })\n        }, routerServerContext);\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/AuraIndicator.tsx":
/*!******************************************!*\
  !*** ./src/components/AuraIndicator.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuraProgression: () => (/* binding */ AuraProgression),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuraInfo: () => (/* binding */ useAuraInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuraIndicator = ({ intimacyLevel, className = '', showLabel = false })=>{\n    // Mappatura dei livelli di intimità con colori e descrizioni\n    const getAuraInfo = (level)=>{\n        const auraMap = {\n            0: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-gray-400 to-blue-400',\n                shadowColor: 'shadow-blue-500/30',\n                glowColor: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',\n                label: 'Primo Incontro',\n                description: 'Conoscenza iniziale'\n            },\n            1: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-slate-400 to-blue-500',\n                shadowColor: 'shadow-blue-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(59,130,246,0.4)]',\n                label: 'Apertura',\n                description: 'Iniziando ad aprirsi'\n            },\n            2: {\n                color: 'teal',\n                bgColor: 'bg-gradient-to-r from-teal-400 to-cyan-500',\n                shadowColor: 'shadow-teal-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(20,184,166,0.4)]',\n                label: 'Connessione',\n                description: 'Connessione emotiva in crescita'\n            },\n            3: {\n                color: 'yellow',\n                bgColor: 'bg-gradient-to-r from-yellow-400 to-amber-500',\n                shadowColor: 'shadow-yellow-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(245,158,11,0.4)]',\n                label: 'Condivisione',\n                description: 'Scambio personale attivo'\n            },\n            4: {\n                color: 'red',\n                bgColor: 'bg-gradient-to-r from-red-400 to-pink-500',\n                shadowColor: 'shadow-red-500/30',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(239,68,68,0.3)]',\n                label: 'Profondità',\n                description: 'Discussioni delicate'\n            },\n            5: {\n                color: 'purple-pink',\n                bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',\n                shadowColor: 'shadow-purple-500/40',\n                glowColor: 'drop-shadow-[0_0_30px_rgba(139,92,246,0.4)]',\n                label: 'Affinità',\n                description: 'Profonda connessione'\n            }\n        };\n        return auraMap[level] || auraMap[0];\n    };\n    const auraInfo = getAuraInfo(intimacyLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            absolute inset-0 rounded-full\n            ${auraInfo.shadowColor}\n            ${auraInfo.glowColor}\n            animate-pulse-slow\n            blur-sm\n          `,\n                        style: {\n                            width: '24px',\n                            height: '24px',\n                            transform: 'scale(1.5)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            relative w-6 h-6 rounded-full\n            ${auraInfo.bgColor}\n            ${auraInfo.shadowColor}\n            shadow-lg\n            transition-all duration-500\n          `\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            absolute inset-1 rounded-full\n            bg-white/20\n            animate-ping\n          `,\n                        style: {\n                            animationDuration: '2s',\n                            animationIterationCount: 'infinite'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: auraInfo.label\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-white/60\",\n                        children: auraInfo.description\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n// Componente per mostrare la progressione dell'aura\nconst AuraProgression = ({ intimacyLevel })=>{\n    const levels = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuraIndicator, {\n                        intimacyLevel: level,\n                        className: `\n              transition-all duration-300\n              ${level <= intimacyLevel ? 'opacity-100 scale-100' : 'opacity-30 scale-75'}\n            `\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    level === intimacyLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-6 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, level, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook per ottenere informazioni sull'aura\nconst useAuraInfo = (intimacyLevel)=>{\n    const getAuraColor = ()=>{\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink'\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    };\n    const getAuraDescription = ()=>{\n        const descriptions = {\n            0: 'Primo incontro - Conoscenza iniziale',\n            1: 'Apertura - Iniziando ad aprirsi',\n            2: 'Connessione - Connessione emotiva in crescita',\n            3: 'Condivisione - Scambio personale attivo',\n            4: 'Profondità - Discussioni delicate',\n            5: 'Affinità - Profonda connessione'\n        };\n        return descriptions[intimacyLevel] || descriptions[0];\n    };\n    const getNextLevelDescription = ()=>{\n        if (intimacyLevel >= 5) return 'Livello massimo raggiunto';\n        const nextDescriptions = {\n            0: 'Continua a parlare per approfondire la conoscenza',\n            1: 'Condividi qualcosa di personale per crescere insieme',\n            2: 'Apri il cuore per rafforzare la connessione',\n            3: 'Esplora argomenti più profondi',\n            4: 'Condividi i tuoi pensieri più intimi'\n        };\n        return nextDescriptions[intimacyLevel] || '';\n    };\n    return {\n        color: getAuraColor(),\n        description: getAuraDescription(),\n        nextLevelDescription: getNextLevelDescription(),\n        isMaxLevel: intimacyLevel >= 5,\n        progress: intimacyLevel / 5 * 100\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuraIndicator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/AuraIndicator.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = ({ message, isUser, timestamp, className = '' })=>{\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('it-IT', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n      flex flex-col \n      ${isUser ? 'items-end' : 'items-start'} \n      mb-4 \n      ${className}\n    `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `\n        chat-bubble\n        ${isUser ? 'chat-bubble-user' : 'chat-bubble-ai'}\n        relative\n        group\n        hover:scale-105\n        transition-transform duration-200\n      `,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n            absolute -bottom-6 text-xs text-white/50\n            opacity-0 group-hover:opacity-100\n            transition-opacity duration-200\n            ${isUser ? 'right-0' : 'left-0'}\n          `,\n                    children: formatTime(timestamp)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, undefined),\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -bottom-2 -left-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '0ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '150ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '300ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ChatBubble.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ChatInput.tsx":
/*!**************************************!*\
  !*** ./src/components/ChatInput.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatInput = ({ onSendMessage, disabled = false, placeholder = \"Scrivi il tuo messaggio...\", className = '' })=>{\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-resize della textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n                textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\n            }\n        }\n    }[\"ChatInput.useEffect\"], [\n        message\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (message.trim() && !disabled) {\n            onSendMessage(message.trim());\n            setMessage('');\n            setIsTyping(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleChange = (e)=>{\n        setMessage(e.target.value);\n        setIsTyping(e.target.value.length > 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: message,\n                                onChange: handleChange,\n                                onKeyDown: handleKeyDown,\n                                placeholder: placeholder,\n                                disabled: disabled,\n                                rows: 1,\n                                className: `\n              input-field\n              w-full resize-none\n              min-h-[48px] max-h-32\n              pr-12\n              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\n            `\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: !message.trim() || disabled,\n                        className: `\n            glass-effect\n            p-3 rounded-xl\n            transition-all duration-200\n            hover:scale-105 hover:shadow-lg\n            focus:outline-none focus:ring-2 focus:ring-blue-400/50\n            ${!message.trim() || disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600/20 active:scale-95'}\n          `,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-white\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-6 left-0 text-xs text-white/40\",\n                children: \"Premi Invio per inviare, Shift+Invio per andare a capo\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInput);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ChatInput.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/GlassFrame.tsx":
/*!***************************************!*\
  !*** ./src/components/GlassFrame.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GlassFrame = ({ children, auraColor = 'blue', className = '' })=>{\n    const getAuraColorClass = (color)=>{\n        const colorMap = {\n            'gray-blue': 'shadow-blue-500/30',\n            'teal': 'shadow-teal-500/40',\n            'yellow': 'shadow-yellow-500/40',\n            'red': 'shadow-red-500/30',\n            'purple-pink': 'shadow-purple-500/40',\n            'blue': 'shadow-blue-500/30'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    const getAuraGlow = (color)=>{\n        const glowMap = {\n            'gray-blue': 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',\n            'teal': 'drop-shadow-[0_0_20px_rgba(20,184,166,0.4)]',\n            'yellow': 'drop-shadow-[0_0_20px_rgba(245,158,11,0.4)]',\n            'red': 'drop-shadow-[0_0_20px_rgba(239,68,68,0.3)]',\n            'purple-pink': 'drop-shadow-[0_0_20px_rgba(139,92,246,0.4)]',\n            'blue': 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]'\n        };\n        return glowMap[color] || glowMap.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          absolute inset-0 rounded-2xl \n          ${getAuraColorClass(auraColor)}\n          ${getAuraGlow(auraColor)}\n          aura-animation\n          shadow-2xl\n        `,\n                style: {\n                    filter: 'blur(2px)',\n                    transform: 'scale(1.02)'\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        relative glass-effect\n        border-2 border-white/20\n        ${getAuraColorClass(auraColor)}\n        transition-all duration-500 ease-in-out\n        hover:border-white/30\n        hover:shadow-2xl\n      `,\n                children: children\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlassFrame);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/GlassFrame.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDK0I7QUFFaEIsU0FBU0EsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakMiLCJzb3VyY2VzIjpbIkc6XFxGcmllbmRzXFxTb3VsVGFsa1xcc3JjXFxwYWdlc1xcX2FwcC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz47XG59XG4iXSwibmFtZXMiOlsiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-node)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-node)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ChatInput */ \"(pages-dir-node)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-node)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-node)/./src/state/userMemory.ts\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-node)/./src/utils/openrouter.ts\");\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    const { memory, isLoaded, addMessage, updateBasicInfo, addTrait, addEmotion, updateIntimacyLevel, getAuraColor, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll automatico verso il basso quando arrivano nuovi messaggi\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Nasconde il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (memory.conversationHistory.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Converte la cronologia della memoria in formato OpenRouter\n    const getOpenRouterHistory = ()=>{\n        return memory.conversationHistory.map((msg)=>({\n                role: msg.isUser ? 'user' : 'assistant',\n                content: msg.content\n            }));\n    };\n    // Gestisce l'invio di un nuovo messaggio\n    const handleSendMessage = async (message)=>{\n        if (isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Aggiunge il messaggio dell'utente alla cronologia\n            addMessage(message, true);\n            // Prepara il contesto utente per l'AI\n            const userContext = {\n                name: memory.name || undefined,\n                age: memory.age || undefined,\n                traits: memory.traits,\n                emotions: memory.emotions,\n                intimacyLevel: memory.intimacyLevel\n            };\n            // Invia il messaggio all'AI\n            const aiResponse = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(message, getOpenRouterHistory(), userContext);\n            // Aggiunge la risposta dell'AI alla cronologia\n            addMessage(aiResponse, false);\n            // Analizza il messaggio per estrarre informazioni (simulato per ora)\n            await analyzeAndUpdateMemory(message, aiResponse);\n        } catch (err) {\n            console.error('Errore nell\\'invio del messaggio:', err);\n            setError('Errore nella comunicazione. Riprova tra poco.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Analizza i messaggi e aggiorna la memoria (versione semplificata)\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        // Analisi semplificata basata su parole chiave\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !memory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updateBasicInfo({\n                    name\n                });\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !memory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updateBasicInfo({\n                    age\n                });\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                addEmotion(emotion);\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità basato sulla lunghezza e contenuto\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            const currentLevel = memory.intimacyLevel;\n            if (currentLevel < 5) {\n                updateIntimacyLevel(currentLevel + 1);\n            }\n        }\n    };\n    // Gestisce il reset della memoria\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            resetMemory();\n            setShowWelcome(true);\n            setError(null);\n        }\n    };\n    // Mostra loading durante l'idratazione\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - La tua amicizia AI\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Caricamento SoulTalk...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen p-4 flex flex-col items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white mb-2\",\n                                    children: \"SoulTalk\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"La tua amicizia AI che cresce con te\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            intimacyLevel: memory.intimacyLevel,\n                                            showLabel: true,\n                                            className: \"justify-center\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__.AuraProgression, {\n                                            intimacyLevel: memory.intimacyLevel\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-[600px] flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatContainerRef,\n                                    className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                                    children: [\n                                        showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-6 rounded-2xl max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-3\",\n                                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/80 text-sm leading-relaxed\",\n                                                        children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                message: message.content,\n                                                isUser: message.isUser,\n                                                timestamp: message.timestamp\n                                            }, message.id, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)),\n                                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-white/60 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-white/60 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-white/60 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-200 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-t border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        onSendMessage: handleSendMessage,\n                                        disabled: isLoading,\n                                        placeholder: memory.name ? `Cosa vuoi condividere con Soul, ${memory.name}?` : \"Dimmi qualcosa di te...\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex justify-between items-center text-sm text-white/60\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: memory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            memory.name,\n                                            \"! Livello di connessione: \",\n                                            memory.intimacyLevel,\n                                            \"/5\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleReset,\n                                    className: \"hover:text-white transition-colors duration-200\",\n                                    children: \"Ricomincia da capo\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/state/userMemory.ts":
/*!*********************************!*\
  !*** ./src/state/userMemory.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserMemoryManager: () => (/* binding */ UserMemoryManager),\n/* harmony export */   useUserMemory: () => (/* binding */ useUserMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Interfaccia per la memoria dell'utente\n// Stato iniziale della memoria utente\nconst initialUserMemory = {\n    name: '',\n    age: 0,\n    pronouns: '',\n    traits: [],\n    emotions: [],\n    keyMoments: [],\n    intimacyLevel: 0,\n    conversationHistory: [],\n    lastInteraction: new Date(),\n    personalityInsights: []\n};\n// Chiave per localStorage\nconst STORAGE_KEY = 'soultalk_user_memory';\n// Funzioni per gestire la memoria utente\nclass UserMemoryManager {\n    // Carica la memoria dal localStorage\n    static loadMemory() {\n        // Controlla se siamo nel browser\n        if (true) {\n            return {\n                ...initialUserMemory\n            };\n        }\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                const parsed = JSON.parse(stored);\n                // Converte le date da string a Date objects\n                parsed.lastInteraction = new Date(parsed.lastInteraction);\n                parsed.conversationHistory = parsed.conversationHistory.map((msg)=>({\n                        ...msg,\n                        timestamp: new Date(msg.timestamp)\n                    }));\n                parsed.personalityInsights = parsed.personalityInsights.map((insight)=>({\n                        ...insight,\n                        timestamp: new Date(insight.timestamp)\n                    }));\n                return parsed;\n            }\n        } catch (error) {\n            console.error('Errore nel caricamento della memoria utente:', error);\n        }\n        return {\n            ...initialUserMemory\n        };\n    }\n    // Salva la memoria nel localStorage\n    static saveMemory(memory) {\n        // Controlla se siamo nel browser\n        if (true) {\n            return;\n        }\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(memory));\n        } catch (error) {\n            console.error('Errore nel salvataggio della memoria utente:', error);\n        }\n    }\n    // Aggiunge un messaggio alla cronologia\n    static addMessage(memory, content, isUser, emotionalTone, topics) {\n        const newMessage = {\n            id: Date.now().toString(),\n            content,\n            isUser,\n            timestamp: new Date(),\n            emotionalTone,\n            topics\n        };\n        const updatedMemory = {\n            ...memory,\n            conversationHistory: [\n                ...memory.conversationHistory,\n                newMessage\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna le informazioni base dell'utente\n    static updateBasicInfo(memory, updates) {\n        const updatedMemory = {\n            ...memory,\n            ...updates,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un tratto della personalità\n    static addTrait(memory, trait) {\n        if (!memory.traits.includes(trait)) {\n            const updatedMemory = {\n                ...memory,\n                traits: [\n                    ...memory.traits,\n                    trait\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un'emozione\n    static addEmotion(memory, emotion) {\n        if (!memory.emotions.includes(emotion)) {\n            const updatedMemory = {\n                ...memory,\n                emotions: [\n                    ...memory.emotions,\n                    emotion\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un momento chiave\n    static addKeyMoment(memory, moment) {\n        const updatedMemory = {\n            ...memory,\n            keyMoments: [\n                ...memory.keyMoments,\n                moment\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna il livello di intimità\n    static updateIntimacyLevel(memory, level) {\n        const clampedLevel = Math.max(0, Math.min(5, level));\n        const updatedMemory = {\n            ...memory,\n            intimacyLevel: clampedLevel,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un insight sulla personalità\n    static addPersonalityInsight(memory, category, insight, confidence) {\n        const newInsight = {\n            category,\n            insight,\n            confidence: Math.max(0, Math.min(1, confidence)),\n            timestamp: new Date()\n        };\n        const updatedMemory = {\n            ...memory,\n            personalityInsights: [\n                ...memory.personalityInsights,\n                newInsight\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Ottiene il colore dell'aura basato sul livello di intimità\n    static getAuraColor(intimacyLevel) {\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink' // Affinità / profonda connessione\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    }\n    // Resetta la memoria utente\n    static resetMemory() {\n        const freshMemory = {\n            ...initialUserMemory\n        };\n        this.saveMemory(freshMemory);\n        return freshMemory;\n    }\n    // Esporta la memoria per backup\n    static exportMemory(memory) {\n        return JSON.stringify(memory, null, 2);\n    }\n    // Importa la memoria da backup\n    static importMemory(jsonData) {\n        try {\n            const imported = JSON.parse(jsonData);\n            // Validazione base della struttura\n            if (imported && typeof imported.name === 'string' && typeof imported.intimacyLevel === 'number') {\n                this.saveMemory(imported);\n                return imported;\n            }\n        } catch (error) {\n            console.error('Errore nell\\'importazione della memoria:', error);\n        }\n        return null;\n    }\n}\n// Hook React per gestire la memoria utente\n\nfunction useUserMemory() {\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useUserMemory.useState\": ()=>({\n                ...initialUserMemory\n            })\n    }[\"useUserMemory.useState\"]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Carica la memoria dal localStorage dopo l'idratazione\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserMemory.useEffect\": ()=>{\n            const loadedMemory = UserMemoryManager.loadMemory();\n            setMemory(loadedMemory);\n            setIsLoaded(true);\n        }\n    }[\"useUserMemory.useEffect\"], []);\n    // Funzioni helper che aggiornano lo stato locale\n    const addMessage = (content, isUser, emotionalTone, topics)=>{\n        const updatedMemory = UserMemoryManager.addMessage(memory, content, isUser, emotionalTone, topics);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateBasicInfo = (updates)=>{\n        const updatedMemory = UserMemoryManager.updateBasicInfo(memory, updates);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addTrait = (trait)=>{\n        const updatedMemory = UserMemoryManager.addTrait(memory, trait);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addEmotion = (emotion)=>{\n        const updatedMemory = UserMemoryManager.addEmotion(memory, emotion);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addKeyMoment = (moment)=>{\n        const updatedMemory = UserMemoryManager.addKeyMoment(memory, moment);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateIntimacyLevel = (level)=>{\n        const updatedMemory = UserMemoryManager.updateIntimacyLevel(memory, level);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addPersonalityInsight = (category, insight, confidence)=>{\n        const updatedMemory = UserMemoryManager.addPersonalityInsight(memory, category, insight, confidence);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const resetMemory = ()=>{\n        const freshMemory = UserMemoryManager.resetMemory();\n        setMemory(freshMemory);\n        return freshMemory;\n    };\n    const getAuraColor = ()=>UserMemoryManager.getAuraColor(memory.intimacyLevel);\n    return {\n        memory,\n        isLoaded,\n        addMessage,\n        updateBasicInfo,\n        addTrait,\n        addEmotion,\n        addKeyMoment,\n        updateIntimacyLevel,\n        addPersonalityInsight,\n        resetMemory,\n        getAuraColor,\n        exportMemory: ()=>UserMemoryManager.exportMemory(memory),\n        importMemory: (jsonData)=>{\n            const imported = UserMemoryManager.importMemory(jsonData);\n            if (imported) {\n                setMemory(imported);\n                return imported;\n            }\n            return null;\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/state/userMemory.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\n\nStart with a calm tone, asking their name, age and who they feel they are today.\n\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\n\nStore and remember what the user shares. You evolve as the connection grows.\n\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    constructor(apiKey, model){\n        this.apiKey = apiKey || process.env.OPENROUTER_API_KEY || '';\n        this.model = model || process.env.OPENROUTER_MODEL || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? `Ciao, sono ${userName}. È la prima volta che parliamo.` : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += `\\n- Name: ${userContext.name}`;\n            }\n            if (userContext.age) {\n                contextualPrompt += `\\n- Age: ${userContext.age}`;\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += `\\n- Personality traits: ${userContext.traits.join(', ')}`;\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += `\\n- Recent emotions: ${userContext.emotions.join(', ')}`;\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += `\\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: `Based on the recent conversation, determine if the intimacy level should increase. Current level: ${currentIntimacyLevel}/5. Respond with only \"yes\" or \"no\".`\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: `Recent conversation: ${recentMessages.slice(-6).map((m)=>`${m.role}: ${m.content}`).join('\\n')}`\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message, conversationHistory = [], userContext) {\n    const client = getOpenRouterClient();\n    if (conversationHistory.length === 0) {\n        return client.startConversation();\n    }\n    return client.continueConversation(conversationHistory, message, userContext);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/openrouter.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();