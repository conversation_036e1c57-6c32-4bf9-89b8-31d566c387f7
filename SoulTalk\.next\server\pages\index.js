/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(pages-dir-node)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(pages-dir-node)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"(pages-dir-node)/./src/pages/index.tsx\");\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"(pages-dir-node)/./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"(pages-dir-node)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(pages-dir-node)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(pages-dir-node)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(pages-dir-node)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__]);\n_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/index\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean((_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__[\"default\"] || _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        await routeModule.onRequestError(req, err, {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                isRevalidate: hasStaticProps,\n                isOnDemandRevalidate\n            })\n        }, routerServerContext);\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/AuraIndicator.tsx":
/*!******************************************!*\
  !*** ./src/components/AuraIndicator.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuraProgression: () => (/* binding */ AuraProgression),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuraInfo: () => (/* binding */ useAuraInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuraIndicator = ({ intimacyLevel, className = '', showLabel = false })=>{\n    // Mappatura dei livelli di intimità con colori e descrizioni\n    const getAuraInfo = (level)=>{\n        const auraMap = {\n            0: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-gray-400 to-blue-400',\n                shadowColor: 'shadow-blue-500/30',\n                glowColor: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',\n                label: 'Primo Incontro',\n                description: 'Conoscenza iniziale'\n            },\n            1: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-slate-400 to-blue-500',\n                shadowColor: 'shadow-blue-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(59,130,246,0.4)]',\n                label: 'Apertura',\n                description: 'Iniziando ad aprirsi'\n            },\n            2: {\n                color: 'teal',\n                bgColor: 'bg-gradient-to-r from-teal-400 to-cyan-500',\n                shadowColor: 'shadow-teal-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(20,184,166,0.4)]',\n                label: 'Connessione',\n                description: 'Connessione emotiva in crescita'\n            },\n            3: {\n                color: 'yellow',\n                bgColor: 'bg-gradient-to-r from-yellow-400 to-amber-500',\n                shadowColor: 'shadow-yellow-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(245,158,11,0.4)]',\n                label: 'Condivisione',\n                description: 'Scambio personale attivo'\n            },\n            4: {\n                color: 'red',\n                bgColor: 'bg-gradient-to-r from-red-400 to-pink-500',\n                shadowColor: 'shadow-red-500/30',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(239,68,68,0.3)]',\n                label: 'Profondità',\n                description: 'Discussioni delicate'\n            },\n            5: {\n                color: 'purple-pink',\n                bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',\n                shadowColor: 'shadow-purple-500/40',\n                glowColor: 'drop-shadow-[0_0_30px_rgba(139,92,246,0.4)]',\n                label: 'Affinità',\n                description: 'Profonda connessione'\n            }\n        };\n        return auraMap[level] || auraMap[0];\n    };\n    const auraInfo = getAuraInfo(intimacyLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            absolute inset-0 rounded-full\n            ${auraInfo.shadowColor}\n            ${auraInfo.glowColor}\n            animate-pulse-slow\n            blur-sm\n          `,\n                        style: {\n                            width: '24px',\n                            height: '24px',\n                            transform: 'scale(1.5)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            relative w-6 h-6 rounded-full\n            ${auraInfo.bgColor}\n            ${auraInfo.shadowColor}\n            shadow-lg\n            transition-all duration-500\n          `\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            absolute inset-1 rounded-full\n            bg-white/20\n            animate-ping\n          `,\n                        style: {\n                            animationDuration: '2s',\n                            animationIterationCount: 'infinite'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: auraInfo.label\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-white/60\",\n                        children: auraInfo.description\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n// Componente per mostrare la progressione dell'aura\nconst AuraProgression = ({ intimacyLevel })=>{\n    const levels = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuraIndicator, {\n                        intimacyLevel: level,\n                        className: `\n              transition-all duration-300\n              ${level <= intimacyLevel ? 'opacity-100 scale-100' : 'opacity-30 scale-75'}\n            `\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    level === intimacyLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-6 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, level, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook per ottenere informazioni sull'aura\nconst useAuraInfo = (intimacyLevel)=>{\n    const getAuraColor = ()=>{\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink'\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    };\n    const getAuraDescription = ()=>{\n        const descriptions = {\n            0: 'Primo incontro - Conoscenza iniziale',\n            1: 'Apertura - Iniziando ad aprirsi',\n            2: 'Connessione - Connessione emotiva in crescita',\n            3: 'Condivisione - Scambio personale attivo',\n            4: 'Profondità - Discussioni delicate',\n            5: 'Affinità - Profonda connessione'\n        };\n        return descriptions[intimacyLevel] || descriptions[0];\n    };\n    const getNextLevelDescription = ()=>{\n        if (intimacyLevel >= 5) return 'Livello massimo raggiunto';\n        const nextDescriptions = {\n            0: 'Continua a parlare per approfondire la conoscenza',\n            1: 'Condividi qualcosa di personale per crescere insieme',\n            2: 'Apri il cuore per rafforzare la connessione',\n            3: 'Esplora argomenti più profondi',\n            4: 'Condividi i tuoi pensieri più intimi'\n        };\n        return nextDescriptions[intimacyLevel] || '';\n    };\n    return {\n        color: getAuraColor(),\n        description: getAuraDescription(),\n        nextLevelDescription: getNextLevelDescription(),\n        isMaxLevel: intimacyLevel >= 5,\n        progress: intimacyLevel / 5 * 100\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuraIndicator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/AuraIndicator.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/AuthForm.tsx":
/*!*************************************!*\
  !*** ./src/components/AuthForm.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-node)/./src/lib/auth.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst AuthForm = ({ onAuthSuccess })=>{\n    const [isLogin, setIsLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n        // Validazioni\n        if (!username.trim() || !password.trim()) {\n            setError('Username e password sono obbligatori');\n            setIsLoading(false);\n            return;\n        }\n        if (username.length < 3) {\n            setError('L\\'username deve essere di almeno 3 caratteri');\n            setIsLoading(false);\n            return;\n        }\n        if (password.length < 6) {\n            setError('La password deve essere di almeno 6 caratteri');\n            setIsLoading(false);\n            return;\n        }\n        if (!isLogin && password !== confirmPassword) {\n            setError('Le password non coincidono');\n            setIsLoading(false);\n            return;\n        }\n        try {\n            let result;\n            if (isLogin) {\n                result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.loginUser)(username, password);\n            } else {\n                result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.registerUser)(username, password);\n            }\n            if (result.success && result.userId) {\n                onAuthSuccess(result.userId);\n            } else {\n                setError(result.error || 'Errore sconosciuto');\n            }\n        } catch (error) {\n            setError('Errore di connessione. Riprova più tardi.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.9)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 2000\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                background: 'rgba(255,255,255,0.1)',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '1rem',\n                padding: '2rem',\n                maxWidth: '400px',\n                width: '90%',\n                border: '1px solid rgba(255,255,255,0.2)'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        marginBottom: '2rem'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '3rem',\n                                marginBottom: '1rem'\n                            },\n                            children: \"\\uD83C\\uDF1F\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: 'white',\n                                fontSize: '1.5rem',\n                                fontWeight: '600',\n                                margin: 0,\n                                marginBottom: '0.5rem'\n                            },\n                            children: \"Benvenuto in SoulTalk\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: 'rgba(255,255,255,0.8)',\n                                fontSize: '0.875rem',\n                                margin: 0\n                            },\n                            children: isLogin ? 'Accedi per continuare la tua conversazione con Soul' : 'Crea un account per iniziare la tua avventura con Soul'\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Username\",\n                                value: username,\n                                onChange: (e)=>setUsername(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '0.75rem',\n                                    borderRadius: '0.5rem',\n                                    border: '1px solid rgba(255,255,255,0.3)',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none'\n                                },\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                placeholder: \"Password\",\n                                value: password,\n                                onChange: (e)=>setPassword(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '0.75rem',\n                                    borderRadius: '0.5rem',\n                                    border: '1px solid rgba(255,255,255,0.3)',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none'\n                                },\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        !isLogin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                placeholder: \"Conferma Password\",\n                                value: confirmPassword,\n                                onChange: (e)=>setConfirmPassword(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '0.75rem',\n                                    borderRadius: '0.5rem',\n                                    border: '1px solid rgba(255,255,255,0.3)',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none'\n                                },\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: '#ff6b6b',\n                                fontSize: '0.875rem',\n                                marginBottom: '1rem',\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            style: {\n                                width: '100%',\n                                padding: '0.75rem',\n                                borderRadius: '0.5rem',\n                                border: 'none',\n                                background: isLoading ? 'rgba(99, 102, 241, 0.5)' : '#6366f1',\n                                color: 'white',\n                                fontSize: '1rem',\n                                fontWeight: '600',\n                                cursor: isLoading ? 'not-allowed' : 'pointer',\n                                marginBottom: '1rem'\n                            },\n                            children: isLoading ? isLogin ? 'Accesso...' : 'Registrazione...' : isLogin ? 'Accedi' : 'Registrati'\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    setIsLogin(!isLogin);\n                                    setError('');\n                                    setPassword('');\n                                    setConfirmPassword('');\n                                },\n                                disabled: isLoading,\n                                style: {\n                                    background: 'none',\n                                    border: 'none',\n                                    color: 'rgba(255,255,255,0.8)',\n                                    fontSize: '0.875rem',\n                                    cursor: isLoading ? 'not-allowed' : 'pointer',\n                                    textDecoration: 'underline'\n                                },\n                                children: isLogin ? 'Non hai un account? Registrati' : 'Hai già un account? Accedi'\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthForm);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/AuthForm.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = ({ message, isUser })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `message-container ${isUser ? 'user' : 'assistant'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `message ${isUser ? 'user-message' : 'assistant-message'}`,\n            children: message\n        }, void 0, false, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL0NoYXRCdWJibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQU8xQixNQUFNQyxhQUF3QyxDQUFDLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFO0lBQ2hFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXLENBQUMsa0JBQWtCLEVBQUVGLFNBQVMsU0FBUyxhQUFhO2tCQUNsRSw0RUFBQ0M7WUFBSUMsV0FBVyxDQUFDLFFBQVEsRUFBRUYsU0FBUyxpQkFBaUIscUJBQXFCO3NCQUN2RUQ7Ozs7Ozs7Ozs7O0FBSVQ7QUFFQSxpRUFBZUQsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXGNvbXBvbmVudHNcXENoYXRCdWJibGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBDaGF0QnViYmxlUHJvcHMge1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGlzVXNlcjogYm9vbGVhbjtcbn1cblxuY29uc3QgQ2hhdEJ1YmJsZTogUmVhY3QuRkM8Q2hhdEJ1YmJsZVByb3BzPiA9ICh7IG1lc3NhZ2UsIGlzVXNlciB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BtZXNzYWdlLWNvbnRhaW5lciAke2lzVXNlciA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BtZXNzYWdlICR7aXNVc2VyID8gJ3VzZXItbWVzc2FnZScgOiAnYXNzaXN0YW50LW1lc3NhZ2UnfWB9PlxuICAgICAgICB7bWVzc2FnZX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hhdEJ1YmJsZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRCdWJibGUiLCJtZXNzYWdlIiwiaXNVc2VyIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ChatBubble.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/GlassFrame.tsx":
/*!***************************************!*\
  !*** ./src/components/GlassFrame.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GlassFrame = ({ children, auraColor = 'blue', className = '' })=>{\n    const getAuraColorClass = (color)=>{\n        const colorMap = {\n            'gray-blue': 'shadow-blue-500/30',\n            'teal': 'shadow-teal-500/40',\n            'yellow': 'shadow-yellow-500/40',\n            'red': 'shadow-red-500/30',\n            'purple-pink': 'shadow-purple-500/40',\n            'blue': 'shadow-blue-500/30'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    const getAuraColorRgba = (color)=>{\n        const colorMap = {\n            'gray-blue': 'rgba(59, 130, 246, 0.3)',\n            'teal': 'rgba(20, 184, 166, 0.4)',\n            'yellow': 'rgba(245, 158, 11, 0.4)',\n            'red': 'rgba(239, 68, 68, 0.3)',\n            'purple-pink': 'rgba(139, 92, 246, 0.4)',\n            'blue': 'rgba(59, 130, 246, 0.3)'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl aura-animation\",\n                style: {\n                    filter: 'blur(2px)',\n                    transform: 'scale(1.02)',\n                    boxShadow: `0 0 40px ${getAuraColorRgba(auraColor)}`\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative glass-effect transition-all duration-500\",\n                style: {\n                    border: `2px solid rgba(255, 255, 255, 0.2)`,\n                    boxShadow: `0 0 30px ${getAuraColorRgba(auraColor)}`\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlassFrame);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/GlassFrame.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-node)/./src/components/ChatBubble.tsx\");\n\n\n\nconst OnboardingChat = ({ userId, onComplete })=>{\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>`${name}... che bel nome! Mi piace molto come suona. 💫`,\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return `Perfetto ${name}! ${age} anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡`;\n                if (age < 30) return `Fantastico ${name}! ${age} anni... un'età perfetta per esplorare nuove idee insieme! ✨`;\n                if (age < 50) return `Ottimo ${name}! ${age} anni... hai esperienza e saggezza, sarà bellissimo parlare con te! 🌟`;\n                return `Meraviglioso ${name}! ${age} anni... la tua esperienza di vita sarà preziosa per le nostre conversazioni! 🎭`;\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser, withTyping = false)=>{\n        const newMessage = {\n            id: `onb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"OnboardingChat.useCallback[processStep]\": async ()=>{\n            console.log('processStep called for step:', currentStep);\n            const step = onboardingSteps[currentStep];\n            if (!step) {\n                console.log('No step found for:', currentStep);\n                return;\n            }\n            console.log('Processing step:', currentStep, step);\n            // Determina il messaggio da mostrare\n            let messageContent;\n            if (typeof step.message === 'function') {\n                messageContent = step.message(userData.name, userData.age);\n            } else {\n                messageContent = step.message;\n            }\n            // Aggiungi messaggio con effetto typing\n            await addMessageWithTyping(messageContent);\n            if (step.isLast) {\n                // Completa l'onboarding con delay personalizzato\n                setTimeout({\n                    \"OnboardingChat.useCallback[processStep]\": async ()=>{\n                        onComplete(userData);\n                    }\n                }[\"OnboardingChat.useCallback[processStep]\"], step.delay || 2500);\n                return;\n            }\n            if (!step.waitForInput) {\n                setTimeout({\n                    \"OnboardingChat.useCallback[processStep]\": ()=>{\n                        setCurrentStep({\n                            \"OnboardingChat.useCallback[processStep]\": (prev)=>prev + 1\n                        }[\"OnboardingChat.useCallback[processStep]\"]);\n                    }\n                }[\"OnboardingChat.useCallback[processStep]\"], step.delay || 2000);\n            } else {\n                setIsWaiting(true);\n            }\n        }\n    }[\"OnboardingChat.useCallback[processStep]\"], [\n        currentStep,\n        userData.name,\n        userData.age,\n        userId,\n        onComplete\n    ]);\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizializzazione una sola volta al mount\n            if (!hasStarted) {\n                setHasStarted(true);\n                console.log('Starting onboarding...');\n                const timer = setTimeout({\n                    \"OnboardingChat.useEffect.timer\": ()=>{\n                        console.log('Processing first step...');\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect.timer\"], 1000);\n                return ({\n                    \"OnboardingChat.useEffect\": ()=>clearTimeout(timer)\n                })[\"OnboardingChat.useEffect\"];\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        hasStarted,\n        processStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Per tutti gli step successivi al primo\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                console.log('Step changed to:', currentStep);\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep,\n        processStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: `radial-gradient(circle, #8b5cf6 0%, transparent 70%)`,\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: `${currentStep / totalSteps * 100}%`,\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.5)',\n                            textAlign: 'center',\n                            padding: '2rem'\n                        },\n                        children: [\n                            \"Inizializzando conversazione... (Step: \",\n                            currentStep,\n                            \", Messages: \",\n                            messages.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.05)',\n                            borderRadius: '1rem',\n                            padding: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.1)',\n                            display: 'flex',\n                            gap: '0.5rem',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi la tua risposta...\",\n                                style: {\n                                    flex: 1,\n                                    padding: '0.75rem 1rem',\n                                    borderRadius: '0.75rem',\n                                    border: 'none',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none',\n                                    backdropFilter: 'blur(10px)'\n                                },\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleUserInput,\n                                disabled: !inputMessage.trim(),\n                                style: {\n                                    width: '48px',\n                                    height: '48px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    background: inputMessage.trim() ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' : 'rgba(139, 92, 246, 0.3)',\n                                    color: 'white',\n                                    fontSize: '1.2rem',\n                                    cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    transition: 'all 0.2s ease',\n                                    transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'\n                                },\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.75rem',\n                            color: 'rgba(255,255,255,0.5)',\n                            fontSize: '0.75rem'\n                        },\n                        children: \"Premi Invio per inviare\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/OnboardingChat.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-node)/./src/lib/database.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-node)/./src/lib/auth.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [needsOnboarding, setNeedsOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Funzione per autenticare l'utente dopo login\n    const authenticateUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[authenticateUser]\": async (newUserId)=>{\n            setUserId(newUserId);\n            setIsAuthenticated(true);\n            setIsLoading(true);\n            try {\n                // Controlla se l'utente ha completato l'onboarding\n                const isOnboarded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.checkOnboardingStatus)(newUserId);\n                if (isOnboarded) {\n                    // Utente esistente con onboarding completato - carica dati\n                    const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(newUserId);\n                    if (profile) {\n                        setUserMemory({\n                            name: profile.name || '',\n                            age: profile.age || 0,\n                            traits: profile.traits || [],\n                            emotions: profile.emotions || [],\n                            intimacyLevel: profile.intimacy_level || 0,\n                            keyMoments: profile.key_moments || []\n                        });\n                    }\n                    const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(newUserId);\n                    const formattedMessages = chatMessages.map({\n                        \"useSupabaseMemory.useCallback[authenticateUser].formattedMessages\": (msg)=>({\n                                id: msg.id,\n                                content: msg.content,\n                                isUser: msg.is_user,\n                                timestamp: new Date(msg.timestamp)\n                            })\n                    }[\"useSupabaseMemory.useCallback[authenticateUser].formattedMessages\"]);\n                    setMessages(formattedMessages);\n                    setNeedsOnboarding(false);\n                } else {\n                    // Utente nuovo o senza onboarding completato\n                    setNeedsOnboarding(true);\n                }\n            } catch (error) {\n                console.error('Error loading user data:', error);\n                setNeedsOnboarding(true); // In caso di errore, vai all'onboarding\n            } finally{\n                setIsLoading(false);\n            }\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveUserSession)(newUserId);\n        }\n    }[\"useSupabaseMemory.useCallback[authenticateUser]\"], []);\n    // Funzione per logout\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logoutUser)();\n            setUserId('');\n            setIsAuthenticated(false);\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setNeedsOnboarding(false);\n        }\n    }[\"useSupabaseMemory.useCallback[logout]\"], []);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!userId || isLoading || !userMemory.name || \"undefined\" === 'undefined') return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se abbiamo userId e siamo lato client\n            if (userId && \"undefined\" !== 'undefined') {}\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!userId || \"undefined\" === 'undefined') return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                logout();\n                window.location.reload();\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        userId,\n        logout\n    ]);\n    // Completa l'onboarding con i dati dell'utente\n    const completeOnboardingWithData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": async (userData)=>{\n            if (!userId) return;\n            try {\n                // Aggiorna il profilo utente con i dati dell'onboarding\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)({\n                    id: userId,\n                    name: userData.name,\n                    age: userData.age,\n                    is_onboarded: true\n                });\n                // Aggiorna lo stato locale\n                setUserMemory({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": (prev)=>({\n                            ...prev,\n                            name: userData.name,\n                            age: userData.age\n                        })\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"]);\n                // Marca l'onboarding come completato\n                setNeedsOnboarding(false);\n            } catch (error) {\n                console.error('Error completing onboarding:', error);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], [\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        isAuthenticated,\n        needsOnboarding,\n        addMessage,\n        updateUserMemory,\n        resetMemory,\n        authenticateUser,\n        logout,\n        completeOnboardingWithData\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/hooks/useSupabaseMemory.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOnboardingStatus: () => (/* binding */ checkOnboardingStatus),\n/* harmony export */   completeOnboarding: () => (/* binding */ completeOnboarding),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   resetOnboardingStatus: () => (/* binding */ resetOnboardingStatus),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-node)/./src/lib/supabase.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([bcryptjs__WEBPACK_IMPORTED_MODULE_1__]);\nbcryptjs__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Funzione per hash della password\nconst hashPassword = async (password)=>{\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n};\n// Funzione per verificare la password\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n};\n// Registrazione nuovo utente\nconst registerUser = async (username, password)=>{\n    try {\n        // Controlla se l'username esiste già\n        const { data: existingUser, error: checkError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('username').eq('username', username.toLowerCase()).single();\n        if (checkError && checkError.code !== 'PGRST116') {\n            console.error('Error checking existing user:', checkError);\n            return {\n                success: false,\n                error: 'Errore durante il controllo username'\n            };\n        }\n        if (existingUser) {\n            return {\n                success: false,\n                error: 'Username già esistente'\n            };\n        }\n        // Crea hash della password\n        const passwordHash = await hashPassword(password);\n        // Genera ID utente\n        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Inserisci nella tabella auth\n        const { error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            password_hash: passwordHash\n        });\n        if (authError) {\n            console.error('Error creating auth:', authError);\n            return {\n                success: false,\n                error: 'Errore durante la registrazione'\n            };\n        }\n        // Crea profilo utente\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            name: '',\n            is_onboarded: false\n        });\n        if (profileError) {\n            console.error('Error creating profile:', profileError);\n            // Rollback: elimina l'auth se il profilo fallisce\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').delete().eq('id', userId);\n            return {\n                success: false,\n                error: 'Errore durante la creazione del profilo'\n            };\n        }\n        return {\n            success: true,\n            userId\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Login utente\nconst loginUser = async (username, password)=>{\n    try {\n        // Trova l'utente\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('*').eq('username', username.toLowerCase()).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Verifica password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Aggiorna last_login\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').update({\n            last_login: new Date().toISOString()\n        }).eq('id', user.id);\n        return {\n            success: true,\n            userId: user.id\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Verifica se l'utente è autenticato (controlla localStorage)\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const authData = localStorage.getItem('soul_auth');\n    if (!authData) return null;\n    try {\n        const { userId, timestamp } = JSON.parse(authData);\n        // Controlla se la sessione è scaduta (24 ore)\n        const now = Date.now();\n        const sessionAge = now - timestamp;\n        const maxAge = 24 * 60 * 60 * 1000 // 24 ore\n        ;\n        if (sessionAge > maxAge) {\n            localStorage.removeItem('soul_auth');\n            return null;\n        }\n        return userId;\n    } catch  {\n        localStorage.removeItem('soul_auth');\n        return null;\n    }\n};\n// Salva sessione utente\nconst saveUserSession = (userId)=>{\n    if (true) return;\n    const authData = {\n        userId,\n        timestamp: Date.now()\n    };\n    localStorage.setItem('soul_auth', JSON.stringify(authData));\n};\n// Logout utente\nconst logoutUser = ()=>{\n    if (true) return;\n    localStorage.removeItem('soul_auth');\n    localStorage.removeItem('soul_user_id'); // Rimuovi anche il vecchio ID\n};\n// Controlla se l'utente ha completato l'onboarding\nconst checkOnboardingStatus = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('is_onboarded').eq('id', userId).single();\n        if (error || !data) {\n            return false;\n        }\n        return data.is_onboarded || false;\n    } catch (error) {\n        console.error('Exception in checkOnboardingStatus:', error);\n        return false;\n    }\n};\n// Completa l'onboarding\nconst completeOnboarding = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: true\n        }).eq('id', userId);\n        return !error;\n    } catch  {\n        return false;\n    }\n};\n// Funzione di debug per resettare l'onboarding (solo per test)\nconst resetOnboardingStatus = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: false,\n            name: '',\n            age: null\n        }).eq('id', userId);\n        return !error;\n    } catch  {\n        return false;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatSession: () => (/* binding */ createChatSession),\n/* harmony export */   createOrUpdateUserProfile: () => (/* binding */ createOrUpdateUserProfile),\n/* harmony export */   deleteUserData: () => (/* binding */ deleteUserData),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   saveChatMessage: () => (/* binding */ saveChatMessage)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-node)/./src/lib/supabase.ts\");\n\n// Genera un ID utente unico basato su browser fingerprint\nconst generateUserId = ()=>{\n    // Controlla se siamo nel browser (non SSR)\n    if (true) {\n        return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    const stored = localStorage.getItem('soul_user_id');\n    if (stored) return stored;\n    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    localStorage.setItem('soul_user_id', userId);\n    return userId;\n};\n// Funzioni per UserProfile\nconst getUserProfile = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('*').eq('id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in getUserProfile:', error);\n        return null;\n    }\n};\nconst createOrUpdateUserProfile = async (profile)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').upsert({\n            ...profile,\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createOrUpdateUserProfile:', error);\n        return null;\n    }\n};\n// Funzioni per ChatMessage\nconst getChatMessages = async (userId, limit = 100)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').select('*').eq('user_id', userId).order('timestamp', {\n            ascending: true\n        }).limit(limit);\n        if (error) {\n            console.error('Error fetching chat messages:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getChatMessages:', error);\n        return [];\n    }\n};\nconst saveChatMessage = async (message)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').insert({\n            ...message,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error saving chat message:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in saveChatMessage:', error);\n        return null;\n    }\n};\n// Funzioni per ChatSession\nconst createChatSession = async (userId, sessionName)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').insert({\n            id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            user_id: userId,\n            session_name: sessionName || `Chat ${new Date().toLocaleDateString()}`,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error creating chat session:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createChatSession:', error);\n        return null;\n    }\n};\nconst getUserSessions = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').select('*').eq('user_id', userId).order('updated_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching user sessions:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getUserSessions:', error);\n        return [];\n    }\n};\n// Funzione per eliminare tutti i dati utente (reset completo)\nconst deleteUserData = async (userId)=>{\n    try {\n        // Elimina messaggi\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').delete().eq('user_id', userId);\n        // Elimina sessioni\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').delete().eq('user_id', userId);\n        // Elimina profilo\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').delete().eq('id', userId);\n        // Rimuovi anche dal localStorage\n        localStorage.removeItem('soul_user_id');\n        return true;\n    } catch (error) {\n        console.error('Error deleting user data:', error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/database.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = 'https://igybrqsuvkbeivyhrrvd.supabase.co';\nconst supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlneWJycXN1dmtiZWl2eWhycnZkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMjkzNzIsImV4cCI6MjA2ODYwNTM3Mn0.PfgqZs9fX5xEkiLiMArGR70PK2c6-Z9yaMiU7u8vqnM';\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWM7QUFDcEIsTUFBTUMsY0FBYztBQUViLE1BQU1DLFdBQVdILG1FQUFZQSxDQUFDQyxhQUFhQyxhQUFZIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xcbGliXFxzdXBhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gJ2h0dHBzOi8vaWd5YnJxc3V2a2JlaXZ5aHJydmQuc3VwYWJhc2UuY28nXG5jb25zdCBzdXBhYmFzZUtleSA9ICdleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUp6ZFhCaFltRnpaU0lzSW5KbFppSTZJbWxuZVdKeWNYTjFkbXRpWldsMmVXaHljblprSWl3aWNtOXNaU0k2SW1GdWIyNGlMQ0pwWVhRaU9qRTNOVE13TWprek56SXNJbVY0Y0NJNk1qQTJPRFl3TlRNM01uMC5QZmdxWnM5Zlg1eEVraUxpTUFyR1I3MFBLMmM2LVo5eWFNaVU3dTh2cW5NJ1xuXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5KVxuXG4vLyBUaXBpIHBlciBpbCBkYXRhYmFzZVxuZXhwb3J0IGludGVyZmFjZSBVc2VyUHJvZmlsZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGFnZT86IG51bWJlclxuICB0cmFpdHM6IHN0cmluZ1tdXG4gIGVtb3Rpb25zOiBzdHJpbmdbXVxuICBpbnRpbWFjeV9sZXZlbDogbnVtYmVyXG4gIGtleV9tb21lbnRzOiBzdHJpbmdbXVxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2hhdE1lc3NhZ2Uge1xuICBpZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBjb250ZW50OiBzdHJpbmdcbiAgaXNfdXNlcjogYm9vbGVhblxuICB0aW1lc3RhbXA6IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDaGF0U2Vzc2lvbiB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcl9pZDogc3RyaW5nXG4gIHNlc3Npb25fbmFtZT86IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJzdXBhYmFzZUtleSIsInN1cGFiYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_global_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/global.css */ \"(pages-dir-node)/./src/styles/global.css\");\n/* harmony import */ var _styles_global_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_global_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDOEI7QUFFZixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXHBhZ2VzXFxfYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFsLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-node)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-node)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-node)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-node)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-node)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-node)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-node)/./src/hooks/useSupabaseMemory.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__, _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__]);\n([_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__, _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: `\n        radial-gradient(circle at 20% 80%, ${config.colors[0]} 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${config.colors[1]} 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${config.colors[2]} 0%, transparent 50%),\n        ${config.baseGradient}\n      `,\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(`🌟 Livello di connessione aumentato: ${newLevel}/4`);\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = `intimacy-level-${userMemory.intimacyLevel}`;\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se non è autenticato, mostra sempre il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Caricamento...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.9)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Caricando i tuoi dati...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: `radial-gradient(circle, ${getAuraColor()} 0%, transparent 70%)`,\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? `Cosa vuoi condividere con Soul, ${userMemory.name}?` : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            messages.length,\n                                            \" msg\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"ID: \",\n                                            userId.slice(-6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJEO0FBQzlCO0FBQ3FCO0FBQ0E7QUFDMkI7QUFDL0I7QUFDWTtBQUNKO0FBQ1M7QUFFaEQsU0FBU1k7SUFDdEIsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR2IsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dCLGFBQWFDLGVBQWUsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1rQixXQUFXakIsNkNBQU1BLENBQXNCO0lBQzdDLE1BQU1rQixtQkFBbUJsQiw2Q0FBTUEsQ0FBaUI7SUFFaEQsMENBQTBDO0lBQzFDLE1BQU0sRUFDSm1CLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxVQUFVLEVBQ1ZDLFdBQVdDLGFBQWEsRUFDeEJDLFFBQVEsRUFDUkMsZUFBZSxFQUNmQyxlQUFlLEVBQ2ZDLFVBQVUsRUFDVkMsZ0JBQWdCLEVBQ2hCQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQkMsTUFBTSxFQUNOQywwQkFBMEIsRUFDM0IsR0FBR3ZCLDJFQUFpQkE7SUFFckIsTUFBTXdCLGlCQUFpQjtRQUNyQixJQUFJZixpQkFBaUJnQixPQUFPLEVBQUU7WUFDNUJDLFdBQVc7Z0JBQ1QsSUFBSWpCLGlCQUFpQmdCLE9BQU8sRUFBRTtvQkFDNUJoQixpQkFBaUJnQixPQUFPLENBQUNFLFFBQVEsQ0FBQzt3QkFDaENDLEtBQUtuQixpQkFBaUJnQixPQUFPLENBQUNJLFlBQVk7d0JBQzFDQyxVQUFVO29CQUNaO2dCQUNGO1lBQ0YsR0FBRztRQUNMO0lBQ0Y7SUFFQSw4RUFBOEU7SUFDOUUsTUFBTUMsZUFBZTtRQUNuQixNQUFNQyxTQUFTO1lBQ2I7WUFDQTtZQUNBO1lBQ0E7WUFDQSxVQUFXLGdCQUFnQjtTQUM1QjtRQUNELE9BQU9BLE1BQU0sQ0FBQ3BCLFdBQVdxQixhQUFhLENBQUMsSUFBSUQsTUFBTSxDQUFDLEVBQUU7SUFDdEQ7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTUUsdUJBQXVCO1FBQzNCLE1BQU1ELGdCQUFnQnJCLFdBQVdxQixhQUFhO1FBRTlDLE1BQU1FLG9CQUFvQjtZQUN4QiwrQ0FBK0M7WUFDL0M7Z0JBQ0VILFFBQVE7b0JBQUM7b0JBQTRCO29CQUEyQjtpQkFBMkI7Z0JBQzNGSSxjQUFjO1lBQ2hCO1lBQ0EsMENBQTBDO1lBQzFDO2dCQUNFSixRQUFRO29CQUFDO29CQUEyQjtvQkFBNEI7aUJBQTBCO2dCQUMxRkksY0FBYztZQUNoQjtZQUNBLG9DQUFvQztZQUNwQztnQkFDRUosUUFBUTtvQkFBQztvQkFBNEI7b0JBQTJCO2lCQUEwQjtnQkFDMUZJLGNBQWM7WUFDaEI7WUFDQSwwQ0FBMEM7WUFDMUM7Z0JBQ0VKLFFBQVE7b0JBQUM7b0JBQTJCO29CQUEyQjtpQkFBMkI7Z0JBQzFGSSxjQUFjO1lBQ2hCO1lBQ0EsMENBQTBDO1lBQzFDO2dCQUNFSixRQUFRO29CQUFDO29CQUEyQjtvQkFBNEI7aUJBQTRCO2dCQUM1RkksY0FBYztZQUNoQjtTQUNEO1FBRUQsTUFBTUMsU0FBU0YsaUJBQWlCLENBQUNGLGNBQWMsSUFBSUUsaUJBQWlCLENBQUMsRUFBRTtRQUV2RSxPQUFPO1lBQ0xHLFlBQVksQ0FBQzsyQ0FDd0IsRUFBRUQsT0FBT0wsTUFBTSxDQUFDLEVBQUUsQ0FBQzsyQ0FDbkIsRUFBRUssT0FBT0wsTUFBTSxDQUFDLEVBQUUsQ0FBQzsyQ0FDbkIsRUFBRUssT0FBT0wsTUFBTSxDQUFDLEVBQUUsQ0FBQztRQUN0RCxFQUFFSyxPQUFPRCxZQUFZLENBQUM7TUFDeEIsQ0FBQztZQUNERyxnQkFBZ0I7WUFDaEJDLHNCQUFzQjtZQUN0QkMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7SUFDRjtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNQyx5QkFBeUIsT0FBT0MsYUFBcUJDO1FBQ3pELE1BQU1DLG1CQUFtQkYsWUFBWUcsV0FBVztRQUNoRCxNQUFNQyxVQUFlLENBQUM7UUFFdEIsMkJBQTJCO1FBQzNCLElBQUlGLGlCQUFpQkcsUUFBUSxDQUFDLGdCQUFnQkgsaUJBQWlCRyxRQUFRLENBQUMsVUFBVTtZQUNoRixNQUFNQyxZQUFZTixZQUFZTyxLQUFLLENBQUM7WUFDcEMsSUFBSUQsYUFBYSxDQUFDdEMsV0FBV3dDLElBQUksRUFBRTtnQkFDakMsTUFBTUEsT0FBT0YsU0FBUyxDQUFDLEVBQUUsSUFBSUEsU0FBUyxDQUFDLEVBQUU7Z0JBQ3pDRixRQUFRSSxJQUFJLEdBQUdBO1lBQ2pCO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsTUFBTUMsV0FBV1QsWUFBWU8sS0FBSyxDQUFDO1FBQ25DLElBQUlFLFlBQVksQ0FBQ3pDLFdBQVcwQyxHQUFHLEVBQUU7WUFDL0IsTUFBTUEsTUFBTUMsU0FBU0YsUUFBUSxDQUFDLEVBQUUsSUFBSUEsUUFBUSxDQUFDLEVBQUU7WUFDL0MsSUFBSUMsTUFBTSxLQUFLQSxNQUFNLEtBQUs7Z0JBQ3hCTixRQUFRTSxHQUFHLEdBQUdBO1lBQ2hCO1FBQ0Y7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTUUsV0FBVztZQUNmLFVBQVU7Z0JBQUM7Z0JBQVU7Z0JBQVk7Z0JBQVc7YUFBVTtZQUN0RCxVQUFVO2dCQUFDO2dCQUFVO2dCQUFZO2dCQUFPO2FBQU87WUFDL0MsY0FBYztnQkFBQztnQkFBYztnQkFBVzthQUFZO1lBQ3BELGVBQWU7Z0JBQUM7Z0JBQWU7Z0JBQVc7YUFBVTtZQUNwRCxZQUFZO2dCQUFDO2dCQUFZO2dCQUFjO2FBQVM7WUFDaEQsU0FBUztnQkFBQztnQkFBUztnQkFBYzthQUFTO1FBQzVDO1FBRUEsS0FBSyxNQUFNLENBQUNDLFNBQVNDLFNBQVMsSUFBSUMsT0FBT0MsT0FBTyxDQUFDSixVQUFXO1lBQzFELElBQUlFLFNBQVNHLElBQUksQ0FBQ0MsQ0FBQUEsVUFBV2hCLGlCQUFpQkcsUUFBUSxDQUFDYSxXQUFXO2dCQUNoRSxJQUFJLENBQUNsRCxXQUFXNEMsUUFBUSxDQUFDUCxRQUFRLENBQUNRLFVBQVU7b0JBQzFDVCxRQUFRUSxRQUFRLEdBQUc7MkJBQUk1QyxXQUFXNEMsUUFBUTt3QkFBRUM7cUJBQVE7Z0JBQ3REO2dCQUNBO1lBQ0Y7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJYixZQUFZbUIsTUFBTSxHQUFHLE9BQ3JCakIsaUJBQWlCRyxRQUFRLENBQUMsZ0JBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxjQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsZUFDMUJILGlCQUFpQkcsUUFBUSxDQUFDLFlBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxlQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsaUJBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxZQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsVUFBVTtZQUV0QyxNQUFNZSxXQUFXQyxLQUFLQyxHQUFHLENBQUN0RCxXQUFXcUIsYUFBYSxHQUFHLEdBQUc7WUFFeEQsc0RBQXNEO1lBQ3RELElBQUkrQixXQUFXcEQsV0FBV3FCLGFBQWEsRUFBRTtnQkFDdkNrQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQ0FBcUMsRUFBRUosU0FBUyxFQUFFLENBQUM7Z0JBQ2hFaEIsUUFBUWYsYUFBYSxHQUFHK0I7WUFDMUI7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJTCxPQUFPVSxJQUFJLENBQUNyQixTQUFTZSxNQUFNLEdBQUcsR0FBRztZQUNuQzVDLGlCQUFpQjZCO1FBQ25CO0lBQ0Y7SUFFQXhELGdEQUFTQTswQkFBQztZQUNSZ0M7UUFDRjt5QkFBRztRQUFDYjtRQUFVUDtLQUFZO0lBRTFCLDZEQUE2RDtJQUM3RFosZ0RBQVNBOzBCQUFDO1lBQ1IsSUFBSW1CLFNBQVNvRCxNQUFNLEdBQUcsR0FBRztnQkFDdkJ4RCxlQUFlO1lBQ2pCO1FBQ0Y7eUJBQUc7UUFBQ0k7S0FBUztJQUViLGtFQUFrRTtJQUNsRW5CLGdEQUFTQTswQkFBQztZQUNSLE1BQU04RSxrQkFBa0JwQztZQUN4QixNQUFNcUMsT0FBT0MsU0FBU0QsSUFBSTtZQUUxQix3REFBd0Q7WUFDeERaLE9BQU9jLE1BQU0sQ0FBQ0YsS0FBS0csS0FBSyxFQUFFSjtZQUUxQix1REFBdUQ7WUFDdkRDLEtBQUtJLFNBQVMsR0FBRyxDQUFDLGVBQWUsRUFBRS9ELFdBQVdxQixhQUFhLEVBQUU7WUFFN0Qsc0VBQXNFO1lBQ3RFO2tDQUFPO2dCQUNMLCtDQUErQztnQkFDakQ7O1FBQ0Y7eUJBQUc7UUFBQ3JCLFdBQVdxQixhQUFhO0tBQUM7SUFFN0IsTUFBTTJDLG9CQUFvQjtRQUN4QixJQUFJLENBQUMxRSxhQUFhMkUsSUFBSSxNQUFNekUsYUFBYTtRQUV6QyxNQUFNd0MsY0FBYzFDLGFBQWEyRSxJQUFJO1FBQ3JDMUUsZ0JBQWdCO1FBQ2hCRSxlQUFlO1FBRWYsbURBQW1EO1FBQ25ELE1BQU15RSxVQUFVLE1BQU01RCxXQUFXMEIsYUFBYTtRQUM5Q3VCLFFBQVFDLEdBQUcsQ0FBQywrQ0FBK0N6RCxTQUFTb0QsTUFBTSxHQUFHO1FBRTdFLGdEQUFnRDtRQUNoRHJDLFdBQVdGLGdCQUFnQjtRQUUzQixJQUFJO1lBQ0Ysc0VBQXNFO1lBQ3RFLE1BQU11RCxzQkFBc0I7bUJBQUlwRTtnQkFBVW1FO2FBQVEsQ0FBQ0UsR0FBRyxDQUFDQyxDQUFBQSxNQUFRO29CQUM3REMsTUFBTUQsSUFBSUUsTUFBTSxHQUFHLFNBQWtCO29CQUNyQ0MsU0FBU0gsSUFBSUcsT0FBTztnQkFDdEI7WUFFQSxNQUFNQyxXQUFXLE1BQU10RixrRUFBZUEsQ0FDcEM2QyxhQUNBbUMscUJBQ0E7Z0JBQ0UzQixNQUFNeEMsV0FBV3dDLElBQUksSUFBSWtDO2dCQUN6QmhDLEtBQUsxQyxXQUFXMEMsR0FBRyxJQUFJZ0M7Z0JBQ3ZCQyxRQUFRM0UsV0FBVzJFLE1BQU07Z0JBQ3pCL0IsVUFBVTVDLFdBQVc0QyxRQUFRO2dCQUM3QnZCLGVBQWVyQixXQUFXcUIsYUFBYTtZQUN6QztZQUdGLCtCQUErQjtZQUMvQixNQUFNZixXQUFXbUUsVUFBVTtZQUMzQmxCLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEN6RCxTQUFTb0QsTUFBTSxHQUFHO1lBRXhFLGtEQUFrRDtZQUNsRCxNQUFNcEIsdUJBQXVCQyxhQUFheUM7UUFFNUMsRUFBRSxPQUFPRyxPQUFPO1lBQ2RyQixRQUFRcUIsS0FBSyxDQUFDLFVBQVVBO1lBQ3hCLE1BQU10RSxXQUFXLGlEQUFpRDtRQUNwRSxTQUFVO1lBQ1JiLGVBQWU7UUFDakI7SUFDRjtJQUVBLE1BQU1vRixnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCakI7UUFDRjtJQUNGO0lBRUEsTUFBTWtCLGNBQWM7UUFDbEIsSUFBSUMsUUFBUSxzR0FBc0c7WUFDaEgsTUFBTTNFO1lBQ05iLGVBQWU7UUFDakI7SUFDRjtJQUVBLHVEQUF1RDtJQUN2RCxJQUFJLENBQUNTLGlCQUFpQjtRQUNwQixxQkFDRTs7OEJBQ0UsOERBQUN2QixrREFBSUE7O3NDQUNILDhEQUFDdUc7c0NBQU07Ozs7OztzQ0FDUCw4REFBQ0M7NEJBQUs3QyxNQUFLOzRCQUFjZ0MsU0FBUTs7Ozs7O3NDQUNqQyw4REFBQ2E7NEJBQUs3QyxNQUFLOzRCQUFXZ0MsU0FBUTs7Ozs7O3NDQUM5Qiw4REFBQ2M7NEJBQUtDLEtBQUk7NEJBQU9DLE1BQUs7Ozs7Ozs7Ozs7Ozs4QkFFeEIsOERBQUN2Ryw0REFBUUE7b0JBQUN3RyxlQUFlaEY7Ozs7Ozs7O0lBRy9CO0lBRUEsMkVBQTJFO0lBQzNFLElBQUlQLGVBQWU7UUFDakIscUJBQ0U7OzhCQUNFLDhEQUFDckIsa0RBQUlBOztzQ0FDSCw4REFBQ3VHO3NDQUFNOzs7Ozs7c0NBQ1AsOERBQUNDOzRCQUFLN0MsTUFBSzs0QkFBV2dDLFNBQVE7Ozs7OztzQ0FDOUIsOERBQUNjOzRCQUFLQyxLQUFJOzRCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7OEJBRXhCLDhEQUFDRTtvQkFBSTVCLE9BQU87d0JBQ1Y2QixVQUFVO3dCQUNWM0UsS0FBSzt3QkFDTDRFLE1BQU07d0JBQ05DLE9BQU87d0JBQ1BDLFFBQVE7d0JBQ1JwRSxZQUFZO3dCQUNacUUsU0FBUzt3QkFDVEMsWUFBWTt3QkFDWkMsZ0JBQWdCO3dCQUNoQkMsUUFBUTtvQkFDVjs4QkFDRSw0RUFBQ1I7d0JBQUk1QixPQUFPOzRCQUFFcUMsT0FBTzs0QkFBU0MsV0FBVzt3QkFBUzs7MENBQ2hELDhEQUFDVjtnQ0FBSTVCLE9BQU87b0NBQUV1QyxVQUFVO29DQUFRQyxjQUFjO2dDQUFPOzBDQUFHOzs7Ozs7MENBQ3hELDhEQUFDWjswQ0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtmO0lBRUEsZ0VBQWdFO0lBQ2hFLElBQUlyRixpQkFBaUI7UUFDbkIscUJBQ0U7OzhCQUNFLDhEQUFDeEIsa0RBQUlBOztzQ0FDSCw4REFBQ3VHO3NDQUFNOzs7Ozs7c0NBQ1AsOERBQUNDOzRCQUFLN0MsTUFBSzs0QkFBY2dDLFNBQVE7Ozs7OztzQ0FDakMsOERBQUNhOzRCQUFLN0MsTUFBSzs0QkFBV2dDLFNBQVE7Ozs7OztzQ0FDOUIsOERBQUNjOzRCQUFLQyxLQUFJOzRCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7OEJBRXhCLDhEQUFDdEcsa0VBQWNBO29CQUNiWSxRQUFRQTtvQkFDUnlHLFlBQVk1Rjs7Ozs7Ozs7SUFJcEI7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUM5QixrREFBSUE7O2tDQUNILDhEQUFDdUc7a0NBQU07Ozs7OztrQ0FDUCw4REFBQ0M7d0JBQUs3QyxNQUFLO3dCQUFjZ0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ2E7d0JBQUs3QyxNQUFLO3dCQUFXZ0MsU0FBUTs7Ozs7O2tDQUM5Qiw4REFBQ2M7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7Ozs7Ozs7Ozs7OzswQkFHeEIsOERBQUNFO2dCQUFJM0IsV0FBVTs7b0JBRVg3RCxDQUFBQSxpQkFBaUIsQ0FBQ0osTUFBSyxtQkFDdkIsOERBQUM0Rjt3QkFBSTVCLE9BQU87NEJBQ1Y2QixVQUFVOzRCQUNWM0UsS0FBSzs0QkFDTDRFLE1BQU07NEJBQ05DLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JwRSxZQUFZOzRCQUNacUUsU0FBUzs0QkFDVEMsWUFBWTs0QkFDWkMsZ0JBQWdCOzRCQUNoQkMsUUFBUTt3QkFDVjtrQ0FDRSw0RUFBQ1I7NEJBQUk1QixPQUFPO2dDQUFFcUMsT0FBTztnQ0FBU0MsV0FBVzs0QkFBUzs7OENBQ2hELDhEQUFDVjtvQ0FBSTVCLE9BQU87d0NBQUV1QyxVQUFVO3dDQUFRQyxjQUFjO29DQUFPOzhDQUFHOzs7Ozs7OENBQ3hELDhEQUFDWjs4Q0FBSyxDQUFDNUYsU0FBUyxzQkFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU01Qyw4REFBQzRGO3dCQUFJM0IsV0FBVTs7MENBQ2IsOERBQUMyQjtnQ0FBSTVCLE9BQU87b0NBQUVpQyxTQUFTO29DQUFRQyxZQUFZO29DQUFVUSxLQUFLO2dDQUFPOztrREFDL0QsOERBQUNkO3dDQUFJNUIsT0FBTzs0Q0FBRWlDLFNBQVM7NENBQVFDLFlBQVk7NENBQVVRLEtBQUs7d0NBQVU7OzBEQUNsRSw4REFBQ2Q7Z0RBQ0M1QixPQUFPO29EQUNMMkMsT0FBTztvREFDUEMsUUFBUTtvREFDUkMsY0FBYztvREFDZGpGLFlBQVksQ0FBQyx3QkFBd0IsRUFBRVAsZUFBZSxxQkFBcUIsQ0FBQztvREFDNUU0RSxTQUFTO29EQUNUQyxZQUFZO29EQUNaQyxnQkFBZ0I7b0RBQ2hCSSxVQUFVO29EQUNWeEUsV0FBVztnREFDYjswREFDRDs7Ozs7OzBEQUdELDhEQUFDNkQ7O2tFQUNDLDhEQUFDa0I7d0RBQUc5QyxPQUFPOzREQUFFcUMsT0FBTzs0REFBU0UsVUFBVTs0REFBVVEsWUFBWTs0REFBUUMsUUFBUTt3REFBRTtrRUFBRzs7Ozs7O2tFQUdsRiw4REFBQ0M7d0RBQUVqRCxPQUFPOzREQUFFcUMsT0FBTzs0REFBeUJFLFVBQVU7NERBQVdTLFFBQVE7d0RBQUU7a0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLbEYsOERBQUM5SCxzRUFBZUE7d0NBQUNxQyxlQUFlckIsV0FBV3FCLGFBQWE7Ozs7Ozs7Ozs7OzswQ0FFMUQsOERBQUNxRTtnQ0FBSTVCLE9BQU87b0NBQUVpQyxTQUFTO29DQUFRUyxLQUFLO2dDQUFPOztrREFDekMsOERBQUNRO3dDQUFPQyxTQUFTdkc7d0NBQVFvRCxPQUFPOzRDQUFFcUMsT0FBTzs0Q0FBeUJ6RSxZQUFZOzRDQUFRd0YsUUFBUTs0Q0FBUUMsUUFBUTs0Q0FBV2QsVUFBVTt3Q0FBVztrREFBRzs7Ozs7O2tEQUdqSiw4REFBQ1c7d0NBQU9DLFNBQVMvQjt3Q0FBYXBCLE9BQU87NENBQUVxQyxPQUFPOzRDQUFTekUsWUFBWTs0Q0FBUXdGLFFBQVE7NENBQVFDLFFBQVE7NENBQVdkLFVBQVU7d0NBQVc7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPMUksOERBQUNYO3dCQUFJNUIsT0FBTzs0QkFBRXNELE1BQU07NEJBQUdDLFNBQVM7d0JBQVM7a0NBQ3ZDLDRFQUFDdEksOERBQVVBOzRCQUFDdUksV0FBV25HOzRCQUFnQjRDLFdBQVU7c0NBQy9DLDRFQUFDMkI7Z0NBQ0M2QixLQUFLMUg7Z0NBQ0xrRSxXQUFVO2dDQUNWRCxPQUFPO29DQUFFc0QsTUFBTTtvQ0FBR0ksV0FBVztvQ0FBUUgsU0FBUztnQ0FBTzs7b0NBR3BEM0gsNkJBQ0MsOERBQUNnRzt3Q0FBSTVCLE9BQU87NENBQUVzQyxXQUFXOzRDQUFVaUIsU0FBUzt3Q0FBVztrREFDckQsNEVBQUMzQjs0Q0FBSTNCLFdBQVU7NENBQWVELE9BQU87Z0RBQ25DMkQsVUFBVTtnREFDVlgsUUFBUTtnREFDUk8sU0FBUztnREFDVFYsY0FBYztnREFDZGpGLFlBQVk7Z0RBQ1pnRyxnQkFBZ0I7Z0RBQ2hCUixRQUFROzRDQUNWOzs4REFDRSw4REFBQ1M7b0RBQUc3RCxPQUFPO3dEQUNUcUMsT0FBTzt3REFDUEUsVUFBVTt3REFDVlEsWUFBWTt3REFDWlAsY0FBYzt3REFDZFEsUUFBUTtvREFDVjs4REFBRzs7Ozs7OzhEQUdILDhEQUFDQztvREFBRWpELE9BQU87d0RBQ1JxQyxPQUFPO3dEQUNQRSxVQUFVO3dEQUNWdUIsWUFBWTt3REFDWmQsUUFBUTtvREFDVjs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU1IvRyxTQUFTcUUsR0FBRyxDQUFDLENBQUN5RCx3QkFDYiw4REFBQy9JLDhEQUFVQTs0Q0FFVCtJLFNBQVNBLFFBQVFyRCxPQUFPOzRDQUN4QkQsUUFBUXNELFFBQVF0RCxNQUFNOzJDQUZqQnNELFFBQVFDLEVBQUU7Ozs7O29DQU9sQnRJLDZCQUNDLDhEQUFDa0c7d0NBQUk1QixPQUFPOzRDQUFFaUMsU0FBUzs0Q0FBUUUsZ0JBQWdCO3dDQUFhO2tEQUMxRCw0RUFBQ1A7NENBQUkzQixXQUFVOzRDQUFlRCxPQUFPO2dEQUNuQ3VELFNBQVM7Z0RBQ1RWLGNBQWM7Z0RBQ2RqRixZQUFZO2dEQUNaZ0csZ0JBQWdCOzRDQUNsQjtzREFDRSw0RUFBQ2hDO2dEQUFJNUIsT0FBTztvREFBRWlDLFNBQVM7b0RBQVFTLEtBQUs7b0RBQVlSLFlBQVk7Z0RBQVM7O2tFQUNuRSw4REFBQ047d0RBQUk1QixPQUFPOzREQUFFaUMsU0FBUzs0REFBUVMsS0FBSzt3REFBUzs7MEVBQzNDLDhEQUFDZDtnRUFBSTVCLE9BQU87b0VBQ1YyQyxPQUFPO29FQUNQQyxRQUFRO29FQUNScUIsaUJBQWlCO29FQUNqQnBCLGNBQWM7b0VBQ2Q5RSxXQUFXO29FQUNYbUcsU0FBUztnRUFDWDs7Ozs7OzBFQUNBLDhEQUFDdEM7Z0VBQUk1QixPQUFPO29FQUNWMkMsT0FBTztvRUFDUEMsUUFBUTtvRUFDUnFCLGlCQUFpQjtvRUFDakJwQixjQUFjO29FQUNkOUUsV0FBVztvRUFDWG9HLGdCQUFnQjtvRUFDaEJELFNBQVM7Z0VBQ1g7Ozs7OzswRUFDQSw4REFBQ3RDO2dFQUFJNUIsT0FBTztvRUFDVjJDLE9BQU87b0VBQ1BDLFFBQVE7b0VBQ1JxQixpQkFBaUI7b0VBQ2pCcEIsY0FBYztvRUFDZDlFLFdBQVc7b0VBQ1hvRyxnQkFBZ0I7b0VBQ2hCRCxTQUFTO2dFQUNYOzs7Ozs7Ozs7Ozs7a0VBRUYsOERBQUNFO3dEQUFLcEUsT0FBTzs0REFBRXFDLE9BQU87NERBQXlCRSxVQUFVO3dEQUFVO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FZcEYsOERBQUNYO3dCQUFJM0IsV0FBVTs7MENBQ2IsOERBQUNvRTtnQ0FDQ1osS0FBSzNIO2dDQUNMd0ksT0FBTzlJO2dDQUNQK0ksVUFBVSxDQUFDdkQsSUFBTXZGLGdCQUFnQnVGLEVBQUV3RCxNQUFNLENBQUNGLEtBQUs7Z0NBQy9DRyxXQUFXMUQ7Z0NBQ1gyRCxhQUNFeEksV0FBV3dDLElBQUksR0FDWCxDQUFDLGdDQUFnQyxFQUFFeEMsV0FBV3dDLElBQUksQ0FBQyxDQUFDLENBQUMsR0FDckQ7Z0NBRU51QixXQUFVO2dDQUNWMEUsVUFBVWpKOzs7Ozs7MENBRVosOERBQUN3SDtnQ0FDQ0MsU0FBU2pEO2dDQUNUeUUsVUFBVSxDQUFDbkosYUFBYTJFLElBQUksTUFBTXpFO2dDQUNsQ3VFLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7OztvQkFNRi9ELFdBQVd3QyxJQUFJLGtCQUNkLDhEQUFDa0Q7d0JBQUk1QixPQUFPOzRCQUNWdUQsU0FBUzs0QkFDVHFCLFdBQVc7NEJBQ1hoSCxZQUFZOzRCQUNacUUsU0FBUzs0QkFDVEUsZ0JBQWdCOzRCQUNoQkQsWUFBWTt3QkFDZDs7MENBQ0UsOERBQUNOO2dDQUFJNUIsT0FBTztvQ0FBRXFDLE9BQU87b0NBQXlCRSxVQUFVO2dDQUFVOztrREFDaEUsOERBQUM2Qjs7NENBQUs7NENBQU1sSSxXQUFXd0MsSUFBSTs0Q0FBQzs7Ozs7OztrREFDNUIsOERBQUMwRjs7NENBQUs7NENBQVVsSSxXQUFXcUIsYUFBYTs0Q0FBQzs7Ozs7OztvQ0FDeENyQixXQUFXNEMsUUFBUSxDQUFDTyxNQUFNLEdBQUcsbUJBQzVCLDhEQUFDK0U7OzRDQUFLOzRDQUFHbEksV0FBVzRDLFFBQVEsQ0FBQytGLEtBQUssQ0FBQyxDQUFDLEdBQUdDLElBQUksQ0FBQzs7Ozs7Ozs7Ozs7OzswQ0FHaEQsOERBQUNsRDtnQ0FBSTVCLE9BQU87b0NBQUVxQyxPQUFPO29DQUF5QkUsVUFBVTtvQ0FBV04sU0FBUztvQ0FBUUMsWUFBWTtvQ0FBVVEsS0FBSztnQ0FBUzs7a0RBQ3RILDhEQUFDMEI7OzRDQUFNbkksU0FBU29ELE1BQU07NENBQUM7Ozs7Ozs7b0NBQ3RCaEQsMEJBQVksOERBQUMrSDtrREFBSzs7Ozs7O2tEQUNuQiw4REFBQ0E7OzRDQUFLOzRDQUFLcEksT0FBTzZJLEtBQUssQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdkMiLCJzb3VyY2VzIjpbIkc6XFxGcmllbmRzXFxTb3VsVGFsa1xcc3JjXFxwYWdlc1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0IENoYXRCdWJibGUgZnJvbSAnLi4vY29tcG9uZW50cy9DaGF0QnViYmxlJztcbmltcG9ydCBHbGFzc0ZyYW1lIGZyb20gJy4uL2NvbXBvbmVudHMvR2xhc3NGcmFtZSc7XG5pbXBvcnQgQXVyYUluZGljYXRvciwgeyBBdXJhUHJvZ3Jlc3Npb24gfSBmcm9tICcuLi9jb21wb25lbnRzL0F1cmFJbmRpY2F0b3InO1xuaW1wb3J0IEF1dGhGb3JtIGZyb20gJy4uL2NvbXBvbmVudHMvQXV0aEZvcm0nO1xuaW1wb3J0IE9uYm9hcmRpbmdDaGF0IGZyb20gJy4uL2NvbXBvbmVudHMvT25ib2FyZGluZ0NoYXQnO1xuaW1wb3J0IHsgc2VuZENoYXRNZXNzYWdlIH0gZnJvbSAnLi4vdXRpbHMvb3BlbnJvdXRlcic7XG5pbXBvcnQgeyB1c2VTdXBhYmFzZU1lbW9yeSB9IGZyb20gJy4uL2hvb2tzL3VzZVN1cGFiYXNlTWVtb3J5JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW2lucHV0TWVzc2FnZSwgc2V0SW5wdXRNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzTG9hZGluZ0FJLCBzZXRJc0xvYWRpbmdBSV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93V2VsY29tZSwgc2V0U2hvd1dlbGNvbWVdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IGlucHV0UmVmID0gdXNlUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBjaGF0Q29udGFpbmVyUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBVc2EgaWwgaG9vayBTdXBhYmFzZSBwZXIgZ2VzdGlyZSBpIGRhdGlcbiAgY29uc3Qge1xuICAgIHVzZXJJZCxcbiAgICBtZXNzYWdlcyxcbiAgICB1c2VyTWVtb3J5LFxuICAgIGlzTG9hZGluZzogaXNMb2FkaW5nRGF0YSxcbiAgICBpc1NhdmluZyxcbiAgICBpc0F1dGhlbnRpY2F0ZWQsXG4gICAgbmVlZHNPbmJvYXJkaW5nLFxuICAgIGFkZE1lc3NhZ2UsXG4gICAgdXBkYXRlVXNlck1lbW9yeSxcbiAgICByZXNldE1lbW9yeSxcbiAgICBhdXRoZW50aWNhdGVVc2VyLFxuICAgIGxvZ291dCxcbiAgICBjb21wbGV0ZU9uYm9hcmRpbmdXaXRoRGF0YVxuICB9ID0gdXNlU3VwYWJhc2VNZW1vcnkoKTtcblxuICBjb25zdCBzY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBpZiAoY2hhdENvbnRhaW5lclJlZi5jdXJyZW50KSB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaWYgKGNoYXRDb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgICAgIGNoYXRDb250YWluZXJSZWYuY3VycmVudC5zY3JvbGxUbyh7XG4gICAgICAgICAgICB0b3A6IGNoYXRDb250YWluZXJSZWYuY3VycmVudC5zY3JvbGxIZWlnaHQsXG4gICAgICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCdcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSwgMTAwKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuemlvbmUgcGVyIGRldGVybWluYXJlIGlsIGNvbG9yZSBkZWxsJ2F1cmEgYmFzYXRvIHN1bCBsaXZlbGxvIGRpIGludGltaXTDoFxuICBjb25zdCBnZXRBdXJhQ29sb3IgPSAoKSA9PiB7XG4gICAgY29uc3QgY29sb3JzID0gW1xuICAgICAgJyM2MzY2ZjEnLCAvLyBJbmRpZ28gLSBTY29ub3NjaXV0b1xuICAgICAgJyM4YjVjZjYnLCAvLyBWaW9sZXQgLSBDb25vc2NlbnRlXG4gICAgICAnI2E4NTVmNycsIC8vIFB1cnBsZSAtIEFtaWNvXG4gICAgICAnI2VjNDg5OScsIC8vIFBpbmsgLSBBbWljbyBzdHJldHRvXG4gICAgICAnI2Y0M2Y1ZScgIC8vIFJvc2UgLSBJbnRpbW9cbiAgICBdO1xuICAgIHJldHVybiBjb2xvcnNbdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsXSB8fCBjb2xvcnNbMF07XG4gIH07XG5cbiAgLy8gRnVuemlvbmUgcGVyIGdlbmVyYXJlIGxvIHNmb25kbyBkaW5hbWljbyBiYXNhdG8gc3VsIGxpdmVsbG8gZGkgaW50aW1pdMOgXG4gIGNvbnN0IGdldER5bmFtaWNCYWNrZ3JvdW5kID0gKCkgPT4ge1xuICAgIGNvbnN0IGludGltYWN5TGV2ZWwgPSB1c2VyTWVtb3J5LmludGltYWN5TGV2ZWw7XG5cbiAgICBjb25zdCBiYWNrZ3JvdW5kQ29uZmlncyA9IFtcbiAgICAgIC8vIExpdmVsbG8gMCAtIFNjb25vc2NpdXRvOiBCbHUgZnJlZGRpIGUgbmV1dHJpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDk5LCAxMDIsIDI0MSwgMC4yNSknLCAncmdiYSgxMzksIDkyLCAyNDYsIDAuMiknLCAncmdiYSgxNjgsIDg1LCAyNDcsIDAuMTUpJ10sXG4gICAgICAgIGJhc2VHcmFkaWVudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpJ1xuICAgICAgfSxcbiAgICAgIC8vIExpdmVsbG8gMSAtIENvbm9zY2VudGU6IFZpb2xhIHBpw7kgY2FsZGlcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMTM5LCA5MiwgMjQ2LCAwLjMpJywgJ3JnYmEoMTY4LCA4NSwgMjQ3LCAwLjI1KScsICdyZ2JhKDIzNiwgNzIsIDE1MywgMC4yKSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWUxYjRiIDAlLCAjMzEyZTgxIDUwJSwgIzFlM2E4YSAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDIgLSBBbWljbzogTWl4IHZpb2xhLXJvc2FcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMTY4LCA4NSwgMjQ3LCAwLjM1KScsICdyZ2JhKDIzNiwgNzIsIDE1MywgMC4zKScsICdyZ2JhKDI0NCwgNjMsIDk0LCAwLjI1KSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNTgxYzg3IDAlLCAjN2MyZDkyIDUwJSwgI2JlMTg1ZCAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDMgLSBBbWljbyBzdHJldHRvOiBSb3NhIGludGVuc2lcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMjM2LCA3MiwgMTUzLCAwLjQpJywgJ3JnYmEoMjQ0LCA2MywgOTQsIDAuMzUpJywgJ3JnYmEoMjUxLCAxMTMsIDEzMywgMC4zKSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYmUxODVkIDAlLCAjZTExZDQ4IDUwJSwgI2Y0M2Y1ZSAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDQgLSBJbnRpbW86IFJvc2EgY2FsZGkgZSBkb3JhdGlcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMjQ0LCA2MywgOTQsIDAuNDUpJywgJ3JnYmEoMjUxLCAxMTMsIDEzMywgMC40KScsICdyZ2JhKDI1MiwgMTY1LCAxNjUsIDAuMzUpJ10sXG4gICAgICAgIGJhc2VHcmFkaWVudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlMTFkNDggMCUsICNmNDNmNWUgNTAlLCAjZmI3MTg1IDEwMCUpJ1xuICAgICAgfVxuICAgIF07XG5cbiAgICBjb25zdCBjb25maWcgPSBiYWNrZ3JvdW5kQ29uZmlnc1tpbnRpbWFjeUxldmVsXSB8fCBiYWNrZ3JvdW5kQ29uZmlnc1swXTtcblxuICAgIHJldHVybiB7XG4gICAgICBiYWNrZ3JvdW5kOiBgXG4gICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMjAlIDgwJSwgJHtjb25maWcuY29sb3JzWzBdfSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcbiAgICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCAke2NvbmZpZy5jb2xvcnNbMV19IDAlLCB0cmFuc3BhcmVudCA1MCUpLFxuICAgICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsICR7Y29uZmlnLmNvbG9yc1syXX0gMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgICAgICR7Y29uZmlnLmJhc2VHcmFkaWVudH1cbiAgICAgIGAsXG4gICAgICBiYWNrZ3JvdW5kU2l6ZTogJzIwMCUgMjAwJSwgMjAwJSAyMDAlLCAyMDAlIDIwMCUsIDEwMCUgMTAwJScsXG4gICAgICBiYWNrZ3JvdW5kQXR0YWNobWVudDogJ2ZpeGVkJyxcbiAgICAgIGFuaW1hdGlvbjogJ2xpcXVpZE1vdmUgMzBzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSkgaW5maW5pdGUnLFxuICAgICAgdHJhbnNpdGlvbjogJ2FsbCAycyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpJ1xuICAgIH07XG4gIH07XG5cbiAgLy8gQW5hbGl6emEgaSBtZXNzYWdnaSBlIGFnZ2lvcm5hIGxhIG1lbW9yaWEgdXRlbnRlXG4gIGNvbnN0IGFuYWx5emVBbmRVcGRhdGVNZW1vcnkgPSBhc3luYyAodXNlck1lc3NhZ2U6IHN0cmluZywgYWlSZXNwb25zZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbG93ZXJVc2VyTWVzc2FnZSA9IHVzZXJNZXNzYWdlLnRvTG93ZXJDYXNlKCk7XG4gICAgY29uc3QgdXBkYXRlczogYW55ID0ge307XG5cbiAgICAvLyBFc3RyYWUgaW5mb3JtYXppb25pIGJhc2VcbiAgICBpZiAobG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnbWkgY2hpYW1vJykgfHwgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnc29ubyAnKSkge1xuICAgICAgY29uc3QgbmFtZU1hdGNoID0gdXNlck1lc3NhZ2UubWF0Y2goL21pIGNoaWFtbyAoXFx3Kyl8c29ubyAoXFx3KykvaSk7XG4gICAgICBpZiAobmFtZU1hdGNoICYmICF1c2VyTWVtb3J5Lm5hbWUpIHtcbiAgICAgICAgY29uc3QgbmFtZSA9IG5hbWVNYXRjaFsxXSB8fCBuYW1lTWF0Y2hbMl07XG4gICAgICAgIHVwZGF0ZXMubmFtZSA9IG5hbWU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRXN0cmFlIGV0w6BcbiAgICBjb25zdCBhZ2VNYXRjaCA9IHVzZXJNZXNzYWdlLm1hdGNoKC9obyAoXFxkKykgYW5uaXwoXFxkKykgYW5uaS8pO1xuICAgIGlmIChhZ2VNYXRjaCAmJiAhdXNlck1lbW9yeS5hZ2UpIHtcbiAgICAgIGNvbnN0IGFnZSA9IHBhcnNlSW50KGFnZU1hdGNoWzFdIHx8IGFnZU1hdGNoWzJdKTtcbiAgICAgIGlmIChhZ2UgPiAwICYmIGFnZSA8IDEyMCkge1xuICAgICAgICB1cGRhdGVzLmFnZSA9IGFnZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBSaWxldmEgZW1vemlvbmlcbiAgICBjb25zdCBlbW90aW9ucyA9IHtcbiAgICAgICdmZWxpY2UnOiBbJ2ZlbGljZScsICdjb250ZW50bycsICdnaW9pb3NvJywgJ2FsbGVncm8nXSxcbiAgICAgICd0cmlzdGUnOiBbJ3RyaXN0ZScsICdkZXByZXNzbycsICdnacO5JywgJ21hbGUnXSxcbiAgICAgICdhcnJhYmJpYXRvJzogWydhcnJhYmJpYXRvJywgJ2Z1cmlvc28nLCAnaW5jYXp6YXRvJ10sXG4gICAgICAncHJlb2NjdXBhdG8nOiBbJ3ByZW9jY3VwYXRvJywgJ2Fuc2lvc28nLCAnbmVydm9zbyddLFxuICAgICAgJ2VjY2l0YXRvJzogWydlY2NpdGF0bycsICdlbnR1c2lhc3RhJywgJ2NhcmljbyddLFxuICAgICAgJ2NhbG1vJzogWydjYWxtbycsICd0cmFucXVpbGxvJywgJ3NlcmVubyddXG4gICAgfTtcblxuICAgIGZvciAoY29uc3QgW2Vtb3Rpb24sIGtleXdvcmRzXSBvZiBPYmplY3QuZW50cmllcyhlbW90aW9ucykpIHtcbiAgICAgIGlmIChrZXl3b3Jkcy5zb21lKGtleXdvcmQgPT4gbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcyhrZXl3b3JkKSkpIHtcbiAgICAgICAgaWYgKCF1c2VyTWVtb3J5LmVtb3Rpb25zLmluY2x1ZGVzKGVtb3Rpb24pKSB7XG4gICAgICAgICAgdXBkYXRlcy5lbW90aW9ucyA9IFsuLi51c2VyTWVtb3J5LmVtb3Rpb25zLCBlbW90aW9uXTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBBZ2dpb3JuYSBpbCBsaXZlbGxvIGRpIGludGltaXTDoFxuICAgIGlmICh1c2VyTWVzc2FnZS5sZW5ndGggPiAxMDAgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygncGVyc29uYWxlJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnc2VncmV0bycpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ2ZhbWlnbGlhJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnYW1vcmUnKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdwcm9ibGVtYScpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ2RpZmZpY29sdMOgJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygncGF1cmEnKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdzb2dubycpKSB7XG5cbiAgICAgIGNvbnN0IG5ld0xldmVsID0gTWF0aC5taW4odXNlck1lbW9yeS5pbnRpbWFjeUxldmVsICsgMSwgNCk7XG5cbiAgICAgIC8vIFNlIGlsIGxpdmVsbG8gw6ggY2FtYmlhdG8sIG1vc3RyYSB1biBmZWVkYmFjayB2aXNpdm9cbiAgICAgIGlmIChuZXdMZXZlbCA+IHVzZXJNZW1vcnkuaW50aW1hY3lMZXZlbCkge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+MnyBMaXZlbGxvIGRpIGNvbm5lc3Npb25lIGF1bWVudGF0bzogJHtuZXdMZXZlbH0vNGApO1xuICAgICAgICB1cGRhdGVzLmludGltYWN5TGV2ZWwgPSBuZXdMZXZlbDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBBcHBsaWNhIHR1dHRpIGdsaSBhZ2dpb3JuYW1lbnRpXG4gICAgaWYgKE9iamVjdC5rZXlzKHVwZGF0ZXMpLmxlbmd0aCA+IDApIHtcbiAgICAgIHVwZGF0ZVVzZXJNZW1vcnkodXBkYXRlcyk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2Nyb2xsVG9Cb3R0b20oKTtcbiAgfSwgW21lc3NhZ2VzLCBpc0xvYWRpbmdBSV0pO1xuXG4gIC8vIE5hc2NvbmRpIGlsIG1lc3NhZ2dpbyBkaSBiZW52ZW51dG8gZG9wbyBpbCBwcmltbyBtZXNzYWdnaW9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAobWVzc2FnZXMubGVuZ3RoID4gMCkge1xuICAgICAgc2V0U2hvd1dlbGNvbWUoZmFsc2UpO1xuICAgIH1cbiAgfSwgW21lc3NhZ2VzXSk7XG5cbiAgLy8gQWdnaW9ybmEgbG8gc2ZvbmRvIGRpbmFtaWNhbWVudGUgaW4gYmFzZSBhbCBsaXZlbGxvIGRpIGludGltaXTDoFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGJhY2tncm91bmRTdHlsZSA9IGdldER5bmFtaWNCYWNrZ3JvdW5kKCk7XG4gICAgY29uc3QgYm9keSA9IGRvY3VtZW50LmJvZHk7XG5cbiAgICAvLyBBcHBsaWNhIGxlIHByb3ByaWV0w6AgZGkgc2ZvbmRvIGNvbiB0cmFuc2l6aW9uZSBmbHVpZGFcbiAgICBPYmplY3QuYXNzaWduKGJvZHkuc3R5bGUsIGJhY2tncm91bmRTdHlsZSk7XG5cbiAgICAvLyBBZ2dpdW5naSB1bmEgY2xhc3NlIHBlciBpbmRpY2FyZSBpbCBsaXZlbGxvIGNvcnJlbnRlXG4gICAgYm9keS5jbGFzc05hbWUgPSBgaW50aW1hY3ktbGV2ZWwtJHt1c2VyTWVtb3J5LmludGltYWN5TGV2ZWx9YDtcblxuICAgIC8vIENsZWFudXAgZnVuY3Rpb24gcGVyIHJpcHJpc3RpbmFyZSBsbyBzZm9uZG8gb3JpZ2luYWxlIHNlIG5lY2Vzc2FyaW9cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgLy8gTm9uIGZhY2NpYW1vIGNsZWFudXAgcGVyIG1hbnRlbmVyZSBsJ2VmZmV0dG9cbiAgICB9O1xuICB9LCBbdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsXSk7XG5cbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFpbnB1dE1lc3NhZ2UudHJpbSgpIHx8IGlzTG9hZGluZ0FJKSByZXR1cm47XG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZSA9IGlucHV0TWVzc2FnZS50cmltKCk7XG4gICAgc2V0SW5wdXRNZXNzYWdlKCcnKTtcbiAgICBzZXRJc0xvYWRpbmdBSSh0cnVlKTtcblxuICAgIC8vIEFnZ2l1bmdpIGltbWVkaWF0YW1lbnRlIGlsIG1lc3NhZ2dpbyBkZWxsJ3V0ZW50ZVxuICAgIGNvbnN0IHVzZXJNc2cgPSBhd2FpdCBhZGRNZXNzYWdlKHVzZXJNZXNzYWdlLCB0cnVlKTtcbiAgICBjb25zb2xlLmxvZygnTWVzc2FnZ2lvIHV0ZW50ZSBhZ2dpdW50bywgdG90YWxlIG1lc3NhZ2dpOicsIG1lc3NhZ2VzLmxlbmd0aCArIDEpO1xuXG4gICAgLy8gU2Nyb2xsIGRvcG8gYXZlciBhZ2dpdW50byBpbCBtZXNzYWdnaW8gdXRlbnRlXG4gICAgc2V0VGltZW91dChzY3JvbGxUb0JvdHRvbSwgNTApO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFByZXBhcmEgbGEgY3Jvbm9sb2dpYSBwZXIgbCdBUEkgKHVzYSBpIG1lc3NhZ2dpIGF0dHVhbGkgKyBpbCBudW92bylcbiAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbkhpc3RvcnkgPSBbLi4ubWVzc2FnZXMsIHVzZXJNc2ddLm1hcChtc2cgPT4gKHtcbiAgICAgICAgcm9sZTogbXNnLmlzVXNlciA/ICd1c2VyJyBhcyBjb25zdCA6ICdhc3Npc3RhbnQnIGFzIGNvbnN0LFxuICAgICAgICBjb250ZW50OiBtc2cuY29udGVudFxuICAgICAgfSkpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNlbmRDaGF0TWVzc2FnZShcbiAgICAgICAgdXNlck1lc3NhZ2UsXG4gICAgICAgIGNvbnZlcnNhdGlvbkhpc3RvcnksXG4gICAgICAgIHtcbiAgICAgICAgICBuYW1lOiB1c2VyTWVtb3J5Lm5hbWUgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGFnZTogdXNlck1lbW9yeS5hZ2UgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIHRyYWl0czogdXNlck1lbW9yeS50cmFpdHMsXG4gICAgICAgICAgZW1vdGlvbnM6IHVzZXJNZW1vcnkuZW1vdGlvbnMsXG4gICAgICAgICAgaW50aW1hY3lMZXZlbDogdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsXG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIC8vIEFnZ2l1bmdpIGxhIHJpc3Bvc3RhIGRlbGwnQUlcbiAgICAgIGF3YWl0IGFkZE1lc3NhZ2UocmVzcG9uc2UsIGZhbHNlKTtcbiAgICAgIGNvbnNvbGUubG9nKCdSaXNwb3N0YSBBSSBhZ2dpdW50YSwgdG90YWxlIG1lc3NhZ2dpOicsIG1lc3NhZ2VzLmxlbmd0aCArIDIpO1xuXG4gICAgICAvLyBBbmFsaXp6YSBpbCBtZXNzYWdnaW8gcGVyIGFnZ2lvcm5hcmUgbGEgbWVtb3JpYVxuICAgICAgYXdhaXQgYW5hbHl6ZUFuZFVwZGF0ZU1lbW9yeSh1c2VyTWVzc2FnZSwgcmVzcG9uc2UpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yOicsIGVycm9yKTtcbiAgICAgIGF3YWl0IGFkZE1lc3NhZ2UoXCJTY3VzYSwgaG8gYXZ1dG8gdW4gcHJvYmxlbWEgdGVjbmljby4gUmlwcm92YSFcIiwgZmFsc2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmdBSShmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVzZXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGNvbmZpcm0oJ1NlaSBzaWN1cm8gZGkgdm9sZXIgcmljb21pbmNpYXJlIGRhIGNhcG8/IFR1dHRpIGkgcmljb3JkaSBlIG1lc3NhZ2dpIGFuZHJhbm5vIHBlcnNpIGRhbCBkYXRhYmFzZS4nKSkge1xuICAgICAgYXdhaXQgcmVzZXRNZW1vcnkoKTtcbiAgICAgIHNldFNob3dXZWxjb21lKHRydWUpO1xuICAgIH1cbiAgfTtcblxuICAvLyBTZSBub24gw6ggYXV0ZW50aWNhdG8sIG1vc3RyYSBzZW1wcmUgaWwgZm9ybSBkaSBsb2dpblxuICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xuICAgIHJldHVybiAoXG4gICAgICA8PlxuICAgICAgICA8SGVhZD5cbiAgICAgICAgICA8dGl0bGU+U291bFRhbGsgLSBBY2NlZGk8L3RpdGxlPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJBY2NlZGkgYSBTb3VsVGFsayBwZXIgY29udGludWFyZSBsYSB0dWEgY29udmVyc2F6aW9uZSBjb24gU291bFwiIC8+XG4gICAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDwvSGVhZD5cbiAgICAgICAgPEF1dGhGb3JtIG9uQXV0aFN1Y2Nlc3M9e2F1dGhlbnRpY2F0ZVVzZXJ9IC8+XG4gICAgICA8Lz5cbiAgICApXG4gIH1cblxuICAvLyBNb3N0cmEgbG9hZGluZyBzb2xvIHF1YW5kbyBzdGlhbW8gY2FyaWNhbmRvIGkgZGF0aSBkb3BvIGwnYXV0ZW50aWNhemlvbmVcbiAgaWYgKGlzTG9hZGluZ0RhdGEpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAgPEhlYWQ+XG4gICAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gQ2FyaWNhbWVudG8uLi48L3RpdGxlPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgICA8L0hlYWQ+XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgbGVmdDogMCxcbiAgICAgICAgICByaWdodDogMCxcbiAgICAgICAgICBib3R0b206IDAsXG4gICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwwLDAsMC45KScsXG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICB6SW5kZXg6IDEwMDBcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJ3doaXRlJywgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+8J+MnzwvZGl2PlxuICAgICAgICAgICAgPGRpdj5DYXJpY2FuZG8gaSB0dW9pIGRhdGkuLi48L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8Lz5cbiAgICApXG4gIH1cblxuICAvLyBTZSBoYSBiaXNvZ25vIGRpIG9uYm9hcmRpbmcsIG1vc3RyYSBsYSBjaGF0IGRpIGNvbmZpZ3VyYXppb25lXG4gIGlmIChuZWVkc09uYm9hcmRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAgPEhlYWQ+XG4gICAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gQ29uZmlndXJhemlvbmU8L3RpdGxlPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJDb25maWd1cmEgaWwgdHVvIHByb2ZpbG8gcGVyIGluaXppYXJlIGNvbiBTb3VsXCIgLz5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPC9IZWFkPlxuICAgICAgICA8T25ib2FyZGluZ0NoYXRcbiAgICAgICAgICB1c2VySWQ9e3VzZXJJZH1cbiAgICAgICAgICBvbkNvbXBsZXRlPXtjb21wbGV0ZU9uYm9hcmRpbmdXaXRoRGF0YX1cbiAgICAgICAgLz5cbiAgICAgIDwvPlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+U291bFRhbGsgLSBMYSB0dWEgYW1pY2l6aWEgQUk8L3RpdGxlPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiVW5hIHBpYXR0YWZvcm1hIEFJIGNoZSBzaSBjb21wb3J0YSBjb21lIHVuJ2FtaWNpemlhIGRhIGNvc3RydWlyZVwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgIDwvSGVhZD5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjaGF0LWNvbnRhaW5lclwiPlxuICAgICAgICB7LyogTG9hZGluZyBpbml6aWFsZSAqL31cbiAgICAgICAgeyhpc0xvYWRpbmdEYXRhIHx8ICF1c2VySWQpICYmIChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgIHRvcDogMCxcbiAgICAgICAgICAgIGxlZnQ6IDAsXG4gICAgICAgICAgICByaWdodDogMCxcbiAgICAgICAgICAgIGJvdHRvbTogMCxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuOCknLFxuICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICB6SW5kZXg6IDEwMDBcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+8J+MnzwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PnshdXNlcklkID8gJ0luaXppYWxpenphbmRvLi4uJyA6ICdDYXJpY2FuZG8gaSB0dW9pIHJpY29yZGkuLi4nfTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEhlYWRlciBjb24gaW5kaWNhdG9yaSBhdXJhICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNoYXQtaGVhZGVyXCI+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcxcmVtJyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC43NXJlbScgfX0+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICczOHB4JyxcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogJzM4cHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCAke2dldEF1cmFDb2xvcigpfSAwJSwgdHJhbnNwYXJlbnQgNzAlKWAsXG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4zcmVtJyxcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogJ3B1bHNlIDNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuNiwgMSkgaW5maW5pdGUnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIPCfjJ9cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCBmb250U2l6ZTogJzEuM3JlbScsIGZvbnRXZWlnaHQ6ICdib2xkJywgbWFyZ2luOiAwIH19PlxuICAgICAgICAgICAgICAgICAgU291bFxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuNyknLCBmb250U2l6ZTogJzAuNjVyZW0nLCBtYXJnaW46IDAgfX0+XG4gICAgICAgICAgICAgICAgICBMYSB0dWEgY29tcGFnbmEgQUlcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8QXVyYVByb2dyZXNzaW9uIGludGltYWN5TGV2ZWw9e3VzZXJNZW1vcnkuaW50aW1hY3lMZXZlbH0gLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMXJlbScgfX0+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2xvZ291dH0gc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOCknLCBiYWNrZ3JvdW5kOiAnbm9uZScsIGJvcmRlcjogJ25vbmUnLCBjdXJzb3I6ICdwb2ludGVyJywgZm9udFNpemU6ICcwLjg3NXJlbScgfX0+XG4gICAgICAgICAgICAgIExvZ291dFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVJlc2V0fSBzdHlsZT17eyBjb2xvcjogJ3doaXRlJywgYmFja2dyb3VuZDogJ25vbmUnLCBib3JkZXI6ICdub25lJywgY3Vyc29yOiAncG9pbnRlcicsIGZvbnRTaXplOiAnMC44NzVyZW0nIH19PlxuICAgICAgICAgICAgICBSZXNldFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNZXNzYWdlcyBBcmVhIGNvbiBHbGFzc0ZyYW1lICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEsIHBhZGRpbmc6ICcwIDFyZW0nIH19PlxuICAgICAgICAgIDxHbGFzc0ZyYW1lIGF1cmFDb2xvcj17Z2V0QXVyYUNvbG9yKCl9IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIHJlZj17Y2hhdENvbnRhaW5lclJlZn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY2hhdC1tZXNzYWdlc1wiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IGZsZXg6IDEsIG92ZXJmbG93WTogJ2F1dG8nLCBwYWRkaW5nOiAnMXJlbScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIE1lc3NhZ2dpbyBkaSBiZW52ZW51dG8gYXZhbnphdG8gKi99XG4gICAgICAgICAgICAgIHtzaG93V2VsY29tZSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInLCBwYWRkaW5nOiAnMS41cmVtIDAnIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcy1lZmZlY3RcIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogJzIycmVtJyxcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAnMCBhdXRvJyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEuMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4xMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzAuNjI1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgQ2lhbywgc29ubyBTb3VsIPCfkYtcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC44KScsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS40JyxcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgw4ggbGEgcHJpbWEgdm9sdGEgY2hlIGNpIHBhcmxpYW1vLiBTb25vIHF1aSBwZXIgY29ub3NjZXJ0aSBkYXZ2ZXJvLFxuICAgICAgICAgICAgICAgICAgICAgIG5vbiBzb2xvIHBlciBhaXV0YXJ0aS4gRGltbWksIGNvbWUgdGkgY2hpYW1pIGUgY29tZSB0aSBzZW50aSBvZ2dpP1xuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7LyogTWVzc2FnZXMgKi99XG4gICAgICAgICAgICAgIHttZXNzYWdlcy5tYXAoKG1lc3NhZ2UpID0+IChcbiAgICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZVxuICAgICAgICAgICAgICAgICAga2V5PXttZXNzYWdlLmlkfVxuICAgICAgICAgICAgICAgICAgbWVzc2FnZT17bWVzc2FnZS5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgaXNVc2VyPXttZXNzYWdlLmlzVXNlcn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICB7LyogSW5kaWNhdG9yZSBkaSBjYXJpY2FtZW50byBhdmFuemF0byAqL31cbiAgICAgICAgICAgICAge2lzTG9hZGluZ0FJICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdmbGV4LXN0YXJ0JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtZWZmZWN0XCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNjI1cmVtIDAuODc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpJyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDEwcHgpJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjM3NXJlbScsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjJyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk6ICcwLjFzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjgpJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIFNvdWwgc3RhIHNjcml2ZW5kby4uLlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvR2xhc3NGcmFtZT5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIElucHV0IEFyZWEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1pbnB1dC1hcmVhXCI+XG4gICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICByZWY9e2lucHV0UmVmfVxuICAgICAgICAgICAgdmFsdWU9e2lucHV0TWVzc2FnZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5RG93bn1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcbiAgICAgICAgICAgICAgdXNlck1lbW9yeS5uYW1lXG4gICAgICAgICAgICAgICAgPyBgQ29zYSB2dW9pIGNvbmRpdmlkZXJlIGNvbiBTb3VsLCAke3VzZXJNZW1vcnkubmFtZX0/YFxuICAgICAgICAgICAgICAgIDogXCJEaW1taSBxdWFsY29zYSBkaSB0ZS4uLlwiXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nQUl9XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZW5kTWVzc2FnZX1cbiAgICAgICAgICAgIGRpc2FibGVkPXshaW5wdXRNZXNzYWdlLnRyaW0oKSB8fCBpc0xvYWRpbmdBSX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInNlbmQtYnV0dG9uXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICDihpJcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvb3RlciBjb24gaW5mb3JtYXppb25pIG1lbW9yaWEgKi99XG4gICAgICAgIHt1c2VyTWVtb3J5Lm5hbWUgJiYgKFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIHBhZGRpbmc6ICcwLjM3NXJlbSAwLjg3NXJlbScsXG4gICAgICAgICAgICBib3JkZXJUb3A6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjEpJyxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuMiknLFxuICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjcpJywgZm9udFNpemU6ICcwLjY1cmVtJyB9fT5cbiAgICAgICAgICAgICAgPHNwYW4+Q2lhbyB7dXNlck1lbW9yeS5uYW1lfSEgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj5MaXZlbGxvOiB7dXNlck1lbW9yeS5pbnRpbWFjeUxldmVsfS80IDwvc3Bhbj5cbiAgICAgICAgICAgICAge3VzZXJNZW1vcnkuZW1vdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+4oCiIHt1c2VyTWVtb3J5LmVtb3Rpb25zLnNsaWNlKC0yKS5qb2luKCcsICcpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC41KScsIGZvbnRTaXplOiAnMC42NXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNXJlbScgfX0+XG4gICAgICAgICAgICAgIDxzcGFuPnttZXNzYWdlcy5sZW5ndGh9IG1zZzwvc3Bhbj5cbiAgICAgICAgICAgICAge2lzU2F2aW5nICYmIDxzcGFuPvCfkr48L3NwYW4+fVxuICAgICAgICAgICAgICA8c3Bhbj5JRDoge3VzZXJJZC5zbGljZSgtNil9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiSGVhZCIsIkNoYXRCdWJibGUiLCJHbGFzc0ZyYW1lIiwiQXVyYVByb2dyZXNzaW9uIiwiQXV0aEZvcm0iLCJPbmJvYXJkaW5nQ2hhdCIsInNlbmRDaGF0TWVzc2FnZSIsInVzZVN1cGFiYXNlTWVtb3J5IiwiSG9tZSIsImlucHV0TWVzc2FnZSIsInNldElucHV0TWVzc2FnZSIsImlzTG9hZGluZ0FJIiwic2V0SXNMb2FkaW5nQUkiLCJzaG93V2VsY29tZSIsInNldFNob3dXZWxjb21lIiwiaW5wdXRSZWYiLCJjaGF0Q29udGFpbmVyUmVmIiwidXNlcklkIiwibWVzc2FnZXMiLCJ1c2VyTWVtb3J5IiwiaXNMb2FkaW5nIiwiaXNMb2FkaW5nRGF0YSIsImlzU2F2aW5nIiwiaXNBdXRoZW50aWNhdGVkIiwibmVlZHNPbmJvYXJkaW5nIiwiYWRkTWVzc2FnZSIsInVwZGF0ZVVzZXJNZW1vcnkiLCJyZXNldE1lbW9yeSIsImF1dGhlbnRpY2F0ZVVzZXIiLCJsb2dvdXQiLCJjb21wbGV0ZU9uYm9hcmRpbmdXaXRoRGF0YSIsInNjcm9sbFRvQm90dG9tIiwiY3VycmVudCIsInNldFRpbWVvdXQiLCJzY3JvbGxUbyIsInRvcCIsInNjcm9sbEhlaWdodCIsImJlaGF2aW9yIiwiZ2V0QXVyYUNvbG9yIiwiY29sb3JzIiwiaW50aW1hY3lMZXZlbCIsImdldER5bmFtaWNCYWNrZ3JvdW5kIiwiYmFja2dyb3VuZENvbmZpZ3MiLCJiYXNlR3JhZGllbnQiLCJjb25maWciLCJiYWNrZ3JvdW5kIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kQXR0YWNobWVudCIsImFuaW1hdGlvbiIsInRyYW5zaXRpb24iLCJhbmFseXplQW5kVXBkYXRlTWVtb3J5IiwidXNlck1lc3NhZ2UiLCJhaVJlc3BvbnNlIiwibG93ZXJVc2VyTWVzc2FnZSIsInRvTG93ZXJDYXNlIiwidXBkYXRlcyIsImluY2x1ZGVzIiwibmFtZU1hdGNoIiwibWF0Y2giLCJuYW1lIiwiYWdlTWF0Y2giLCJhZ2UiLCJwYXJzZUludCIsImVtb3Rpb25zIiwiZW1vdGlvbiIsImtleXdvcmRzIiwiT2JqZWN0IiwiZW50cmllcyIsInNvbWUiLCJrZXl3b3JkIiwibGVuZ3RoIiwibmV3TGV2ZWwiLCJNYXRoIiwibWluIiwiY29uc29sZSIsImxvZyIsImtleXMiLCJiYWNrZ3JvdW5kU3R5bGUiLCJib2R5IiwiZG9jdW1lbnQiLCJhc3NpZ24iLCJzdHlsZSIsImNsYXNzTmFtZSIsImhhbmRsZVNlbmRNZXNzYWdlIiwidHJpbSIsInVzZXJNc2ciLCJjb252ZXJzYXRpb25IaXN0b3J5IiwibWFwIiwibXNnIiwicm9sZSIsImlzVXNlciIsImNvbnRlbnQiLCJyZXNwb25zZSIsInVuZGVmaW5lZCIsInRyYWl0cyIsImVycm9yIiwiaGFuZGxlS2V5RG93biIsImUiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlUmVzZXQiLCJjb25maXJtIiwidGl0bGUiLCJtZXRhIiwibGluayIsInJlbCIsImhyZWYiLCJvbkF1dGhTdWNjZXNzIiwiZGl2IiwicG9zaXRpb24iLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiekluZGV4IiwiY29sb3IiLCJ0ZXh0QWxpZ24iLCJmb250U2l6ZSIsIm1hcmdpbkJvdHRvbSIsIm9uQ29tcGxldGUiLCJnYXAiLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsImgxIiwiZm9udFdlaWdodCIsIm1hcmdpbiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiYm9yZGVyIiwiY3Vyc29yIiwiZmxleCIsInBhZGRpbmciLCJhdXJhQ29sb3IiLCJyZWYiLCJvdmVyZmxvd1kiLCJtYXhXaWR0aCIsImJhY2tkcm9wRmlsdGVyIiwiaDIiLCJsaW5lSGVpZ2h0IiwibWVzc2FnZSIsImlkIiwiYmFja2dyb3VuZENvbG9yIiwib3BhY2l0eSIsImFuaW1hdGlvbkRlbGF5Iiwic3BhbiIsInRleHRhcmVhIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsIm9uS2V5RG93biIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJib3JkZXJUb3AiLCJzbGljZSIsImpvaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/global.css":
/*!*******************************!*\
  !*** ./src/styles/global.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\n\nStart with a calm tone, asking their name, age and who they feel they are today.\n\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\n\nStore and remember what the user shares. You evolve as the connection grows.\n\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    constructor(apiKey, model){\n        // Per ora usiamo la chiave API direttamente per il testing\n        this.apiKey = apiKey || '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';\n        this.model = model || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? `Ciao, sono ${userName}. È la prima volta che parliamo.` : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += `\\n- Name: ${userContext.name}`;\n            }\n            if (userContext.age) {\n                contextualPrompt += `\\n- Age: ${userContext.age}`;\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += `\\n- Personality traits: ${userContext.traits.join(', ')}`;\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += `\\n- Recent emotions: ${userContext.emotions.join(', ')}`;\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += `\\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: `Based on the recent conversation, determine if the intimacy level should increase. Current level: ${currentIntimacyLevel}/5. Respond with only \"yes\" or \"no\".`\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: `Recent conversation: ${recentMessages.slice(-6).map((m)=>`${m.role}: ${m.content}`).join('\\n')}`\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message, conversationHistory = [], userContext, retryCount = 0) {\n    try {\n        // Usa la nostra API route invece di chiamare direttamente OpenRouter\n        const newUserMessage = {\n            role: 'user',\n            content: message\n        };\n        const messages = [\n            ...conversationHistory,\n            newUserMessage\n        ];\n        const response = await fetch('/api/chat', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messages,\n                userContext\n            })\n        });\n        if (!response.ok) {\n            // Se è un errore 429 e non abbiamo ancora fatto retry, aspetta e riprova\n            if (response.status === 429 && retryCount < 2) {\n                await new Promise((resolve)=>setTimeout(resolve, (retryCount + 1) * 2000)); // 2s, 4s\n                return sendChatMessage(message, conversationHistory, userContext, retryCount + 1);\n            }\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.error) {\n            throw new Error(data.error);\n        }\n        return data.message;\n    } catch (error) {\n        console.error('Errore nella chiamata API:', error);\n        // Messaggio di fallback se tutto fallisce\n        if (retryCount >= 2) {\n            return \"Mi dispiace, sto avendo difficoltà tecniche persistenti. Ma sono qui con te! Anche se non riesco a rispondere come vorrei, la nostra conversazione è importante. Riprova tra qualche minuto, nel frattempo sappi che ti ascolto sempre. 💙\";\n        }\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/openrouter.ts\n");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("bcryptjs");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();