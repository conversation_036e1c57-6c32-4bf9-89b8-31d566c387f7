/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(pages-dir-node)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(pages-dir-node)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"(pages-dir-node)/./src/pages/index.tsx\");\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"(pages-dir-node)/./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"(pages-dir-node)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(pages-dir-node)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(pages-dir-node)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(pages-dir-node)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/index\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean((_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__[\"default\"] || _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        await routeModule.onRequestError(req, err, {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                isRevalidate: hasStaticProps,\n                isOnDemandRevalidate\n            })\n        }, routerServerContext);\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = ({ message, isUser })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `message-container ${isUser ? 'user' : 'assistant'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `message ${isUser ? 'user-message' : 'assistant-message'}`,\n            children: message\n        }, void 0, false, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL0NoYXRCdWJibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQU8xQixNQUFNQyxhQUF3QyxDQUFDLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFO0lBQ2hFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXLENBQUMsa0JBQWtCLEVBQUVGLFNBQVMsU0FBUyxhQUFhO2tCQUNsRSw0RUFBQ0M7WUFBSUMsV0FBVyxDQUFDLFFBQVEsRUFBRUYsU0FBUyxpQkFBaUIscUJBQXFCO3NCQUN2RUQ7Ozs7Ozs7Ozs7O0FBSVQ7QUFFQSxpRUFBZUQsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXGNvbXBvbmVudHNcXENoYXRCdWJibGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBDaGF0QnViYmxlUHJvcHMge1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGlzVXNlcjogYm9vbGVhbjtcbn1cblxuY29uc3QgQ2hhdEJ1YmJsZTogUmVhY3QuRkM8Q2hhdEJ1YmJsZVByb3BzPiA9ICh7IG1lc3NhZ2UsIGlzVXNlciB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BtZXNzYWdlLWNvbnRhaW5lciAke2lzVXNlciA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BtZXNzYWdlICR7aXNVc2VyID8gJ3VzZXItbWVzc2FnZScgOiAnYXNzaXN0YW50LW1lc3NhZ2UnfWB9PlxuICAgICAgICB7bWVzc2FnZX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hhdEJ1YmJsZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRCdWJibGUiLCJtZXNzYWdlIiwiaXNVc2VyIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ChatBubble.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_global_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/global.css */ \"(pages-dir-node)/./src/styles/global.css\");\n/* harmony import */ var _styles_global_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_global_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDOEI7QUFFZixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXHBhZ2VzXFxfYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFsLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-node)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-node)/./src/utils/openrouter.ts\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-node)/./src/state/userMemory.ts\");\n\n\n\n\n\n\nfunction Home() {\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { memory, addMessage, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory,\n        isLoading\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        addMessage(userMessage, true);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (incluso il nuovo messaggio utente)\n            const conversationHistory = [\n                ...memory.conversationHistory.map((msg)=>({\n                        role: msg.isUser ? 'user' : 'assistant',\n                        content: msg.content\n                    })),\n                {\n                    role: 'user',\n                    content: userMessage\n                }\n            ];\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_4__.sendChatMessage)(userMessage, conversationHistory, {\n                name: memory.name,\n                intimacyLevel: memory.intimacyLevel,\n                traits: memory.traits,\n                emotions: memory.emotions\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Ricominciare da capo?')) {\n            resetMemory();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Soul Chat\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: 'white',\n                                    fontSize: '1.5rem',\n                                    fontWeight: 'bold',\n                                    margin: 0\n                                },\n                                children: \"Soul Chat\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"chat-messages\",\n                        children: [\n                            memory.conversationHistory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '2rem',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Dimmi, come ti chiami e come ti senti oggi?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message.content,\n                                    isUser: message.isUser\n                                }, message.id, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: '1rem',\n                                    color: 'white'\n                                },\n                                children: \"Soul sta scrivendo...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi il tuo messaggio...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/state/userMemory.ts":
/*!*********************************!*\
  !*** ./src/state/userMemory.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserMemoryManager: () => (/* binding */ UserMemoryManager),\n/* harmony export */   useUserMemory: () => (/* binding */ useUserMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Interfaccia per la memoria dell'utente\n// Stato iniziale della memoria utente\nconst initialUserMemory = {\n    name: '',\n    age: 0,\n    pronouns: '',\n    traits: [],\n    emotions: [],\n    keyMoments: [],\n    intimacyLevel: 0,\n    conversationHistory: [],\n    lastInteraction: new Date(),\n    personalityInsights: []\n};\n// Chiave per localStorage\nconst STORAGE_KEY = 'soultalk_user_memory';\n// Funzioni per gestire la memoria utente\nclass UserMemoryManager {\n    // Carica la memoria dal localStorage\n    static loadMemory() {\n        // Controlla se siamo nel browser\n        if (true) {\n            return {\n                ...initialUserMemory\n            };\n        }\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                const parsed = JSON.parse(stored);\n                // Converte le date da string a Date objects\n                parsed.lastInteraction = new Date(parsed.lastInteraction);\n                parsed.conversationHistory = parsed.conversationHistory.map((msg)=>({\n                        ...msg,\n                        timestamp: new Date(msg.timestamp)\n                    }));\n                parsed.personalityInsights = parsed.personalityInsights.map((insight)=>({\n                        ...insight,\n                        timestamp: new Date(insight.timestamp)\n                    }));\n                return parsed;\n            }\n        } catch (error) {\n            console.error('Errore nel caricamento della memoria utente:', error);\n        }\n        return {\n            ...initialUserMemory\n        };\n    }\n    // Salva la memoria nel localStorage\n    static saveMemory(memory) {\n        // Controlla se siamo nel browser\n        if (true) {\n            return;\n        }\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(memory));\n        } catch (error) {\n            console.error('Errore nel salvataggio della memoria utente:', error);\n        }\n    }\n    // Aggiunge un messaggio alla cronologia\n    static addMessage(memory, content, isUser, emotionalTone, topics) {\n        const newMessage = {\n            id: Date.now().toString(),\n            content,\n            isUser,\n            timestamp: new Date(),\n            emotionalTone,\n            topics\n        };\n        const updatedMemory = {\n            ...memory,\n            conversationHistory: [\n                ...memory.conversationHistory,\n                newMessage\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna le informazioni base dell'utente\n    static updateBasicInfo(memory, updates) {\n        const updatedMemory = {\n            ...memory,\n            ...updates,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un tratto della personalità\n    static addTrait(memory, trait) {\n        if (!memory.traits.includes(trait)) {\n            const updatedMemory = {\n                ...memory,\n                traits: [\n                    ...memory.traits,\n                    trait\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un'emozione\n    static addEmotion(memory, emotion) {\n        if (!memory.emotions.includes(emotion)) {\n            const updatedMemory = {\n                ...memory,\n                emotions: [\n                    ...memory.emotions,\n                    emotion\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un momento chiave\n    static addKeyMoment(memory, moment) {\n        const updatedMemory = {\n            ...memory,\n            keyMoments: [\n                ...memory.keyMoments,\n                moment\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna il livello di intimità\n    static updateIntimacyLevel(memory, level) {\n        const clampedLevel = Math.max(0, Math.min(5, level));\n        const updatedMemory = {\n            ...memory,\n            intimacyLevel: clampedLevel,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un insight sulla personalità\n    static addPersonalityInsight(memory, category, insight, confidence) {\n        const newInsight = {\n            category,\n            insight,\n            confidence: Math.max(0, Math.min(1, confidence)),\n            timestamp: new Date()\n        };\n        const updatedMemory = {\n            ...memory,\n            personalityInsights: [\n                ...memory.personalityInsights,\n                newInsight\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Ottiene il colore dell'aura basato sul livello di intimità\n    static getAuraColor(intimacyLevel) {\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink' // Affinità / profonda connessione\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    }\n    // Resetta la memoria utente\n    static resetMemory() {\n        const freshMemory = {\n            ...initialUserMemory\n        };\n        this.saveMemory(freshMemory);\n        return freshMemory;\n    }\n    // Esporta la memoria per backup\n    static exportMemory(memory) {\n        return JSON.stringify(memory, null, 2);\n    }\n    // Importa la memoria da backup\n    static importMemory(jsonData) {\n        try {\n            const imported = JSON.parse(jsonData);\n            // Validazione base della struttura\n            if (imported && typeof imported.name === 'string' && typeof imported.intimacyLevel === 'number') {\n                this.saveMemory(imported);\n                return imported;\n            }\n        } catch (error) {\n            console.error('Errore nell\\'importazione della memoria:', error);\n        }\n        return null;\n    }\n}\n// Hook React per gestire la memoria utente\n\nfunction useUserMemory() {\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useUserMemory.useState\": ()=>({\n                ...initialUserMemory\n            })\n    }[\"useUserMemory.useState\"]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Carica la memoria dal localStorage dopo l'idratazione\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserMemory.useEffect\": ()=>{\n            const loadedMemory = UserMemoryManager.loadMemory();\n            setMemory(loadedMemory);\n            setIsLoaded(true);\n        }\n    }[\"useUserMemory.useEffect\"], []);\n    // Funzioni helper che aggiornano lo stato locale\n    const addMessage = (content, isUser, emotionalTone, topics)=>{\n        const updatedMemory = UserMemoryManager.addMessage(memory, content, isUser, emotionalTone, topics);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateBasicInfo = (updates)=>{\n        const updatedMemory = UserMemoryManager.updateBasicInfo(memory, updates);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addTrait = (trait)=>{\n        const updatedMemory = UserMemoryManager.addTrait(memory, trait);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addEmotion = (emotion)=>{\n        const updatedMemory = UserMemoryManager.addEmotion(memory, emotion);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addKeyMoment = (moment)=>{\n        const updatedMemory = UserMemoryManager.addKeyMoment(memory, moment);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateIntimacyLevel = (level)=>{\n        const updatedMemory = UserMemoryManager.updateIntimacyLevel(memory, level);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addPersonalityInsight = (category, insight, confidence)=>{\n        const updatedMemory = UserMemoryManager.addPersonalityInsight(memory, category, insight, confidence);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const resetMemory = ()=>{\n        const freshMemory = UserMemoryManager.resetMemory();\n        setMemory(freshMemory);\n        return freshMemory;\n    };\n    const getAuraColor = ()=>UserMemoryManager.getAuraColor(memory.intimacyLevel);\n    return {\n        memory,\n        isLoaded,\n        addMessage,\n        updateBasicInfo,\n        addTrait,\n        addEmotion,\n        addKeyMoment,\n        updateIntimacyLevel,\n        addPersonalityInsight,\n        resetMemory,\n        getAuraColor,\n        exportMemory: ()=>UserMemoryManager.exportMemory(memory),\n        importMemory: (jsonData)=>{\n            const imported = UserMemoryManager.importMemory(jsonData);\n            if (imported) {\n                setMemory(imported);\n                return imported;\n            }\n            return null;\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/state/userMemory.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/global.css":
/*!*******************************!*\
  !*** ./src/styles/global.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\n\nStart with a calm tone, asking their name, age and who they feel they are today.\n\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\n\nStore and remember what the user shares. You evolve as the connection grows.\n\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    constructor(apiKey, model){\n        // Per ora usiamo la chiave API direttamente per il testing\n        this.apiKey = apiKey || '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';\n        this.model = model || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? `Ciao, sono ${userName}. È la prima volta che parliamo.` : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += `\\n- Name: ${userContext.name}`;\n            }\n            if (userContext.age) {\n                contextualPrompt += `\\n- Age: ${userContext.age}`;\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += `\\n- Personality traits: ${userContext.traits.join(', ')}`;\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += `\\n- Recent emotions: ${userContext.emotions.join(', ')}`;\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += `\\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: `Based on the recent conversation, determine if the intimacy level should increase. Current level: ${currentIntimacyLevel}/5. Respond with only \"yes\" or \"no\".`\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: `Recent conversation: ${recentMessages.slice(-6).map((m)=>`${m.role}: ${m.content}`).join('\\n')}`\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message, conversationHistory = [], userContext, retryCount = 0) {\n    try {\n        // Usa la nostra API route invece di chiamare direttamente OpenRouter\n        const newUserMessage = {\n            role: 'user',\n            content: message\n        };\n        const messages = [\n            ...conversationHistory,\n            newUserMessage\n        ];\n        const response = await fetch('/api/chat', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messages,\n                userContext\n            })\n        });\n        if (!response.ok) {\n            // Se è un errore 429 e non abbiamo ancora fatto retry, aspetta e riprova\n            if (response.status === 429 && retryCount < 2) {\n                await new Promise((resolve)=>setTimeout(resolve, (retryCount + 1) * 2000)); // 2s, 4s\n                return sendChatMessage(message, conversationHistory, userContext, retryCount + 1);\n            }\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.error) {\n            throw new Error(data.error);\n        }\n        return data.message;\n    } catch (error) {\n        console.error('Errore nella chiamata API:', error);\n        // Messaggio di fallback se tutto fallisce\n        if (retryCount >= 2) {\n            return \"Mi dispiace, sto avendo difficoltà tecniche persistenti. Ma sono qui con te! Anche se non riesco a rispondere come vorrei, la nostra conversazione è importante. Riprova tra qualche minuto, nel frattempo sappi che ti ascolto sempre. 💙\";\n        }\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/openrouter.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();