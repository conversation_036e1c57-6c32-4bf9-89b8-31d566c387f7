/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/format-url */ \"next/dist/shared/lib/router/utils/format-url\");\n/* harmony import */ var next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(pages-dir-node)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(pages-dir-node)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/shared/lib/page-path/normalize-data-path */ \"next/dist/shared/lib/page-path/normalize-data-path\");\n/* harmony import */ var next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"(pages-dir-node)/./src/pages/index.tsx\");\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(pages-dir-node)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/shared/lib/utils */ \"next/dist/shared/lib/utils\");\n/* harmony import */ var next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/lib/redirect-status */ \"(pages-dir-node)/./node_modules/next/dist/lib/redirect-status.js\");\n/* harmony import */ var next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/lib/constants */ \"(pages-dir-node)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(pages-dir-node)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(pages-dir-node)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/response-cache/utils */ \"(pages-dir-node)/./node_modules/next/dist/server/response-cache/utils.js\");\n/* harmony import */ var next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(pages-dir-node)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/add-path-prefix */ \"next/dist/shared/lib/router/utils/add-path-prefix\");\n/* harmony import */ var next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/remove-trailing-slash */ \"next/dist/shared/lib/router/utils/remove-trailing-slash\");\n/* harmony import */ var next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__]);\n_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_10__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_11___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__\n});\nasync function handler(req, res, ctx) {\n    var _serverFilesManifest_config_experimental, _serverFilesManifest_config;\n    let srcPage = \"/index\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { buildId, query, params, parsedUrl, originalQuery, originalPathname, buildManifest, nextFontManifest, serverFilesManifest, reactLoadableManifest, prerenderManifest, isDraftMode, isOnDemandRevalidate, revalidateOnlyGenerated, locale, locales, defaultLocale, routerServerContext, nextConfig, resolvedPathname } = prepareResult;\n    const isExperimentalCompile = serverFilesManifest == null ? void 0 : (_serverFilesManifest_config = serverFilesManifest.config) == null ? void 0 : (_serverFilesManifest_config_experimental = _serverFilesManifest_config.experimental) == null ? void 0 : _serverFilesManifest_config_experimental.isExperimentalCompile;\n    const hasServerProps = Boolean(getServerSideProps);\n    const hasStaticProps = Boolean(getStaticProps);\n    const hasStaticPaths = Boolean(getStaticPaths);\n    const hasGetInitialProps = Boolean((_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__[\"default\"] || _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__).getInitialProps);\n    const isAmp = query.amp && config.amp;\n    let cacheKey = null;\n    let isIsrFallback = false;\n    let isNextDataRequest = prepareResult.isNextDataRequest && (hasStaticProps || hasServerProps);\n    const is404Page = srcPage === '/404';\n    const is500Page = srcPage === '/500';\n    const isErrorPage = srcPage === '/_error';\n    if (!routeModule.isDev && !isDraftMode && hasStaticProps) {\n        cacheKey = `${locale ? `/${locale}` : ''}${(srcPage === '/' || resolvedPathname === '/') && locale ? '' : resolvedPathname}${isAmp ? '.amp' : ''}`;\n        if (is404Page || is500Page || isErrorPage) {\n            cacheKey = `${locale ? `/${locale}` : ''}${srcPage}${isAmp ? '.amp' : ''}`;\n        }\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    if (hasStaticPaths && !isDraftMode) {\n        const decodedPathname = (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(locale ? (0,next_dist_shared_lib_router_utils_add_path_prefix__WEBPACK_IMPORTED_MODULE_24__.addPathPrefix)(resolvedPathname, `/${locale}`) : resolvedPathname);\n        const isPrerendered = Boolean(prerenderManifest.routes[decodedPathname]) || prerenderManifest.notFoundRoutes.includes(decodedPathname);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[srcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_21__.NoFallbackError();\n            }\n            if (typeof prerenderInfo.fallback === 'string' && !isPrerendered && !isNextDataRequest) {\n                isIsrFallback = true;\n            }\n        }\n    }\n    // When serving a bot request, we want to serve a blocking render and not\n    // the prerendered page. This ensures that the correct content is served\n    // to the bot in the head.\n    if (isIsrFallback && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_23__.isBot)(req.headers['user-agent'] || '') || (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n        isIsrFallback = false;\n    }\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const method = req.method || 'GET';\n        const resolvedUrl = (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n            pathname: nextConfig.trailingSlash ? parsedUrl.pathname : (0,next_dist_shared_lib_router_utils_remove_trailing_slash__WEBPACK_IMPORTED_MODULE_25__.removeTrailingSlash)(parsedUrl.pathname || '/'),\n            // make sure to only add query values from original URL\n            query: hasStaticProps ? {} : originalQuery\n        });\n        const publicRuntimeConfig = (routerServerContext == null ? void 0 : routerServerContext.publicRuntimeConfig) || nextConfig.publicRuntimeConfig;\n        const handleResponse = async (span)=>{\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                var _previousCacheEntry_value;\n                const doRender = async ()=>{\n                    try {\n                        var _nextConfig_i18n, _nextConfig_experimental_amp, _nextConfig_experimental_amp1;\n                        return await routeModule.render(req, res, {\n                            query: hasStaticProps && !isExperimentalCompile ? {\n                                ...params,\n                                ...isAmp ? {\n                                    amp: query.amp\n                                } : {}\n                            } : {\n                                ...query,\n                                ...params\n                            },\n                            params,\n                            page: srcPage,\n                            renderContext: {\n                                isDraftMode,\n                                isFallback: isIsrFallback,\n                                developmentNotFoundSourcePage: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'developmentNotFoundSourcePage')\n                            },\n                            sharedContext: {\n                                buildId,\n                                customServer: Boolean(routerServerContext == null ? void 0 : routerServerContext.isCustomServer) || undefined,\n                                deploymentId: false\n                            },\n                            renderOpts: {\n                                params,\n                                routeModule,\n                                page: srcPage,\n                                pageConfig: config || {},\n                                Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__),\n                                ComponentMod: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_13__,\n                                getStaticProps,\n                                getStaticPaths,\n                                getServerSideProps,\n                                supportsDynamicResponse: !hasStaticProps,\n                                buildManifest,\n                                nextFontManifest,\n                                reactLoadableManifest,\n                                assetPrefix: nextConfig.assetPrefix,\n                                strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n                                previewProps: prerenderManifest.preview,\n                                images: nextConfig.images,\n                                nextConfigOutput: nextConfig.output,\n                                optimizeCss: Boolean(nextConfig.experimental.optimizeCss),\n                                nextScriptWorkers: Boolean(nextConfig.experimental.nextScriptWorkers),\n                                domainLocales: (_nextConfig_i18n = nextConfig.i18n) == null ? void 0 : _nextConfig_i18n.domains,\n                                crossOrigin: nextConfig.crossOrigin,\n                                multiZoneDraftMode,\n                                basePath: nextConfig.basePath,\n                                canonicalBase: nextConfig.amp.canonicalBase || '',\n                                ampOptimizerConfig: (_nextConfig_experimental_amp = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp.optimizer,\n                                disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n                                largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n                                // Only the `publicRuntimeConfig` key is exposed to the client side\n                                // It'll be rendered as part of __NEXT_DATA__ on the client side\n                                runtimeConfig: Object.keys(publicRuntimeConfig).length > 0 ? publicRuntimeConfig : undefined,\n                                isExperimentalCompile,\n                                experimental: {\n                                    clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                                },\n                                locale,\n                                locales,\n                                defaultLocale,\n                                setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                                isNextDataRequest: isNextDataRequest && (hasServerProps || hasStaticProps),\n                                resolvedUrl,\n                                // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n                                // and not the resolved URL to prevent a hydration mismatch on\n                                // asPath\n                                resolvedAsPath: hasServerProps || hasGetInitialProps ? (0,next_dist_shared_lib_router_utils_format_url__WEBPACK_IMPORTED_MODULE_4__.formatUrl)({\n                                    // we use the original URL pathname less the _next/data prefix if\n                                    // present\n                                    pathname: isNextDataRequest ? (0,next_dist_shared_lib_page_path_normalize_data_path__WEBPACK_IMPORTED_MODULE_8__.normalizeDataPath)(originalPathname) : originalPathname,\n                                    query: originalQuery\n                                }) : resolvedUrl,\n                                isOnDemandRevalidate,\n                                ErrorDebug: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'PagesErrorDebug'),\n                                err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'invokeError'),\n                                dev: routeModule.isDev,\n                                // needed for experimental.optimizeCss feature\n                                distDir: `${routeModule.projectDir}/${routeModule.distDir}`,\n                                ampSkipValidation: (_nextConfig_experimental_amp1 = nextConfig.experimental.amp) == null ? void 0 : _nextConfig_experimental_amp1.skipValidation,\n                                ampValidator: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'ampValidator')\n                            }\n                        }).then((renderResult)=>{\n                            const { metadata } = renderResult;\n                            let cacheControl = metadata.cacheControl;\n                            if ('isNotFound' in metadata && metadata.isNotFound) {\n                                return {\n                                    value: null,\n                                    cacheControl\n                                };\n                            }\n                            // Handle `isRedirect`.\n                            if (metadata.isRedirect) {\n                                return {\n                                    value: {\n                                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT,\n                                        props: metadata.pageData ?? metadata.flightData\n                                    },\n                                    cacheControl\n                                };\n                            }\n                            return {\n                                value: {\n                                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                                    html: renderResult,\n                                    pageData: renderResult.metadata.pageData,\n                                    headers: renderResult.metadata.headers,\n                                    status: renderResult.metadata.statusCode\n                                },\n                                cacheControl\n                            };\n                        }).finally(()=>{\n                            if (!span) return;\n                            span.setAttributes({\n                                'http.status_code': res.statusCode,\n                                'next.rsc': false\n                            });\n                            const rootSpanAttributes = tracer.getRootSpanAttributes();\n                            // We were unable to get attributes, probably OTEL is not enabled\n                            if (!rootSpanAttributes) {\n                                return;\n                            }\n                            if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest) {\n                                console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                                return;\n                            }\n                            const route = rootSpanAttributes.get('next.route');\n                            if (route) {\n                                const name = `${method} ${route}`;\n                                span.setAttributes({\n                                    'next.route': route,\n                                    'http.route': route,\n                                    'next.span_name': name\n                                });\n                                span.updateName(name);\n                            } else {\n                                span.updateName(`${method} ${req.url}`);\n                            }\n                        });\n                    } catch (err) {\n                        // if this is a background revalidate we need to report\n                        // the request error here as it won't be bubbled\n                        if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                            await routeModule.onRequestError(req, err, {\n                                routerKind: 'Pages Router',\n                                routePath: srcPage,\n                                routeType: 'render',\n                                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                                    isRevalidate: hasStaticProps,\n                                    isOnDemandRevalidate\n                                })\n                            }, routerServerContext);\n                        }\n                        throw err;\n                    }\n                };\n                // if we've already generated this page we no longer\n                // serve the fallback\n                if (previousCacheEntry) {\n                    isIsrFallback = false;\n                }\n                if (isIsrFallback) {\n                    const fallbackResponse = await routeModule.getResponseCache(req).get(routeModule.isDev ? null : locale ? `/${locale}${srcPage}` : srcPage, async ({ previousCacheEntry: previousFallbackCacheEntry = null })=>{\n                        if (!routeModule.isDev) {\n                            return (0,next_dist_server_response_cache_utils__WEBPACK_IMPORTED_MODULE_20__.toResponseCacheEntry)(previousFallbackCacheEntry);\n                        }\n                        return doRender();\n                    }, {\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                        isFallback: true,\n                        isRoutePPREnabled: false,\n                        isOnDemandRevalidate: false,\n                        incrementalCache: await routeModule.getIncrementalCache(req, nextConfig, prerenderManifest),\n                        waitUntil: ctx.waitUntil\n                    });\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        fallbackResponse.isMiss = true;\n                        return fallbackResponse;\n                    }\n                }\n                if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                    res.statusCode = 404;\n                    // on-demand revalidate always sets this header\n                    res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                    res.end('This page could not be found');\n                    return null;\n                }\n                if (isIsrFallback && (previousCacheEntry == null ? void 0 : (_previousCacheEntry_value = previousCacheEntry.value) == null ? void 0 : _previousCacheEntry_value.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                    return {\n                        value: {\n                            kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES,\n                            html: new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(previousCacheEntry.value.html), {\n                                contentType: 'text/html;utf-8',\n                                metadata: {\n                                    statusCode: previousCacheEntry.value.status,\n                                    headers: previousCacheEntry.value.headers\n                                }\n                            }),\n                            pageData: {},\n                            status: previousCacheEntry.value.status,\n                            headers: previousCacheEntry.value.headers\n                        },\n                        cacheControl: {\n                            revalidate: 0,\n                            expire: undefined\n                        }\n                    };\n                }\n                return doRender();\n            };\n            const result = await routeModule.handleResponse({\n                cacheKey,\n                req,\n                nextConfig,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                waitUntil: ctx.waitUntil,\n                responseGenerator: responseGenerator,\n                prerenderManifest\n            });\n            // if we got a cache hit this wasn't an ISR fallback\n            // but it wasn't generated during build so isn't in the\n            // prerender-manifest\n            if (isIsrFallback && !(result == null ? void 0 : result.isMiss)) {\n                isIsrFallback = false;\n            }\n            // response is finished is no cache entry\n            if (!result) {\n                return;\n            }\n            if (hasStaticProps && !(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : result.isMiss ? 'MISS' : result.isStale ? 'STALE' : 'HIT');\n            }\n            let cacheControl;\n            if (!hasStaticProps || isIsrFallback) {\n                if (!res.getHeader('Cache-Control')) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                }\n            } else if (is404Page) {\n                const notFoundRevalidate = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'notFoundRevalidate');\n                cacheControl = {\n                    revalidate: typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n                    expire: undefined\n                };\n            } else if (is500Page) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (result.cacheControl) {\n                // If the cache entry has a cache control with a revalidate value that's\n                // a number, use it.\n                if (typeof result.cacheControl.revalidate === 'number') {\n                    var _result_cacheControl;\n                    if (result.cacheControl.revalidate < 1) {\n                        throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${result.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                            value: \"E22\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    cacheControl = {\n                        revalidate: result.cacheControl.revalidate,\n                        expire: ((_result_cacheControl = result.cacheControl) == null ? void 0 : _result_cacheControl.expire) ?? nextConfig.expireTime\n                    };\n                } else {\n                    // revalidate: false\n                    cacheControl = {\n                        revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_17__.CACHE_ONE_YEAR,\n                        expire: undefined\n                    };\n                }\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheControl && !res.getHeader('Cache-Control')) {\n                res.setHeader('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_14__.getCacheControlHeader)(cacheControl));\n            }\n            // notFound: true case\n            if (!result.value) {\n                var _result_cacheControl1;\n                // add revalidate metadata before rendering 404 page\n                // so that we can use this as source of truth for the\n                // cache-control header instead of what the 404 page returns\n                // for the revalidate value\n                (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.addRequestMeta)(req, 'notFoundRevalidate', (_result_cacheControl1 = result.cacheControl) == null ? void 0 : _result_cacheControl1.revalidate);\n                res.statusCode = 404;\n                if (isNextDataRequest) {\n                    res.end('{\"notFound\":true}');\n                    return;\n                }\n                // TODO: should route-module itself handle rendering the 404\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res, parsedUrl, false);\n                } else {\n                    res.end('This page could not be found');\n                }\n                return;\n            }\n            if (result.value.kind === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.REDIRECT) {\n                if (isNextDataRequest) {\n                    res.setHeader('content-type', 'application/json');\n                    res.end(JSON.stringify(result.value.props));\n                    return;\n                } else {\n                    const handleRedirect = (pageData)=>{\n                        const redirect = {\n                            destination: pageData.pageProps.__N_REDIRECT,\n                            statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n                            basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH\n                        };\n                        const statusCode = (0,next_dist_lib_redirect_status__WEBPACK_IMPORTED_MODULE_16__.getRedirectStatus)(redirect);\n                        const { basePath } = nextConfig;\n                        if (basePath && redirect.basePath !== false && redirect.destination.startsWith('/')) {\n                            redirect.destination = `${basePath}${redirect.destination}`;\n                        }\n                        if (redirect.destination.startsWith('/')) {\n                            redirect.destination = (0,next_dist_shared_lib_utils__WEBPACK_IMPORTED_MODULE_15__.normalizeRepeatedSlashes)(redirect.destination);\n                        }\n                        res.statusCode = statusCode;\n                        res.setHeader('Location', redirect.destination);\n                        if (statusCode === next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_22__.RedirectStatusCode.PermanentRedirect) {\n                            res.setHeader('Refresh', `0;url=${redirect.destination}`);\n                        }\n                        res.end(redirect.destination);\n                    };\n                    await handleRedirect(result.value.props);\n                    return null;\n                }\n            }\n            if (result.value.kind !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_9__.CachedRouteKind.PAGES) {\n                throw Object.defineProperty(new Error(`Invariant: received non-pages cache entry in pages handler`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E695\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // when invoking _error before pages/500 we don't actually\n            // send the _error response\n            if ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'customErrorRender') || isErrorPage && (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_5__.getRequestMeta)(req, 'minimalMode') && res.statusCode === 500) {\n                return null;\n            }\n            await (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_18__.sendRenderResult)({\n                req,\n                res,\n                // If we are rendering the error page it's not a data request\n                // anymore\n                result: isNextDataRequest && !isErrorPage && !is500Page ? new next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"](Buffer.from(JSON.stringify(result.value.pageData)), {\n                    contentType: 'application/json',\n                    metadata: result.value.html.metadata\n                }) : result.value.html,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                cacheControl: routeModule.isDev ? undefined : cacheControl,\n                type: isNextDataRequest ? 'json' : 'html'\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse();\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_2__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        await routeModule.onRequestError(req, err, {\n            routerKind: 'Pages Router',\n            routePath: srcPage,\n            routeType: 'render',\n            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_7__.getRevalidateReason)({\n                isRevalidate: hasStaticProps,\n                isOnDemandRevalidate\n            })\n        }, routerServerContext);\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/AuraIndicator.tsx":
/*!******************************************!*\
  !*** ./src/components/AuraIndicator.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuraProgression: () => (/* binding */ AuraProgression),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuraInfo: () => (/* binding */ useAuraInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuraIndicator = ({ intimacyLevel, className = '', showLabel = false })=>{\n    // Mappatura dei livelli di intimità con colori e descrizioni\n    const getAuraInfo = (level)=>{\n        const auraMap = {\n            0: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-gray-400 to-blue-400',\n                shadowColor: 'shadow-blue-500/30',\n                glowColor: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',\n                label: 'Primo Incontro',\n                description: 'Conoscenza iniziale'\n            },\n            1: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-slate-400 to-blue-500',\n                shadowColor: 'shadow-blue-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(59,130,246,0.4)]',\n                label: 'Apertura',\n                description: 'Iniziando ad aprirsi'\n            },\n            2: {\n                color: 'teal',\n                bgColor: 'bg-gradient-to-r from-teal-400 to-cyan-500',\n                shadowColor: 'shadow-teal-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(20,184,166,0.4)]',\n                label: 'Connessione',\n                description: 'Connessione emotiva in crescita'\n            },\n            3: {\n                color: 'yellow',\n                bgColor: 'bg-gradient-to-r from-yellow-400 to-amber-500',\n                shadowColor: 'shadow-yellow-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(245,158,11,0.4)]',\n                label: 'Condivisione',\n                description: 'Scambio personale attivo'\n            },\n            4: {\n                color: 'red',\n                bgColor: 'bg-gradient-to-r from-red-400 to-pink-500',\n                shadowColor: 'shadow-red-500/30',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(239,68,68,0.3)]',\n                label: 'Profondità',\n                description: 'Discussioni delicate'\n            },\n            5: {\n                color: 'purple-pink',\n                bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',\n                shadowColor: 'shadow-purple-500/40',\n                glowColor: 'drop-shadow-[0_0_30px_rgba(139,92,246,0.4)]',\n                label: 'Affinità',\n                description: 'Profonda connessione'\n            }\n        };\n        return auraMap[level] || auraMap[0];\n    };\n    const auraInfo = getAuraInfo(intimacyLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center space-x-3 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            absolute inset-0 rounded-full\n            ${auraInfo.shadowColor}\n            ${auraInfo.glowColor}\n            animate-pulse-slow\n            blur-sm\n          `,\n                        style: {\n                            width: '24px',\n                            height: '24px',\n                            transform: 'scale(1.5)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            relative w-6 h-6 rounded-full\n            ${auraInfo.bgColor}\n            ${auraInfo.shadowColor}\n            shadow-lg\n            transition-all duration-500\n          `\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n            absolute inset-1 rounded-full\n            bg-white/20\n            animate-ping\n          `,\n                        style: {\n                            animationDuration: '2s',\n                            animationIterationCount: 'infinite'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: auraInfo.label\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-white/60\",\n                        children: auraInfo.description\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n// Componente per mostrare la progressione dell'aura\nconst AuraProgression = ({ intimacyLevel })=>{\n    const levels = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuraIndicator, {\n                        intimacyLevel: level,\n                        className: `\n              transition-all duration-300\n              ${level <= intimacyLevel ? 'opacity-100 scale-100' : 'opacity-30 scale-75'}\n            `\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    level === intimacyLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-6 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, level, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook per ottenere informazioni sull'aura\nconst useAuraInfo = (intimacyLevel)=>{\n    const getAuraColor = ()=>{\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink'\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    };\n    const getAuraDescription = ()=>{\n        const descriptions = {\n            0: 'Primo incontro - Conoscenza iniziale',\n            1: 'Apertura - Iniziando ad aprirsi',\n            2: 'Connessione - Connessione emotiva in crescita',\n            3: 'Condivisione - Scambio personale attivo',\n            4: 'Profondità - Discussioni delicate',\n            5: 'Affinità - Profonda connessione'\n        };\n        return descriptions[intimacyLevel] || descriptions[0];\n    };\n    const getNextLevelDescription = ()=>{\n        if (intimacyLevel >= 5) return 'Livello massimo raggiunto';\n        const nextDescriptions = {\n            0: 'Continua a parlare per approfondire la conoscenza',\n            1: 'Condividi qualcosa di personale per crescere insieme',\n            2: 'Apri il cuore per rafforzare la connessione',\n            3: 'Esplora argomenti più profondi',\n            4: 'Condividi i tuoi pensieri più intimi'\n        };\n        return nextDescriptions[intimacyLevel] || '';\n    };\n    return {\n        color: getAuraColor(),\n        description: getAuraDescription(),\n        nextLevelDescription: getNextLevelDescription(),\n        isMaxLevel: intimacyLevel >= 5,\n        progress: intimacyLevel / 5 * 100\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuraIndicator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/AuraIndicator.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/AuthForm.tsx":
/*!*************************************!*\
  !*** ./src/components/AuthForm.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-node)/./src/lib/auth.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst AuthForm = ({ onAuthSuccess })=>{\n    const [isLogin, setIsLogin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n        // Validazioni\n        if (!username.trim() || !password.trim()) {\n            setError('Username e password sono obbligatori');\n            setIsLoading(false);\n            return;\n        }\n        if (username.length < 3) {\n            setError('L\\'username deve essere di almeno 3 caratteri');\n            setIsLoading(false);\n            return;\n        }\n        if (password.length < 6) {\n            setError('La password deve essere di almeno 6 caratteri');\n            setIsLoading(false);\n            return;\n        }\n        if (!isLogin && password !== confirmPassword) {\n            setError('Le password non coincidono');\n            setIsLoading(false);\n            return;\n        }\n        try {\n            let result;\n            if (isLogin) {\n                result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.loginUser)(username, password);\n            } else {\n                result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.registerUser)(username, password);\n            }\n            if (result.success && result.userId) {\n                onAuthSuccess(result.userId);\n            } else {\n                setError(result.error || 'Errore sconosciuto');\n            }\n        } catch (error) {\n            setError('Errore di connessione. Riprova più tardi.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.9)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 2000\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                background: 'rgba(255,255,255,0.1)',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '1rem',\n                padding: '2rem',\n                maxWidth: '400px',\n                width: '90%',\n                border: '1px solid rgba(255,255,255,0.2)'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        marginBottom: '2rem'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '3rem',\n                                marginBottom: '1rem'\n                            },\n                            children: \"\\uD83C\\uDF1F\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: 'white',\n                                fontSize: '1.5rem',\n                                fontWeight: '600',\n                                margin: 0,\n                                marginBottom: '0.5rem'\n                            },\n                            children: \"Benvenuto in SoulTalk\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: 'rgba(255,255,255,0.8)',\n                                fontSize: '0.875rem',\n                                margin: 0\n                            },\n                            children: isLogin ? 'Accedi per continuare la tua conversazione con Soul' : 'Crea un account per iniziare la tua avventura con Soul'\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Username\",\n                                value: username,\n                                onChange: (e)=>setUsername(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '0.75rem',\n                                    borderRadius: '0.5rem',\n                                    border: '1px solid rgba(255,255,255,0.3)',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none'\n                                },\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                placeholder: \"Password\",\n                                value: password,\n                                onChange: (e)=>setPassword(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '0.75rem',\n                                    borderRadius: '0.5rem',\n                                    border: '1px solid rgba(255,255,255,0.3)',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none'\n                                },\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        !isLogin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"password\",\n                                placeholder: \"Conferma Password\",\n                                value: confirmPassword,\n                                onChange: (e)=>setConfirmPassword(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '0.75rem',\n                                    borderRadius: '0.5rem',\n                                    border: '1px solid rgba(255,255,255,0.3)',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none'\n                                },\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: '#ff6b6b',\n                                fontSize: '0.875rem',\n                                marginBottom: '1rem',\n                                textAlign: 'center'\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            style: {\n                                width: '100%',\n                                padding: '0.75rem',\n                                borderRadius: '0.5rem',\n                                border: 'none',\n                                background: isLoading ? 'rgba(99, 102, 241, 0.5)' : '#6366f1',\n                                color: 'white',\n                                fontSize: '1rem',\n                                fontWeight: '600',\n                                cursor: isLoading ? 'not-allowed' : 'pointer',\n                                marginBottom: '1rem'\n                            },\n                            children: isLoading ? isLogin ? 'Accesso...' : 'Registrazione...' : isLogin ? 'Accedi' : 'Registrati'\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    setIsLogin(!isLogin);\n                                    setError('');\n                                    setPassword('');\n                                    setConfirmPassword('');\n                                },\n                                disabled: isLoading,\n                                style: {\n                                    background: 'none',\n                                    border: 'none',\n                                    color: 'rgba(255,255,255,0.8)',\n                                    fontSize: '0.875rem',\n                                    cursor: isLoading ? 'not-allowed' : 'pointer',\n                                    textDecoration: 'underline'\n                                },\n                                children: isLogin ? 'Non hai un account? Registrati' : 'Hai già un account? Accedi'\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuthForm.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthForm);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/AuthForm.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = ({ message, isUser })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `message-container ${isUser ? 'user' : 'assistant'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `message ${isUser ? 'user-message' : 'assistant-message'}`,\n            children: message\n        }, void 0, false, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL0NoYXRCdWJibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQU8xQixNQUFNQyxhQUF3QyxDQUFDLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFO0lBQ2hFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXLENBQUMsa0JBQWtCLEVBQUVGLFNBQVMsU0FBUyxhQUFhO2tCQUNsRSw0RUFBQ0M7WUFBSUMsV0FBVyxDQUFDLFFBQVEsRUFBRUYsU0FBUyxpQkFBaUIscUJBQXFCO3NCQUN2RUQ7Ozs7Ozs7Ozs7O0FBSVQ7QUFFQSxpRUFBZUQsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXGNvbXBvbmVudHNcXENoYXRCdWJibGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBDaGF0QnViYmxlUHJvcHMge1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGlzVXNlcjogYm9vbGVhbjtcbn1cblxuY29uc3QgQ2hhdEJ1YmJsZTogUmVhY3QuRkM8Q2hhdEJ1YmJsZVByb3BzPiA9ICh7IG1lc3NhZ2UsIGlzVXNlciB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BtZXNzYWdlLWNvbnRhaW5lciAke2lzVXNlciA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BtZXNzYWdlICR7aXNVc2VyID8gJ3VzZXItbWVzc2FnZScgOiAnYXNzaXN0YW50LW1lc3NhZ2UnfWB9PlxuICAgICAgICB7bWVzc2FnZX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hhdEJ1YmJsZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoYXRCdWJibGUiLCJtZXNzYWdlIiwiaXNVc2VyIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ChatBubble.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/GlassFrame.tsx":
/*!***************************************!*\
  !*** ./src/components/GlassFrame.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GlassFrame = ({ children, auraColor = 'blue', className = '' })=>{\n    const getAuraColorClass = (color)=>{\n        const colorMap = {\n            'gray-blue': 'shadow-blue-500/30',\n            'teal': 'shadow-teal-500/40',\n            'yellow': 'shadow-yellow-500/40',\n            'red': 'shadow-red-500/30',\n            'purple-pink': 'shadow-purple-500/40',\n            'blue': 'shadow-blue-500/30'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    const getAuraColorRgba = (color)=>{\n        const colorMap = {\n            'gray-blue': 'rgba(59, 130, 246, 0.3)',\n            'teal': 'rgba(20, 184, 166, 0.4)',\n            'yellow': 'rgba(245, 158, 11, 0.4)',\n            'red': 'rgba(239, 68, 68, 0.3)',\n            'purple-pink': 'rgba(139, 92, 246, 0.4)',\n            'blue': 'rgba(59, 130, 246, 0.3)'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl aura-animation\",\n                style: {\n                    filter: 'blur(2px)',\n                    transform: 'scale(1.02)',\n                    boxShadow: `0 0 40px ${getAuraColorRgba(auraColor)}`\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative glass-effect transition-all duration-500\",\n                style: {\n                    border: `2px solid rgba(255, 255, 255, 0.2)`,\n                    boxShadow: `0 0 30px ${getAuraColorRgba(auraColor)}`\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlassFrame);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/GlassFrame.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-node)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-node)/./src/lib/auth.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_3__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst OnboardingChat = ({ userId, onComplete })=>{\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>`${name}... che bel nome! Mi piace molto come suona. 💫`,\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return `Perfetto ${name}! ${age} anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡`;\n                if (age < 30) return `Fantastico ${name}! ${age} anni... un'età perfetta per esplorare nuove idee insieme! ✨`;\n                if (age < 50) return `Ottimo ${name}! ${age} anni... hai esperienza e saggezza, sarà bellissimo parlare con te! 🌟`;\n                return `Meraviglioso ${name}! ${age} anni... la tua esperienza di vita sarà preziosa per le nostre conversazioni! 🎭`;\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser, withTyping = false)=>{\n        const newMessage = {\n            id: `onb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        // Determina il messaggio da mostrare\n        let messageContent;\n        if (typeof step.message === 'function') {\n            messageContent = step.message(userData.name, userData.age);\n        } else {\n            messageContent = step.message;\n        }\n        // Aggiungi messaggio con effetto typing\n        await addMessageWithTyping(messageContent);\n        if (step.isLast) {\n            // Completa l'onboarding con delay personalizzato\n            setTimeout(async ()=>{\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.completeOnboarding)(userId);\n                onComplete(userData);\n            }, step.delay || 2500);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, step.delay || 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizia l'onboarding solo al primo step\n            if (currentStep === 0) {\n                setTimeout({\n                    \"OnboardingChat.useEffect\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect\"], 1000);\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: `radial-gradient(circle, #8b5cf6 0%, transparent 70%)`,\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: `${currentStep / totalSteps * 100}%`,\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.05)',\n                            borderRadius: '1rem',\n                            padding: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.1)',\n                            display: 'flex',\n                            gap: '0.5rem',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi la tua risposta...\",\n                                style: {\n                                    flex: 1,\n                                    padding: '0.75rem 1rem',\n                                    borderRadius: '0.75rem',\n                                    border: 'none',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none',\n                                    backdropFilter: 'blur(10px)'\n                                },\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleUserInput,\n                                disabled: !inputMessage.trim(),\n                                style: {\n                                    width: '48px',\n                                    height: '48px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    background: inputMessage.trim() ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' : 'rgba(139, 92, 246, 0.3)',\n                                    color: 'white',\n                                    fontSize: '1.2rem',\n                                    cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    transition: 'all 0.2s ease',\n                                    transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'\n                                },\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.75rem',\n                            color: 'rgba(255,255,255,0.5)',\n                            fontSize: '0.75rem'\n                        },\n                        children: \"Premi Invio per inviare\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/OnboardingChat.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-node)/./src/lib/database.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-node)/./src/lib/auth.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [needsOnboarding, setNeedsOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Inizializza solo lato client\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            setIsClient(true);\n            // Controlla se l'utente è già autenticato\n            if (false) {}\n        }\n    }[\"useSupabaseMemory.useEffect\"], []);\n    // Funzione per autenticare l'utente\n    const authenticateUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[authenticateUser]\": (newUserId)=>{\n            setUserId(newUserId);\n            setIsAuthenticated(true);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveUserSession)(newUserId);\n        }\n    }[\"useSupabaseMemory.useCallback[authenticateUser]\"], []);\n    // Funzione per logout\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logoutUser)();\n            setUserId('');\n            setIsAuthenticated(false);\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setNeedsOnboarding(false);\n        }\n    }[\"useSupabaseMemory.useCallback[logout]\"], []);\n    // Carica i dati iniziali solo quando siamo lato client, autenticati e abbiamo un userId\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || !isAuthenticated) return;\n            const loadData = {\n                \"useSupabaseMemory.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Controlla se l'utente ha completato l'onboarding\n                        const isOnboarded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.checkOnboardingStatus)(userId);\n                        setNeedsOnboarding(!isOnboarded);\n                        // Carica profilo utente\n                        const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(userId);\n                        if (profile) {\n                            setUserMemory({\n                                name: profile.name || '',\n                                age: profile.age || 0,\n                                traits: profile.traits || [],\n                                emotions: profile.emotions || [],\n                                intimacyLevel: profile.intimacy_level || 0,\n                                keyMoments: profile.key_moments || []\n                            });\n                        }\n                        // Carica messaggi solo se l'onboarding è completato\n                        if (isOnboarded) {\n                            const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(userId);\n                            const formattedMessages = chatMessages.map({\n                                \"useSupabaseMemory.useEffect.loadData.formattedMessages\": (msg)=>({\n                                        id: msg.id,\n                                        content: msg.content,\n                                        isUser: msg.is_user,\n                                        timestamp: new Date(msg.timestamp)\n                                    })\n                            }[\"useSupabaseMemory.useEffect.loadData.formattedMessages\"]);\n                            setMessages(formattedMessages);\n                        }\n                    } catch (error) {\n                        console.error('Error loading data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseMemory.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        isAuthenticated\n    ]);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || isLoading || !userMemory.name) return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se siamo lato client e abbiamo userId\n            if (isClient && userId) {\n                setIsSaving(true);\n                try {\n                    const chatMessage = {\n                        id: newMessage.id,\n                        user_id: userId,\n                        content: newMessage.content,\n                        is_user: newMessage.isUser,\n                        timestamp: newMessage.timestamp.toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.saveChatMessage)(chatMessage);\n                } catch (error) {\n                    console.error('Error saving message:', error);\n                } finally{\n                    setIsSaving(false);\n                }\n            }\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        isClient,\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!isClient || !userId) return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                logout();\n                if (false) {}\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        isClient,\n        userId,\n        logout\n    ]);\n    // Completa l'onboarding con i dati dell'utente\n    const completeOnboardingWithData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": async (userData)=>{\n            if (!userId) return;\n            try {\n                // Aggiorna il profilo utente con i dati dell'onboarding\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)({\n                    id: userId,\n                    name: userData.name,\n                    age: userData.age,\n                    is_onboarded: true\n                });\n                // Aggiorna lo stato locale\n                setUserMemory({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": (prev)=>({\n                            ...prev,\n                            name: userData.name,\n                            age: userData.age\n                        })\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"]);\n                setNeedsOnboarding(false);\n            } catch (error) {\n                console.error('Error completing onboarding:', error);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], [\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        isAuthenticated,\n        needsOnboarding,\n        addMessage,\n        updateUserMemory,\n        resetMemory,\n        authenticateUser,\n        logout,\n        completeOnboardingWithData\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/hooks/useSupabaseMemory.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOnboardingStatus: () => (/* binding */ checkOnboardingStatus),\n/* harmony export */   completeOnboarding: () => (/* binding */ completeOnboarding),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-node)/./src/lib/supabase.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([bcryptjs__WEBPACK_IMPORTED_MODULE_1__]);\nbcryptjs__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// Funzione per hash della password\nconst hashPassword = async (password)=>{\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n};\n// Funzione per verificare la password\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n};\n// Registrazione nuovo utente\nconst registerUser = async (username, password)=>{\n    try {\n        // Controlla se l'username esiste già\n        const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('username').eq('username', username.toLowerCase()).single();\n        if (existingUser) {\n            return {\n                success: false,\n                error: 'Username già esistente'\n            };\n        }\n        // Crea hash della password\n        const passwordHash = await hashPassword(password);\n        // Genera ID utente\n        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Inserisci nella tabella auth\n        const { error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            password_hash: passwordHash\n        });\n        if (authError) {\n            console.error('Error creating auth:', authError);\n            return {\n                success: false,\n                error: 'Errore durante la registrazione'\n            };\n        }\n        // Crea profilo utente\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            name: '',\n            is_onboarded: false\n        });\n        if (profileError) {\n            console.error('Error creating profile:', profileError);\n            // Rollback: elimina l'auth se il profilo fallisce\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').delete().eq('id', userId);\n            return {\n                success: false,\n                error: 'Errore durante la creazione del profilo'\n            };\n        }\n        return {\n            success: true,\n            userId\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Login utente\nconst loginUser = async (username, password)=>{\n    try {\n        // Trova l'utente\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('*').eq('username', username.toLowerCase()).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Verifica password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Aggiorna last_login\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').update({\n            last_login: new Date().toISOString()\n        }).eq('id', user.id);\n        return {\n            success: true,\n            userId: user.id\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Verifica se l'utente è autenticato (controlla localStorage)\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const authData = localStorage.getItem('soul_auth');\n    if (!authData) return null;\n    try {\n        const { userId, timestamp } = JSON.parse(authData);\n        // Controlla se la sessione è scaduta (24 ore)\n        const now = Date.now();\n        const sessionAge = now - timestamp;\n        const maxAge = 24 * 60 * 60 * 1000 // 24 ore\n        ;\n        if (sessionAge > maxAge) {\n            localStorage.removeItem('soul_auth');\n            return null;\n        }\n        return userId;\n    } catch  {\n        localStorage.removeItem('soul_auth');\n        return null;\n    }\n};\n// Salva sessione utente\nconst saveUserSession = (userId)=>{\n    if (true) return;\n    const authData = {\n        userId,\n        timestamp: Date.now()\n    };\n    localStorage.setItem('soul_auth', JSON.stringify(authData));\n};\n// Logout utente\nconst logoutUser = ()=>{\n    if (true) return;\n    localStorage.removeItem('soul_auth');\n    localStorage.removeItem('soul_user_id'); // Rimuovi anche il vecchio ID\n};\n// Controlla se l'utente ha completato l'onboarding\nconst checkOnboardingStatus = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('is_onboarded').eq('id', userId).single();\n        if (error || !data) return false;\n        return data.is_onboarded || false;\n    } catch  {\n        return false;\n    }\n};\n// Completa l'onboarding\nconst completeOnboarding = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: true\n        }).eq('id', userId);\n        return !error;\n    } catch  {\n        return false;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9saWIvYXV0aC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUNSO0FBZ0I3QixtQ0FBbUM7QUFDNUIsTUFBTUUsZUFBZSxPQUFPQztJQUNqQyxNQUFNQyxhQUFhO0lBQ25CLE9BQU8sTUFBTUgscURBQVcsQ0FBQ0UsVUFBVUM7QUFDckMsRUFBQztBQUVELHNDQUFzQztBQUMvQixNQUFNRSxpQkFBaUIsT0FBT0gsVUFBa0JFO0lBQ3JELE9BQU8sTUFBTUosd0RBQWMsQ0FBQ0UsVUFBVUU7QUFDeEMsRUFBQztBQUVELDZCQUE2QjtBQUN0QixNQUFNRyxlQUFlLE9BQU9DLFVBQWtCTjtJQUNuRCxJQUFJO1FBQ0YscUNBQXFDO1FBQ3JDLE1BQU0sRUFBRU8sTUFBTUMsWUFBWSxFQUFFLEdBQUcsTUFBTVgsK0NBQVFBLENBQzFDWSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFDLFlBQ1BDLEVBQUUsQ0FBQyxZQUFZTCxTQUFTTSxXQUFXLElBQ25DQyxNQUFNO1FBRVQsSUFBSUwsY0FBYztZQUNoQixPQUFPO2dCQUFFTSxTQUFTO2dCQUFPQyxPQUFPO1lBQXlCO1FBQzNEO1FBRUEsMkJBQTJCO1FBQzNCLE1BQU1DLGVBQWUsTUFBTWpCLGFBQWFDO1FBRXhDLG1CQUFtQjtRQUNuQixNQUFNaUIsU0FBUyxDQUFDLEtBQUssRUFBRUMsS0FBS0MsR0FBRyxHQUFHLENBQUMsRUFBRUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUcsSUFBSTtRQUU5RSwrQkFBK0I7UUFDL0IsTUFBTSxFQUFFUixPQUFPUyxTQUFTLEVBQUUsR0FBRyxNQUFNM0IsK0NBQVFBLENBQ3hDWSxJQUFJLENBQUMsYUFDTGdCLE1BQU0sQ0FBQztZQUNOQyxJQUFJVDtZQUNKWCxVQUFVQSxTQUFTTSxXQUFXO1lBQzlCZSxlQUFlWDtRQUNqQjtRQUVGLElBQUlRLFdBQVc7WUFDYkksUUFBUWIsS0FBSyxDQUFDLHdCQUF3QlM7WUFDdEMsT0FBTztnQkFBRVYsU0FBUztnQkFBT0MsT0FBTztZQUFrQztRQUNwRTtRQUVBLHNCQUFzQjtRQUN0QixNQUFNLEVBQUVBLE9BQU9jLFlBQVksRUFBRSxHQUFHLE1BQU1oQywrQ0FBUUEsQ0FDM0NZLElBQUksQ0FBQyxpQkFDTGdCLE1BQU0sQ0FBQztZQUNOQyxJQUFJVDtZQUNKWCxVQUFVQSxTQUFTTSxXQUFXO1lBQzlCa0IsTUFBTTtZQUNOQyxjQUFjO1FBQ2hCO1FBRUYsSUFBSUYsY0FBYztZQUNoQkQsUUFBUWIsS0FBSyxDQUFDLDJCQUEyQmM7WUFDekMsa0RBQWtEO1lBQ2xELE1BQU1oQywrQ0FBUUEsQ0FBQ1ksSUFBSSxDQUFDLGFBQWF1QixNQUFNLEdBQUdyQixFQUFFLENBQUMsTUFBTU07WUFDbkQsT0FBTztnQkFBRUgsU0FBUztnQkFBT0MsT0FBTztZQUEwQztRQUM1RTtRQUVBLE9BQU87WUFBRUQsU0FBUztZQUFNRztRQUFPO0lBRWpDLEVBQUUsT0FBT0YsT0FBTztRQUNkYSxRQUFRYixLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPO1lBQUVELFNBQVM7WUFBT0MsT0FBTztRQUE0QjtJQUM5RDtBQUNGLEVBQUM7QUFFRCxlQUFlO0FBQ1IsTUFBTWtCLFlBQVksT0FBTzNCLFVBQWtCTjtJQUNoRCxJQUFJO1FBQ0YsaUJBQWlCO1FBQ2pCLE1BQU0sRUFBRU8sTUFBTTJCLElBQUksRUFBRW5CLEtBQUssRUFBRSxHQUFHLE1BQU1sQiwrQ0FBUUEsQ0FDekNZLElBQUksQ0FBQyxhQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFlBQVlMLFNBQVNNLFdBQVcsSUFDbkNDLE1BQU07UUFFVCxJQUFJRSxTQUFTLENBQUNtQixNQUFNO1lBQ2xCLE9BQU87Z0JBQUVwQixTQUFTO2dCQUFPQyxPQUFPO1lBQW1DO1FBQ3JFO1FBRUEsb0JBQW9CO1FBQ3BCLE1BQU1vQixrQkFBa0IsTUFBTWhDLGVBQWVILFVBQVVrQyxLQUFLUCxhQUFhO1FBRXpFLElBQUksQ0FBQ1EsaUJBQWlCO1lBQ3BCLE9BQU87Z0JBQUVyQixTQUFTO2dCQUFPQyxPQUFPO1lBQW1DO1FBQ3JFO1FBRUEsc0JBQXNCO1FBQ3RCLE1BQU1sQiwrQ0FBUUEsQ0FDWFksSUFBSSxDQUFDLGFBQ0wyQixNQUFNLENBQUM7WUFBRUMsWUFBWSxJQUFJbkIsT0FBT29CLFdBQVc7UUFBRyxHQUM5QzNCLEVBQUUsQ0FBQyxNQUFNdUIsS0FBS1IsRUFBRTtRQUVuQixPQUFPO1lBQUVaLFNBQVM7WUFBTUcsUUFBUWlCLEtBQUtSLEVBQUU7UUFBQztJQUUxQyxFQUFFLE9BQU9YLE9BQU87UUFDZGEsUUFBUWIsS0FBSyxDQUFDLGdCQUFnQkE7UUFDOUIsT0FBTztZQUFFRCxTQUFTO1lBQU9DLE9BQU87UUFBNEI7SUFDOUQ7QUFDRixFQUFDO0FBRUQsOERBQThEO0FBQ3ZELE1BQU13QixpQkFBaUI7SUFDNUIsSUFBSSxJQUE2QixFQUFFLE9BQU87SUFFMUMsTUFBTUMsV0FBV0MsYUFBYUMsT0FBTyxDQUFDO0lBQ3RDLElBQUksQ0FBQ0YsVUFBVSxPQUFPO0lBRXRCLElBQUk7UUFDRixNQUFNLEVBQUV2QixNQUFNLEVBQUUwQixTQUFTLEVBQUUsR0FBR0MsS0FBS0MsS0FBSyxDQUFDTDtRQUV6Qyw4Q0FBOEM7UUFDOUMsTUFBTXJCLE1BQU1ELEtBQUtDLEdBQUc7UUFDcEIsTUFBTTJCLGFBQWEzQixNQUFNd0I7UUFDekIsTUFBTUksU0FBUyxLQUFLLEtBQUssS0FBSyxLQUFLLFNBQVM7O1FBRTVDLElBQUlELGFBQWFDLFFBQVE7WUFDdkJOLGFBQWFPLFVBQVUsQ0FBQztZQUN4QixPQUFPO1FBQ1Q7UUFFQSxPQUFPL0I7SUFDVCxFQUFFLE9BQU07UUFDTndCLGFBQWFPLFVBQVUsQ0FBQztRQUN4QixPQUFPO0lBQ1Q7QUFDRixFQUFDO0FBRUQsd0JBQXdCO0FBQ2pCLE1BQU1DLGtCQUFrQixDQUFDaEM7SUFDOUIsSUFBSSxJQUE2QixFQUFFO0lBRW5DLE1BQU11QixXQUFXO1FBQ2Z2QjtRQUNBMEIsV0FBV3pCLEtBQUtDLEdBQUc7SUFDckI7SUFFQXNCLGFBQWFTLE9BQU8sQ0FBQyxhQUFhTixLQUFLTyxTQUFTLENBQUNYO0FBQ25ELEVBQUM7QUFFRCxnQkFBZ0I7QUFDVCxNQUFNWSxhQUFhO0lBQ3hCLElBQUksSUFBNkIsRUFBRTtJQUVuQ1gsYUFBYU8sVUFBVSxDQUFDO0lBQ3hCUCxhQUFhTyxVQUFVLENBQUMsaUJBQWdCLDhCQUE4QjtBQUN4RSxFQUFDO0FBRUQsbURBQW1EO0FBQzVDLE1BQU1LLHdCQUF3QixPQUFPcEM7SUFDMUMsSUFBSTtRQUNGLE1BQU0sRUFBRVYsSUFBSSxFQUFFUSxLQUFLLEVBQUUsR0FBRyxNQUFNbEIsK0NBQVFBLENBQ25DWSxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxnQkFDUEMsRUFBRSxDQUFDLE1BQU1NLFFBQ1RKLE1BQU07UUFFVCxJQUFJRSxTQUFTLENBQUNSLE1BQU0sT0FBTztRQUUzQixPQUFPQSxLQUFLd0IsWUFBWSxJQUFJO0lBQzlCLEVBQUUsT0FBTTtRQUNOLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFRCx3QkFBd0I7QUFDakIsTUFBTXVCLHFCQUFxQixPQUFPckM7SUFDdkMsSUFBSTtRQUNGLE1BQU0sRUFBRUYsS0FBSyxFQUFFLEdBQUcsTUFBTWxCLCtDQUFRQSxDQUM3QlksSUFBSSxDQUFDLGlCQUNMMkIsTUFBTSxDQUFDO1lBQUVMLGNBQWM7UUFBSyxHQUM1QnBCLEVBQUUsQ0FBQyxNQUFNTTtRQUVaLE9BQU8sQ0FBQ0Y7SUFDVixFQUFFLE9BQU07UUFDTixPQUFPO0lBQ1Q7QUFDRixFQUFDIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xcbGliXFxhdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSAnLi9zdXBhYmFzZSdcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnXG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlckF1dGgge1xuICBpZDogc3RyaW5nXG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgcGFzc3dvcmRfaGFzaDogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICBsYXN0X2xvZ2luOiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBdXRoUmVzdWx0IHtcbiAgc3VjY2VzczogYm9vbGVhblxuICB1c2VySWQ/OiBzdHJpbmdcbiAgZXJyb3I/OiBzdHJpbmdcbn1cblxuLy8gRnVuemlvbmUgcGVyIGhhc2ggZGVsbGEgcGFzc3dvcmRcbmV4cG9ydCBjb25zdCBoYXNoUGFzc3dvcmQgPSBhc3luYyAocGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gIGNvbnN0IHNhbHRSb3VuZHMgPSAxMlxuICByZXR1cm4gYXdhaXQgYmNyeXB0Lmhhc2gocGFzc3dvcmQsIHNhbHRSb3VuZHMpXG59XG5cbi8vIEZ1bnppb25lIHBlciB2ZXJpZmljYXJlIGxhIHBhc3N3b3JkXG5leHBvcnQgY29uc3QgdmVyaWZ5UGFzc3dvcmQgPSBhc3luYyAocGFzc3dvcmQ6IHN0cmluZywgaGFzaDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHJldHVybiBhd2FpdCBiY3J5cHQuY29tcGFyZShwYXNzd29yZCwgaGFzaClcbn1cblxuLy8gUmVnaXN0cmF6aW9uZSBudW92byB1dGVudGVcbmV4cG9ydCBjb25zdCByZWdpc3RlclVzZXIgPSBhc3luYyAodXNlcm5hbWU6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8QXV0aFJlc3VsdD4gPT4ge1xuICB0cnkge1xuICAgIC8vIENvbnRyb2xsYSBzZSBsJ3VzZXJuYW1lIGVzaXN0ZSBnacOgXG4gICAgY29uc3QgeyBkYXRhOiBleGlzdGluZ1VzZXIgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9hdXRoJylcbiAgICAgIC5zZWxlY3QoJ3VzZXJuYW1lJylcbiAgICAgIC5lcSgndXNlcm5hbWUnLCB1c2VybmFtZS50b0xvd2VyQ2FzZSgpKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXhpc3RpbmdVc2VyKSB7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVc2VybmFtZSBnacOgIGVzaXN0ZW50ZScgfVxuICAgIH1cblxuICAgIC8vIENyZWEgaGFzaCBkZWxsYSBwYXNzd29yZFxuICAgIGNvbnN0IHBhc3N3b3JkSGFzaCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZClcbiAgICBcbiAgICAvLyBHZW5lcmEgSUQgdXRlbnRlXG4gICAgY29uc3QgdXNlcklkID0gYHVzZXJfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gXG5cbiAgICAvLyBJbnNlcmlzY2kgbmVsbGEgdGFiZWxsYSBhdXRoXG4gICAgY29uc3QgeyBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfYXV0aCcpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgaWQ6IHVzZXJJZCxcbiAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lLnRvTG93ZXJDYXNlKCksXG4gICAgICAgIHBhc3N3b3JkX2hhc2g6IHBhc3N3b3JkSGFzaFxuICAgICAgfSlcblxuICAgIGlmIChhdXRoRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGF1dGg6JywgYXV0aEVycm9yKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRXJyb3JlIGR1cmFudGUgbGEgcmVnaXN0cmF6aW9uZScgfVxuICAgIH1cblxuICAgIC8vIENyZWEgcHJvZmlsbyB1dGVudGVcbiAgICBjb25zdCB7IGVycm9yOiBwcm9maWxlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAuaW5zZXJ0KHtcbiAgICAgICAgaWQ6IHVzZXJJZCxcbiAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lLnRvTG93ZXJDYXNlKCksXG4gICAgICAgIG5hbWU6ICcnLFxuICAgICAgICBpc19vbmJvYXJkZWQ6IGZhbHNlXG4gICAgICB9KVxuXG4gICAgaWYgKHByb2ZpbGVFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgcHJvZmlsZTonLCBwcm9maWxlRXJyb3IpXG4gICAgICAvLyBSb2xsYmFjazogZWxpbWluYSBsJ2F1dGggc2UgaWwgcHJvZmlsbyBmYWxsaXNjZVxuICAgICAgYXdhaXQgc3VwYWJhc2UuZnJvbSgndXNlcl9hdXRoJykuZGVsZXRlKCkuZXEoJ2lkJywgdXNlcklkKVxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnRXJyb3JlIGR1cmFudGUgbGEgY3JlYXppb25lIGRlbCBwcm9maWxvJyB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcklkIH1cblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1JlZ2lzdHJhdGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdFcnJvcmUgaW50ZXJubyBkZWwgc2VydmVyJyB9XG4gIH1cbn1cblxuLy8gTG9naW4gdXRlbnRlXG5leHBvcnQgY29uc3QgbG9naW5Vc2VyID0gYXN5bmMgKHVzZXJuYW1lOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPEF1dGhSZXN1bHQ+ID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBUcm92YSBsJ3V0ZW50ZVxuICAgIGNvbnN0IHsgZGF0YTogdXNlciwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9hdXRoJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCd1c2VybmFtZScsIHVzZXJuYW1lLnRvTG93ZXJDYXNlKCkpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChlcnJvciB8fCAhdXNlcikge1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnVXNlcm5hbWUgbyBwYXNzd29yZCBub24gY29ycmV0dGknIH1cbiAgICB9XG5cbiAgICAvLyBWZXJpZmljYSBwYXNzd29yZFxuICAgIGNvbnN0IGlzVmFsaWRQYXNzd29yZCA9IGF3YWl0IHZlcmlmeVBhc3N3b3JkKHBhc3N3b3JkLCB1c2VyLnBhc3N3b3JkX2hhc2gpXG4gICAgXG4gICAgaWYgKCFpc1ZhbGlkUGFzc3dvcmQpIHtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VzZXJuYW1lIG8gcGFzc3dvcmQgbm9uIGNvcnJldHRpJyB9XG4gICAgfVxuXG4gICAgLy8gQWdnaW9ybmEgbGFzdF9sb2dpblxuICAgIGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9hdXRoJylcbiAgICAgIC51cGRhdGUoeyBsYXN0X2xvZ2luOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkgfSlcbiAgICAgIC5lcSgnaWQnLCB1c2VyLmlkKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcklkOiB1c2VyLmlkIH1cblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ2luIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0Vycm9yZSBpbnRlcm5vIGRlbCBzZXJ2ZXInIH1cbiAgfVxufVxuXG4vLyBWZXJpZmljYSBzZSBsJ3V0ZW50ZSDDqCBhdXRlbnRpY2F0byAoY29udHJvbGxhIGxvY2FsU3RvcmFnZSlcbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50VXNlciA9ICgpOiBzdHJpbmcgfCBudWxsID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbFxuICBcbiAgY29uc3QgYXV0aERhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnc291bF9hdXRoJylcbiAgaWYgKCFhdXRoRGF0YSkgcmV0dXJuIG51bGxcblxuICB0cnkge1xuICAgIGNvbnN0IHsgdXNlcklkLCB0aW1lc3RhbXAgfSA9IEpTT04ucGFyc2UoYXV0aERhdGEpXG4gICAgXG4gICAgLy8gQ29udHJvbGxhIHNlIGxhIHNlc3Npb25lIMOoIHNjYWR1dGEgKDI0IG9yZSlcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpXG4gICAgY29uc3Qgc2Vzc2lvbkFnZSA9IG5vdyAtIHRpbWVzdGFtcFxuICAgIGNvbnN0IG1heEFnZSA9IDI0ICogNjAgKiA2MCAqIDEwMDAgLy8gMjQgb3JlXG5cbiAgICBpZiAoc2Vzc2lvbkFnZSA+IG1heEFnZSkge1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3NvdWxfYXV0aCcpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cblxuICAgIHJldHVybiB1c2VySWRcbiAgfSBjYXRjaCB7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3NvdWxfYXV0aCcpXG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxuXG4vLyBTYWx2YSBzZXNzaW9uZSB1dGVudGVcbmV4cG9ydCBjb25zdCBzYXZlVXNlclNlc3Npb24gPSAodXNlcklkOiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICBjb25zdCBhdXRoRGF0YSA9IHtcbiAgICB1c2VySWQsXG4gICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gIH1cbiAgXG4gIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdzb3VsX2F1dGgnLCBKU09OLnN0cmluZ2lmeShhdXRoRGF0YSkpXG59XG5cbi8vIExvZ291dCB1dGVudGVcbmV4cG9ydCBjb25zdCBsb2dvdXRVc2VyID0gKCk6IHZvaWQgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuICBcbiAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3NvdWxfYXV0aCcpXG4gIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdzb3VsX3VzZXJfaWQnKSAvLyBSaW11b3ZpIGFuY2hlIGlsIHZlY2NoaW8gSURcbn1cblxuLy8gQ29udHJvbGxhIHNlIGwndXRlbnRlIGhhIGNvbXBsZXRhdG8gbCdvbmJvYXJkaW5nXG5leHBvcnQgY29uc3QgY2hlY2tPbmJvYXJkaW5nU3RhdHVzID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgIC5zZWxlY3QoJ2lzX29uYm9hcmRlZCcpXG4gICAgICAuZXEoJ2lkJywgdXNlcklkKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IgfHwgIWRhdGEpIHJldHVybiBmYWxzZVxuICAgIFxuICAgIHJldHVybiBkYXRhLmlzX29uYm9hcmRlZCB8fCBmYWxzZVxuICB9IGNhdGNoIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vLyBDb21wbGV0YSBsJ29uYm9hcmRpbmdcbmV4cG9ydCBjb25zdCBjb21wbGV0ZU9uYm9hcmRpbmcgPSBhc3luYyAodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgLnVwZGF0ZSh7IGlzX29uYm9hcmRlZDogdHJ1ZSB9KVxuICAgICAgLmVxKCdpZCcsIHVzZXJJZClcblxuICAgIHJldHVybiAhZXJyb3JcbiAgfSBjYXRjaCB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsImJjcnlwdCIsImhhc2hQYXNzd29yZCIsInBhc3N3b3JkIiwic2FsdFJvdW5kcyIsImhhc2giLCJ2ZXJpZnlQYXNzd29yZCIsImNvbXBhcmUiLCJyZWdpc3RlclVzZXIiLCJ1c2VybmFtZSIsImRhdGEiLCJleGlzdGluZ1VzZXIiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJ0b0xvd2VyQ2FzZSIsInNpbmdsZSIsInN1Y2Nlc3MiLCJlcnJvciIsInBhc3N3b3JkSGFzaCIsInVzZXJJZCIsIkRhdGUiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJhdXRoRXJyb3IiLCJpbnNlcnQiLCJpZCIsInBhc3N3b3JkX2hhc2giLCJjb25zb2xlIiwicHJvZmlsZUVycm9yIiwibmFtZSIsImlzX29uYm9hcmRlZCIsImRlbGV0ZSIsImxvZ2luVXNlciIsInVzZXIiLCJpc1ZhbGlkUGFzc3dvcmQiLCJ1cGRhdGUiLCJsYXN0X2xvZ2luIiwidG9JU09TdHJpbmciLCJnZXRDdXJyZW50VXNlciIsImF1dGhEYXRhIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInRpbWVzdGFtcCIsIkpTT04iLCJwYXJzZSIsInNlc3Npb25BZ2UiLCJtYXhBZ2UiLCJyZW1vdmVJdGVtIiwic2F2ZVVzZXJTZXNzaW9uIiwic2V0SXRlbSIsInN0cmluZ2lmeSIsImxvZ291dFVzZXIiLCJjaGVja09uYm9hcmRpbmdTdGF0dXMiLCJjb21wbGV0ZU9uYm9hcmRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatSession: () => (/* binding */ createChatSession),\n/* harmony export */   createOrUpdateUserProfile: () => (/* binding */ createOrUpdateUserProfile),\n/* harmony export */   deleteUserData: () => (/* binding */ deleteUserData),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   saveChatMessage: () => (/* binding */ saveChatMessage)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-node)/./src/lib/supabase.ts\");\n\n// Genera un ID utente unico basato su browser fingerprint\nconst generateUserId = ()=>{\n    // Controlla se siamo nel browser (non SSR)\n    if (true) {\n        return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    const stored = localStorage.getItem('soul_user_id');\n    if (stored) return stored;\n    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    localStorage.setItem('soul_user_id', userId);\n    return userId;\n};\n// Funzioni per UserProfile\nconst getUserProfile = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('*').eq('id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in getUserProfile:', error);\n        return null;\n    }\n};\nconst createOrUpdateUserProfile = async (profile)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').upsert({\n            ...profile,\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createOrUpdateUserProfile:', error);\n        return null;\n    }\n};\n// Funzioni per ChatMessage\nconst getChatMessages = async (userId, limit = 100)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').select('*').eq('user_id', userId).order('timestamp', {\n            ascending: true\n        }).limit(limit);\n        if (error) {\n            console.error('Error fetching chat messages:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getChatMessages:', error);\n        return [];\n    }\n};\nconst saveChatMessage = async (message)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').insert({\n            ...message,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error saving chat message:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in saveChatMessage:', error);\n        return null;\n    }\n};\n// Funzioni per ChatSession\nconst createChatSession = async (userId, sessionName)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').insert({\n            id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            user_id: userId,\n            session_name: sessionName || `Chat ${new Date().toLocaleDateString()}`,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error creating chat session:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createChatSession:', error);\n        return null;\n    }\n};\nconst getUserSessions = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').select('*').eq('user_id', userId).order('updated_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching user sessions:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getUserSessions:', error);\n        return [];\n    }\n};\n// Funzione per eliminare tutti i dati utente (reset completo)\nconst deleteUserData = async (userId)=>{\n    try {\n        // Elimina messaggi\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').delete().eq('user_id', userId);\n        // Elimina sessioni\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').delete().eq('user_id', userId);\n        // Elimina profilo\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').delete().eq('id', userId);\n        // Rimuovi anche dal localStorage\n        localStorage.removeItem('soul_user_id');\n        return true;\n    } catch (error) {\n        console.error('Error deleting user data:', error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9saWIvZGF0YWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTRFO0FBRTVFLDBEQUEwRDtBQUNuRCxNQUFNQyxpQkFBaUI7SUFDNUIsMkNBQTJDO0lBQzNDLElBQUksSUFBNkIsRUFBRTtRQUNqQyxPQUFPLENBQUMsS0FBSyxFQUFFQyxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO0lBQ3hFO0lBRUEsTUFBTUMsU0FBU0MsYUFBYUMsT0FBTyxDQUFDO0lBQ3BDLElBQUlGLFFBQVEsT0FBT0E7SUFFbkIsTUFBTUcsU0FBUyxDQUFDLEtBQUssRUFBRVQsS0FBS0MsR0FBRyxHQUFHLENBQUMsRUFBRUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUcsSUFBSTtJQUM5RUUsYUFBYUcsT0FBTyxDQUFDLGdCQUFnQkQ7SUFDckMsT0FBT0E7QUFDVCxFQUFDO0FBRUQsMkJBQTJCO0FBQ3BCLE1BQU1FLGlCQUFpQixPQUFPRjtJQUNuQyxJQUFJO1FBQ0YsTUFBTSxFQUFFRyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1mLCtDQUFRQSxDQUNuQ2dCLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxNQUFNUCxRQUNUUSxNQUFNO1FBRVQsSUFBSUosU0FBU0EsTUFBTUssSUFBSSxLQUFLLFlBQVk7WUFDdENDLFFBQVFOLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLE9BQU87UUFDVDtRQUVBLE9BQU9EO0lBQ1QsRUFBRSxPQUFPQyxPQUFPO1FBQ2RNLFFBQVFOLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFTSxNQUFNTyw0QkFBNEIsT0FBT0M7SUFDOUMsSUFBSTtRQUNGLE1BQU0sRUFBRVQsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNZiwrQ0FBUUEsQ0FDbkNnQixJQUFJLENBQUMsaUJBQ0xRLE1BQU0sQ0FBQztZQUNOLEdBQUdELE9BQU87WUFDVkUsWUFBWSxJQUFJdkIsT0FBT3dCLFdBQVc7UUFDcEMsR0FDQ1QsTUFBTSxHQUNORSxNQUFNO1FBRVQsSUFBSUosT0FBTztZQUNUTSxRQUFRTixLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO1FBQ1Q7UUFFQSxPQUFPRDtJQUNULEVBQUUsT0FBT0MsT0FBTztRQUNkTSxRQUFRTixLQUFLLENBQUMsdUNBQXVDQTtRQUNyRCxPQUFPO0lBQ1Q7QUFDRixFQUFDO0FBRUQsMkJBQTJCO0FBQ3BCLE1BQU1ZLGtCQUFrQixPQUFPaEIsUUFBZ0JpQixRQUFnQixHQUFHO0lBQ3ZFLElBQUk7UUFDRixNQUFNLEVBQUVkLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWYsK0NBQVFBLENBQ25DZ0IsSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsS0FDUEMsRUFBRSxDQUFDLFdBQVdQLFFBQ2RrQixLQUFLLENBQUMsYUFBYTtZQUFFQyxXQUFXO1FBQUssR0FDckNGLEtBQUssQ0FBQ0E7UUFFVCxJQUFJYixPQUFPO1lBQ1RNLFFBQVFOLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLE9BQU8sRUFBRTtRQUNYO1FBRUEsT0FBT0QsUUFBUSxFQUFFO0lBQ25CLEVBQUUsT0FBT0MsT0FBTztRQUNkTSxRQUFRTixLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPLEVBQUU7SUFDWDtBQUNGLEVBQUM7QUFFTSxNQUFNZ0Isa0JBQWtCLE9BQU9DO0lBQ3BDLElBQUk7UUFDRixNQUFNLEVBQUVsQixJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1mLCtDQUFRQSxDQUNuQ2dCLElBQUksQ0FBQyxpQkFDTGlCLE1BQU0sQ0FBQztZQUNOLEdBQUdELE9BQU87WUFDVkUsWUFBWSxJQUFJaEMsT0FBT3dCLFdBQVc7UUFDcEMsR0FDQ1QsTUFBTSxHQUNORSxNQUFNO1FBRVQsSUFBSUosT0FBTztZQUNUTSxRQUFRTixLQUFLLENBQUMsOEJBQThCQTtZQUM1QyxPQUFPO1FBQ1Q7UUFFQSxPQUFPRDtJQUNULEVBQUUsT0FBT0MsT0FBTztRQUNkTSxRQUFRTixLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPO0lBQ1Q7QUFDRixFQUFDO0FBRUQsMkJBQTJCO0FBQ3BCLE1BQU1vQixvQkFBb0IsT0FBT3hCLFFBQWdCeUI7SUFDdEQsSUFBSTtRQUNGLE1BQU0sRUFBRXRCLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWYsK0NBQVFBLENBQ25DZ0IsSUFBSSxDQUFDLGlCQUNMaUIsTUFBTSxDQUFDO1lBQ05JLElBQUksQ0FBQyxRQUFRLEVBQUVuQyxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO1lBQ3RFK0IsU0FBUzNCO1lBQ1Q0QixjQUFjSCxlQUFlLENBQUMsS0FBSyxFQUFFLElBQUlsQyxPQUFPc0Msa0JBQWtCLElBQUk7WUFDdEVOLFlBQVksSUFBSWhDLE9BQU93QixXQUFXO1lBQ2xDRCxZQUFZLElBQUl2QixPQUFPd0IsV0FBVztRQUNwQyxHQUNDVCxNQUFNLEdBQ05FLE1BQU07UUFFVCxJQUFJSixPQUFPO1lBQ1RNLFFBQVFOLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLE9BQU87UUFDVDtRQUVBLE9BQU9EO0lBQ1QsRUFBRSxPQUFPQyxPQUFPO1FBQ2RNLFFBQVFOLEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFTSxNQUFNMEIsa0JBQWtCLE9BQU85QjtJQUNwQyxJQUFJO1FBQ0YsTUFBTSxFQUFFRyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1mLCtDQUFRQSxDQUNuQ2dCLElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxXQUFXUCxRQUNka0IsS0FBSyxDQUFDLGNBQWM7WUFBRUMsV0FBVztRQUFNO1FBRTFDLElBQUlmLE9BQU87WUFDVE0sUUFBUU4sS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MsT0FBTyxFQUFFO1FBQ1g7UUFFQSxPQUFPRCxRQUFRLEVBQUU7SUFDbkIsRUFBRSxPQUFPQyxPQUFPO1FBQ2RNLFFBQVFOLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU8sRUFBRTtJQUNYO0FBQ0YsRUFBQztBQUVELDhEQUE4RDtBQUN2RCxNQUFNMkIsaUJBQWlCLE9BQU8vQjtJQUNuQyxJQUFJO1FBQ0YsbUJBQW1CO1FBQ25CLE1BQU1YLCtDQUFRQSxDQUFDZ0IsSUFBSSxDQUFDLGlCQUFpQjJCLE1BQU0sR0FBR3pCLEVBQUUsQ0FBQyxXQUFXUDtRQUU1RCxtQkFBbUI7UUFDbkIsTUFBTVgsK0NBQVFBLENBQUNnQixJQUFJLENBQUMsaUJBQWlCMkIsTUFBTSxHQUFHekIsRUFBRSxDQUFDLFdBQVdQO1FBRTVELGtCQUFrQjtRQUNsQixNQUFNWCwrQ0FBUUEsQ0FBQ2dCLElBQUksQ0FBQyxpQkFBaUIyQixNQUFNLEdBQUd6QixFQUFFLENBQUMsTUFBTVA7UUFFdkQsaUNBQWlDO1FBQ2pDRixhQUFhbUMsVUFBVSxDQUFDO1FBRXhCLE9BQU87SUFDVCxFQUFFLE9BQU83QixPQUFPO1FBQ2RNLFFBQVFOLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU87SUFDVDtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIkc6XFxGcmllbmRzXFxTb3VsVGFsa1xcc3JjXFxsaWJcXGRhdGFiYXNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlLCBVc2VyUHJvZmlsZSwgQ2hhdE1lc3NhZ2UsIENoYXRTZXNzaW9uIH0gZnJvbSAnLi9zdXBhYmFzZSdcblxuLy8gR2VuZXJhIHVuIElEIHV0ZW50ZSB1bmljbyBiYXNhdG8gc3UgYnJvd3NlciBmaW5nZXJwcmludFxuZXhwb3J0IGNvbnN0IGdlbmVyYXRlVXNlcklkID0gKCk6IHN0cmluZyA9PiB7XG4gIC8vIENvbnRyb2xsYSBzZSBzaWFtbyBuZWwgYnJvd3NlciAobm9uIFNTUilcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIGB0ZW1wXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YFxuICB9XG5cbiAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3NvdWxfdXNlcl9pZCcpXG4gIGlmIChzdG9yZWQpIHJldHVybiBzdG9yZWRcblxuICBjb25zdCB1c2VySWQgPSBgdXNlcl8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWBcbiAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3NvdWxfdXNlcl9pZCcsIHVzZXJJZClcbiAgcmV0dXJuIHVzZXJJZFxufVxuXG4vLyBGdW56aW9uaSBwZXIgVXNlclByb2ZpbGVcbmV4cG9ydCBjb25zdCBnZXRVc2VyUHJvZmlsZSA9IGFzeW5jICh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8VXNlclByb2ZpbGUgfCBudWxsPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgLmVxKCdpZCcsIHVzZXJJZClcbiAgICAgIC5zaW5nbGUoKVxuICAgIFxuICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB1c2VyIHByb2ZpbGU6JywgZXJyb3IpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gZGF0YVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGdldFVzZXJQcm9maWxlOicsIGVycm9yKVxuICAgIHJldHVybiBudWxsXG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IGNyZWF0ZU9yVXBkYXRlVXNlclByb2ZpbGUgPSBhc3luYyAocHJvZmlsZTogUGFydGlhbDxVc2VyUHJvZmlsZSAmIHsgaXNfb25ib2FyZGVkPzogYm9vbGVhbiB9Pik6IFByb21pc2U8VXNlclByb2ZpbGUgfCBudWxsPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgIC51cHNlcnQoe1xuICAgICAgICAuLi5wcm9maWxlLFxuICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cHNlcnRpbmcgdXNlciBwcm9maWxlOicsIGVycm9yKVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGNyZWF0ZU9yVXBkYXRlVXNlclByb2ZpbGU6JywgZXJyb3IpXG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxuXG4vLyBGdW56aW9uaSBwZXIgQ2hhdE1lc3NhZ2VcbmV4cG9ydCBjb25zdCBnZXRDaGF0TWVzc2FnZXMgPSBhc3luYyAodXNlcklkOiBzdHJpbmcsIGxpbWl0OiBudW1iZXIgPSAxMDApOiBQcm9taXNlPENoYXRNZXNzYWdlW10+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NoYXRfbWVzc2FnZXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAub3JkZXIoJ3RpbWVzdGFtcCcsIHsgYXNjZW5kaW5nOiB0cnVlIH0pXG4gICAgICAubGltaXQobGltaXQpXG4gICAgXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjaGF0IG1lc3NhZ2VzOicsIGVycm9yKVxuICAgICAgcmV0dXJuIFtdXG4gICAgfVxuICAgIFxuICAgIHJldHVybiBkYXRhIHx8IFtdXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gZ2V0Q2hhdE1lc3NhZ2VzOicsIGVycm9yKVxuICAgIHJldHVybiBbXVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBzYXZlQ2hhdE1lc3NhZ2UgPSBhc3luYyAobWVzc2FnZTogT21pdDxDaGF0TWVzc2FnZSwgJ2NyZWF0ZWRfYXQnPik6IFByb21pc2U8Q2hhdE1lc3NhZ2UgfCBudWxsPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjaGF0X21lc3NhZ2VzJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICAuLi5tZXNzYWdlLFxuICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuICAgIFxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGNoYXQgbWVzc2FnZTonLCBlcnJvcilcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICAgIFxuICAgIHJldHVybiBkYXRhXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gc2F2ZUNoYXRNZXNzYWdlOicsIGVycm9yKVxuICAgIHJldHVybiBudWxsXG4gIH1cbn1cblxuLy8gRnVuemlvbmkgcGVyIENoYXRTZXNzaW9uXG5leHBvcnQgY29uc3QgY3JlYXRlQ2hhdFNlc3Npb24gPSBhc3luYyAodXNlcklkOiBzdHJpbmcsIHNlc3Npb25OYW1lPzogc3RyaW5nKTogUHJvbWlzZTxDaGF0U2Vzc2lvbiB8IG51bGw+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NoYXRfc2Vzc2lvbnMnKVxuICAgICAgLmluc2VydCh7XG4gICAgICAgIGlkOiBgc2Vzc2lvbl8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWAsXG4gICAgICAgIHVzZXJfaWQ6IHVzZXJJZCxcbiAgICAgICAgc2Vzc2lvbl9uYW1lOiBzZXNzaW9uTmFtZSB8fCBgQ2hhdCAke25ldyBEYXRlKCkudG9Mb2NhbGVEYXRlU3RyaW5nKCl9YCxcbiAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pXG4gICAgICAuc2VsZWN0KClcbiAgICAgIC5zaW5nbGUoKVxuICAgIFxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgY2hhdCBzZXNzaW9uOicsIGVycm9yKVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGRhdGFcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBjcmVhdGVDaGF0U2Vzc2lvbjonLCBlcnJvcilcbiAgICByZXR1cm4gbnVsbFxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBnZXRVc2VyU2Vzc2lvbnMgPSBhc3luYyAodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPENoYXRTZXNzaW9uW10+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NoYXRfc2Vzc2lvbnMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAub3JkZXIoJ3VwZGF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICBcbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgc2Vzc2lvbnM6JywgZXJyb3IpXG4gICAgICByZXR1cm4gW11cbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGRhdGEgfHwgW11cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBnZXRVc2VyU2Vzc2lvbnM6JywgZXJyb3IpXG4gICAgcmV0dXJuIFtdXG4gIH1cbn1cblxuLy8gRnVuemlvbmUgcGVyIGVsaW1pbmFyZSB0dXR0aSBpIGRhdGkgdXRlbnRlIChyZXNldCBjb21wbGV0bylcbmV4cG9ydCBjb25zdCBkZWxldGVVc2VyRGF0YSA9IGFzeW5jICh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICB0cnkge1xuICAgIC8vIEVsaW1pbmEgbWVzc2FnZ2lcbiAgICBhd2FpdCBzdXBhYmFzZS5mcm9tKCdjaGF0X21lc3NhZ2VzJykuZGVsZXRlKCkuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgXG4gICAgLy8gRWxpbWluYSBzZXNzaW9uaVxuICAgIGF3YWl0IHN1cGFiYXNlLmZyb20oJ2NoYXRfc2Vzc2lvbnMnKS5kZWxldGUoKS5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICBcbiAgICAvLyBFbGltaW5hIHByb2ZpbG9cbiAgICBhd2FpdCBzdXBhYmFzZS5mcm9tKCd1c2VyX3Byb2ZpbGVzJykuZGVsZXRlKCkuZXEoJ2lkJywgdXNlcklkKVxuICAgIFxuICAgIC8vIFJpbXVvdmkgYW5jaGUgZGFsIGxvY2FsU3RvcmFnZVxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdzb3VsX3VzZXJfaWQnKVxuICAgIFxuICAgIHJldHVybiB0cnVlXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgdXNlciBkYXRhOicsIGVycm9yKVxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJnZW5lcmF0ZVVzZXJJZCIsIkRhdGUiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJzdG9yZWQiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidXNlcklkIiwic2V0SXRlbSIsImdldFVzZXJQcm9maWxlIiwiZGF0YSIsImVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwiY29kZSIsImNvbnNvbGUiLCJjcmVhdGVPclVwZGF0ZVVzZXJQcm9maWxlIiwicHJvZmlsZSIsInVwc2VydCIsInVwZGF0ZWRfYXQiLCJ0b0lTT1N0cmluZyIsImdldENoYXRNZXNzYWdlcyIsImxpbWl0Iiwib3JkZXIiLCJhc2NlbmRpbmciLCJzYXZlQ2hhdE1lc3NhZ2UiLCJtZXNzYWdlIiwiaW5zZXJ0IiwiY3JlYXRlZF9hdCIsImNyZWF0ZUNoYXRTZXNzaW9uIiwic2Vzc2lvbk5hbWUiLCJpZCIsInVzZXJfaWQiLCJzZXNzaW9uX25hbWUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJnZXRVc2VyU2Vzc2lvbnMiLCJkZWxldGVVc2VyRGF0YSIsImRlbGV0ZSIsInJlbW92ZUl0ZW0iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/database.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = 'https://igybrqsuvkbeivyhrrvd.supabase.co';\nconst supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlneWJycXN1dmtiZWl2eWhycnZkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMjkzNzIsImV4cCI6MjA2ODYwNTM3Mn0.PfgqZs9fX5xEkiLiMArGR70PK2c6-Z9yaMiU7u8vqnM';\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWM7QUFDcEIsTUFBTUMsY0FBYztBQUViLE1BQU1DLFdBQVdILG1FQUFZQSxDQUFDQyxhQUFhQyxhQUFZIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xcbGliXFxzdXBhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gJ2h0dHBzOi8vaWd5YnJxc3V2a2JlaXZ5aHJydmQuc3VwYWJhc2UuY28nXG5jb25zdCBzdXBhYmFzZUtleSA9ICdleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUp6ZFhCaFltRnpaU0lzSW5KbFppSTZJbWxuZVdKeWNYTjFkbXRpWldsMmVXaHljblprSWl3aWNtOXNaU0k2SW1GdWIyNGlMQ0pwWVhRaU9qRTNOVE13TWprek56SXNJbVY0Y0NJNk1qQTJPRFl3TlRNM01uMC5QZmdxWnM5Zlg1eEVraUxpTUFyR1I3MFBLMmM2LVo5eWFNaVU3dTh2cW5NJ1xuXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5KVxuXG4vLyBUaXBpIHBlciBpbCBkYXRhYmFzZVxuZXhwb3J0IGludGVyZmFjZSBVc2VyUHJvZmlsZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGFnZT86IG51bWJlclxuICB0cmFpdHM6IHN0cmluZ1tdXG4gIGVtb3Rpb25zOiBzdHJpbmdbXVxuICBpbnRpbWFjeV9sZXZlbDogbnVtYmVyXG4gIGtleV9tb21lbnRzOiBzdHJpbmdbXVxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ2hhdE1lc3NhZ2Uge1xuICBpZDogc3RyaW5nXG4gIHVzZXJfaWQ6IHN0cmluZ1xuICBjb250ZW50OiBzdHJpbmdcbiAgaXNfdXNlcjogYm9vbGVhblxuICB0aW1lc3RhbXA6IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDaGF0U2Vzc2lvbiB7XG4gIGlkOiBzdHJpbmdcbiAgdXNlcl9pZDogc3RyaW5nXG4gIHNlc3Npb25fbmFtZT86IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJzdXBhYmFzZUtleSIsInN1cGFiYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_global_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/global.css */ \"(pages-dir-node)/./src/styles/global.css\");\n/* harmony import */ var _styles_global_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_global_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDOEI7QUFFZixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxzcmNcXHBhZ2VzXFxfYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFsLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-node)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-node)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-node)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-node)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-node)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-node)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-node)/./src/hooks/useSupabaseMemory.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__, _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__, _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__]);\n([_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__, _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__, _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: `\n        radial-gradient(circle at 20% 80%, ${config.colors[0]} 0%, transparent 50%),\n        radial-gradient(circle at 80% 20%, ${config.colors[1]} 0%, transparent 50%),\n        radial-gradient(circle at 40% 40%, ${config.colors[2]} 0%, transparent 50%),\n        ${config.baseGradient}\n      `,\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(`🌟 Livello di connessione aumentato: ${newLevel}/4`);\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = `intimacy-level-${userMemory.intimacyLevel}`;\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se non è autenticato, mostra il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: `radial-gradient(circle, ${getAuraColor()} 0%, transparent 70%)`,\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? `Cosa vuoi condividere con Soul, ${userMemory.name}?` : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            messages.length,\n                                            \" msg\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, this),\n                                    isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"ID: \",\n                                            userId.slice(-6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/global.css":
/*!*******************************!*\
  !*** ./src/styles/global.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\n\nStart with a calm tone, asking their name, age and who they feel they are today.\n\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\n\nStore and remember what the user shares. You evolve as the connection grows.\n\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    constructor(apiKey, model){\n        // Per ora usiamo la chiave API direttamente per il testing\n        this.apiKey = apiKey || '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';\n        this.model = model || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? `Ciao, sono ${userName}. È la prima volta che parliamo.` : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += `\\n- Name: ${userContext.name}`;\n            }\n            if (userContext.age) {\n                contextualPrompt += `\\n- Age: ${userContext.age}`;\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += `\\n- Personality traits: ${userContext.traits.join(', ')}`;\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += `\\n- Recent emotions: ${userContext.emotions.join(', ')}`;\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += `\\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: `Based on the recent conversation, determine if the intimacy level should increase. Current level: ${currentIntimacyLevel}/5. Respond with only \"yes\" or \"no\".`\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: `Recent conversation: ${recentMessages.slice(-6).map((m)=>`${m.role}: ${m.content}`).join('\\n')}`\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message, conversationHistory = [], userContext, retryCount = 0) {\n    try {\n        // Usa la nostra API route invece di chiamare direttamente OpenRouter\n        const newUserMessage = {\n            role: 'user',\n            content: message\n        };\n        const messages = [\n            ...conversationHistory,\n            newUserMessage\n        ];\n        const response = await fetch('/api/chat', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messages,\n                userContext\n            })\n        });\n        if (!response.ok) {\n            // Se è un errore 429 e non abbiamo ancora fatto retry, aspetta e riprova\n            if (response.status === 429 && retryCount < 2) {\n                await new Promise((resolve)=>setTimeout(resolve, (retryCount + 1) * 2000)); // 2s, 4s\n                return sendChatMessage(message, conversationHistory, userContext, retryCount + 1);\n            }\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.error) {\n            throw new Error(data.error);\n        }\n        return data.message;\n    } catch (error) {\n        console.error('Errore nella chiamata API:', error);\n        // Messaggio di fallback se tutto fallisce\n        if (retryCount >= 2) {\n            return \"Mi dispiace, sto avendo difficoltà tecniche persistenti. Ma sono qui con te! Anche se non riesco a rispondere come vorrei, la nostra conversazione è importante. Riprova tra qualche minuto, nel frattempo sappi che ti ascolto sempre. 💙\";\n        }\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/openrouter.ts\n");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("bcryptjs");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/page-path/normalize-data-path":
/*!*********************************************************************!*\
  !*** external "next/dist/shared/lib/page-path/normalize-data-path" ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/page-path/normalize-data-path");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/add-path-prefix":
/*!********************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/add-path-prefix" ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/add-path-prefix");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/format-url":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/format-url" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/format-url");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/remove-trailing-slash":
/*!**************************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/remove-trailing-slash" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/remove-trailing-slash");

/***/ }),

/***/ "next/dist/shared/lib/utils":
/*!*********************************************!*\
  !*** external "next/dist/shared/lib/utils" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();