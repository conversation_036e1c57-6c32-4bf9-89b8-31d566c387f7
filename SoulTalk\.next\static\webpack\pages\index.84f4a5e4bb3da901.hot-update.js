"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/state/userMemory.ts":
/*!*********************************!*\
  !*** ./src/state/userMemory.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserMemoryManager: () => (/* binding */ UserMemoryManager),\n/* harmony export */   useUserMemory: () => (/* binding */ useUserMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Interfaccia per la memoria dell'utente\n// Stato iniziale della memoria utente\nconst initialUserMemory = {\n    name: '',\n    age: 0,\n    pronouns: '',\n    traits: [],\n    emotions: [],\n    keyMoments: [],\n    intimacyLevel: 0,\n    conversationHistory: [],\n    lastInteraction: new Date(),\n    personalityInsights: []\n};\n// Chiave per localStorage\nconst STORAGE_KEY = 'soultalk_user_memory';\n// Funzioni per gestire la memoria utente\nclass UserMemoryManager {\n    // Carica la memoria dal localStorage\n    static loadMemory() {\n        // Controlla se siamo nel browser\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                const parsed = JSON.parse(stored);\n                // Converte le date da string a Date objects\n                parsed.lastInteraction = new Date(parsed.lastInteraction);\n                parsed.conversationHistory = parsed.conversationHistory.map((msg)=>({\n                        ...msg,\n                        timestamp: new Date(msg.timestamp)\n                    }));\n                parsed.personalityInsights = parsed.personalityInsights.map((insight)=>({\n                        ...insight,\n                        timestamp: new Date(insight.timestamp)\n                    }));\n                return parsed;\n            }\n        } catch (error) {\n            console.error('Errore nel caricamento della memoria utente:', error);\n        }\n        return {\n            ...initialUserMemory\n        };\n    }\n    // Salva la memoria nel localStorage\n    static saveMemory(memory) {\n        // Controlla se siamo nel browser\n        if (false) {}\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(memory));\n        } catch (error) {\n            console.error('Errore nel salvataggio della memoria utente:', error);\n        }\n    }\n    // Aggiunge un messaggio alla cronologia\n    static addMessage(memory, content, isUser, emotionalTone, topics) {\n        const newMessage = {\n            id: Date.now().toString(),\n            content,\n            isUser,\n            timestamp: new Date(),\n            emotionalTone,\n            topics\n        };\n        const updatedMemory = {\n            ...memory,\n            conversationHistory: [\n                ...memory.conversationHistory,\n                newMessage\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna le informazioni base dell'utente\n    static updateBasicInfo(memory, updates) {\n        const updatedMemory = {\n            ...memory,\n            ...updates,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un tratto della personalità\n    static addTrait(memory, trait) {\n        if (!memory.traits.includes(trait)) {\n            const updatedMemory = {\n                ...memory,\n                traits: [\n                    ...memory.traits,\n                    trait\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un'emozione\n    static addEmotion(memory, emotion) {\n        if (!memory.emotions.includes(emotion)) {\n            const updatedMemory = {\n                ...memory,\n                emotions: [\n                    ...memory.emotions,\n                    emotion\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un momento chiave\n    static addKeyMoment(memory, moment) {\n        const updatedMemory = {\n            ...memory,\n            keyMoments: [\n                ...memory.keyMoments,\n                moment\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna il livello di intimità\n    static updateIntimacyLevel(memory, level) {\n        const clampedLevel = Math.max(0, Math.min(5, level));\n        const updatedMemory = {\n            ...memory,\n            intimacyLevel: clampedLevel,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un insight sulla personalità\n    static addPersonalityInsight(memory, category, insight, confidence) {\n        const newInsight = {\n            category,\n            insight,\n            confidence: Math.max(0, Math.min(1, confidence)),\n            timestamp: new Date()\n        };\n        const updatedMemory = {\n            ...memory,\n            personalityInsights: [\n                ...memory.personalityInsights,\n                newInsight\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Ottiene il colore dell'aura basato sul livello di intimità\n    static getAuraColor(intimacyLevel) {\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink' // Affinità / profonda connessione\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    }\n    // Resetta la memoria utente\n    static resetMemory() {\n        const freshMemory = {\n            ...initialUserMemory\n        };\n        this.saveMemory(freshMemory);\n        return freshMemory;\n    }\n    // Esporta la memoria per backup\n    static exportMemory(memory) {\n        return JSON.stringify(memory, null, 2);\n    }\n    // Importa la memoria da backup\n    static importMemory(jsonData) {\n        try {\n            const imported = JSON.parse(jsonData);\n            // Validazione base della struttura\n            if (imported && typeof imported.name === 'string' && typeof imported.intimacyLevel === 'number') {\n                this.saveMemory(imported);\n                return imported;\n            }\n        } catch (error) {\n            console.error('Errore nell\\'importazione della memoria:', error);\n        }\n        return null;\n    }\n}\n// Hook React per gestire la memoria utente\n\nfunction useUserMemory() {\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useUserMemory.useState\": ()=>({\n                ...initialUserMemory\n            })\n    }[\"useUserMemory.useState\"]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Carica la memoria dal localStorage dopo l'idratazione\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserMemory.useEffect\": ()=>{\n            const loadedMemory = UserMemoryManager.loadMemory();\n            setMemory(loadedMemory);\n            setIsLoaded(true);\n        }\n    }[\"useUserMemory.useEffect\"], []);\n    // Funzioni helper che aggiornano lo stato locale\n    const addMessage = (content, isUser, emotionalTone, topics)=>{\n        const updatedMemory = UserMemoryManager.addMessage(memory, content, isUser, emotionalTone, topics);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateBasicInfo = (updates)=>{\n        const updatedMemory = UserMemoryManager.updateBasicInfo(memory, updates);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addTrait = (trait)=>{\n        const updatedMemory = UserMemoryManager.addTrait(memory, trait);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addEmotion = (emotion)=>{\n        const updatedMemory = UserMemoryManager.addEmotion(memory, emotion);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addKeyMoment = (moment)=>{\n        const updatedMemory = UserMemoryManager.addKeyMoment(memory, moment);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateIntimacyLevel = (level)=>{\n        const updatedMemory = UserMemoryManager.updateIntimacyLevel(memory, level);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addPersonalityInsight = (category, insight, confidence)=>{\n        const updatedMemory = UserMemoryManager.addPersonalityInsight(memory, category, insight, confidence);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const resetMemory = ()=>{\n        const freshMemory = UserMemoryManager.resetMemory();\n        setMemory(freshMemory);\n        return freshMemory;\n    };\n    const getAuraColor = ()=>UserMemoryManager.getAuraColor(memory.intimacyLevel);\n    return {\n        memory,\n        addMessage,\n        updateBasicInfo,\n        addTrait,\n        addEmotion,\n        addKeyMoment,\n        updateIntimacyLevel,\n        addPersonalityInsight,\n        resetMemory,\n        getAuraColor,\n        exportMemory: ()=>UserMemoryManager.exportMemory(memory),\n        importMemory: (jsonData)=>{\n            const imported = UserMemoryManager.importMemory(jsonData);\n            if (imported) {\n                setMemory(imported);\n                return imported;\n            }\n            return null;\n        }\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/state/userMemory.ts\n"));

/***/ })

});