{"name": "soultalk", "version": "1.0.0", "description": "Una piattaforma AI che si comporta come un'amicizia da costruire", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": ["ai", "chat", "friendship", "nextjs"], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.52.0", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-next": "^15.4.2", "next": "^15.4.2", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}