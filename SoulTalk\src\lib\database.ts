import { supabase, UserProfile, ChatMessage, ChatSession } from './supabase'

// Genera un ID utente unico basato su browser fingerprint
export const generateUserId = (): string => {
  const stored = localStorage.getItem('soul_user_id')
  if (stored) return stored
  
  const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  localStorage.setItem('soul_user_id', userId)
  return userId
}

// Funzioni per UserProfile
export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching user profile:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error in getUserProfile:', error)
    return null
  }
}

export const createOrUpdateUserProfile = async (profile: Partial<UserProfile>): Promise<UserProfile | null> => {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        ...profile,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error upserting user profile:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error in createOrUpdateUserProfile:', error)
    return null
  }
}

// Funzioni per ChatMessage
export const getChatMessages = async (userId: string, limit: number = 100): Promise<ChatMessage[]> => {
  try {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: true })
      .limit(limit)
    
    if (error) {
      console.error('Error fetching chat messages:', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('Error in getChatMessages:', error)
    return []
  }
}

export const saveChatMessage = async (message: Omit<ChatMessage, 'created_at'>): Promise<ChatMessage | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        ...message,
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error saving chat message:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error in saveChatMessage:', error)
    return null
  }
}

// Funzioni per ChatSession
export const createChatSession = async (userId: string, sessionName?: string): Promise<ChatSession | null> => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .insert({
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: userId,
        session_name: sessionName || `Chat ${new Date().toLocaleDateString()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error creating chat session:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error in createChatSession:', error)
    return null
  }
}

export const getUserSessions = async (userId: string): Promise<ChatSession[]> => {
  try {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching user sessions:', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('Error in getUserSessions:', error)
    return []
  }
}

// Funzione per eliminare tutti i dati utente (reset completo)
export const deleteUserData = async (userId: string): Promise<boolean> => {
  try {
    // Elimina messaggi
    await supabase.from('chat_messages').delete().eq('user_id', userId)
    
    // Elimina sessioni
    await supabase.from('chat_sessions').delete().eq('user_id', userId)
    
    // Elimina profilo
    await supabase.from('user_profiles').delete().eq('id', userId)
    
    // Rimuovi anche dal localStorage
    localStorage.removeItem('soul_user_id')
    
    return true
  } catch (error) {
    console.error('Error deleting user data:', error)
    return false
  }
}
