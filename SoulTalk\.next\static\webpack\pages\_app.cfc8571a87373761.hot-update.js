"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  background-attachment: fixed;\\n  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;\\n  animation: liquidMove 20s ease-in-out infinite;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n/* Chat Messages */\\n.message-container {\\n  margin-bottom: 1rem;\\n  display: flex;\\n}\\n\\n.message-container.user {\\n  justify-content: flex-end;\\n}\\n\\n.message-container.assistant {\\n  justify-content: flex-start;\\n}\\n\\n.message {\\n  max-width: 70%;\\n  padding: 0.75rem 1rem;\\n  border-radius: 1rem;\\n  word-wrap: break-word;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n}\\n\\n.user-message {\\n  background: #007AFF;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.assistant-message {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom-left-radius: 0.25rem;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  flex: 1 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1.5rem;\\n  padding: 0.75rem 1rem;\\n  color: white;\\n  font-size: 1rem;\\n  resize: none;\\n  min-height: 44px;\\n  max-height: 120px;\\n  outline: none;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-button {\\n  background: #007AFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.send-button:hover {\\n  background: #0056CC;\\n  transform: scale(1.05);\\n}\\n\\n.send-button:disabled {\\n  background: rgba(255, 255, 255, 0.2);\\n  cursor: not-allowed;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-container {\\n  height: 100vh;\\n  max-width: 800px;\\n  margin: 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  background: rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.chat-messages {\\n  flex: 1 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.chat-input-area {\\n  padding: 1rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n\\n.input-container {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: flex-end;\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 85%;\\n    min-width: 6rem;\\n    padding: 0.75rem 1rem;\\n    font-size: 0.875rem;\\n    line-height: 1.3;\\n  }\\n  .chat-main-container { max-width: 100%; margin: 0; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 90%;\\n    min-width: 5rem;\\n    padding: 0.625rem 0.875rem;\\n    font-size: 0.8rem;\\n    line-height: 1.2;\\n  }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n@keyframes liquidMove {\\n  0%, 100% {\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  25% {\\n    background:\\n      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  50% {\\n    background:\\n      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  75% {\\n    background:\\n      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/global.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ;;;;kEAIgE;EAChE,4BAA4B;EAC5B,2DAA2D;EAC3D,8CAA8C;AAChD;;AAEA,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,mBAAmB,kBAAkB,EAAE;AACvC,uBAAuB,qBAAqB,EAAE;AAC9C,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,sBAAsB,EAAE;AAChC,qBAAqB,kBAAkB,EAAE;AACzC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,kBAAkB,EAAE,mBAAmB,EAAE;AACrD,WAAW,eAAe,EAAE,oBAAoB,EAAE;AAClD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,WAAW,mBAAmB,EAAE,qBAAqB,EAAE;AACvD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,SAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;;AAEzB,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,kDAA0C;UAA1C,0CAA0C;EAC1C,qCAAqC;EACrC,2CAA2C;EAC3C;;;4CAG0C;EAC1C,mBAAmB;AACrB;;AAEA,kBAAkB;AAClB;EACE,mBAAmB;EACnB,aAAa;AACf;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,2BAA2B;AAC7B;;AAEA;EACE,cAAc;EACd,qBAAqB;EACrB,mBAAmB;EACnB,qBAAqB;EACrB,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,mCAAmC;AACrC;;AAEA;EACE,oCAAoC;EACpC,YAAY;EACZ,0CAA0C;EAC1C,mCAA2B;UAA3B,2BAA2B;EAC3B,kCAAkC;AACpC;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,SAAO;EACP,oCAAoC;EACpC,0CAA0C;EAC1C,qBAAqB;EACrB,qBAAqB;EACrB,YAAY;EACZ,eAAe;EACf,YAAY;EACZ,gBAAgB;EAChB,iBAAiB;EACjB,aAAa;AACf;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA;EACE,oCAAoC;EACpC,mBAAmB;AACrB;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,+BAA+B;AAC/B;EACE,aAAa;EACb,aAAa;EACb,sBAAsB;EACtB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,gBAAgB;EAChB,cAAc;EACd,aAAa;EACb,sBAAsB;EACtB,8BAA8B;EAC9B,mCAA2B;UAA3B,2BAA2B;EAC3B,+CAA+C;EAC/C,gDAAgD;AAClD;;AAEA;EACE,SAAO;EACP,gBAAgB;EAChB,aAAa;EACb,qBAAqB;EACrB,wBAAwB;AAC1B;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;EACb,8CAA8C;EAC9C,8BAA8B;AAChC;;AAEA;EACE,aAAa;EACb,WAAW;EACX,qBAAqB;AACvB;;AAEA,4BAA4B;AAC5B;EACE,eAAe,gBAAgB,EAAE;EACjC,iBAAiB,iBAAiB,EAAE;EACpC,mBAAmB,eAAe,EAAE;EACpC,YAAY,eAAe,EAAE,oBAAoB,EAAE;EACnD,WAAW,mBAAmB,EAAE,iBAAiB,EAAE;EACnD,OAAO,gBAAgB,EAAE;EACzB,QAAQ,qBAAqB,EAAE;EAC/B;IACE,cAAc;IACd,eAAe;IACf,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;EAClB;EACA,uBAAuB,eAAe,EAAE,SAAS,EAAE;AACrD;;AAEA;EACE,eAAe,iBAAiB,EAAE;EAClC,iBAAiB,kBAAkB,EAAE;EACrC,mBAAmB,gBAAgB,EAAE;EACrC,YAAY,mBAAmB,EAAE,iBAAiB,EAAE;EACpD,qBAAqB,mBAAmB,EAAE;EAC1C,qBAAqB,kBAAkB,EAAE;EACzC;IACE,cAAc;IACd,eAAe;IACf,0BAA0B;IAC1B,iBAAiB;IACjB,gBAAgB;EAClB;EACA,eAAe,gBAAgB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE;AACjF;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA;EACE;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;AACF\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  background-attachment: fixed;\\n  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;\\n  animation: liquidMove 20s ease-in-out infinite;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n/* Chat Messages */\\n.message-container {\\n  margin-bottom: 1rem;\\n  display: flex;\\n}\\n\\n.message-container.user {\\n  justify-content: flex-end;\\n}\\n\\n.message-container.assistant {\\n  justify-content: flex-start;\\n}\\n\\n.message {\\n  max-width: 70%;\\n  padding: 0.75rem 1rem;\\n  border-radius: 1rem;\\n  word-wrap: break-word;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n}\\n\\n.user-message {\\n  background: #007AFF;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.assistant-message {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  backdrop-filter: blur(10px);\\n  border-bottom-left-radius: 0.25rem;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1.5rem;\\n  padding: 0.75rem 1rem;\\n  color: white;\\n  font-size: 1rem;\\n  resize: none;\\n  min-height: 44px;\\n  max-height: 120px;\\n  outline: none;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-button {\\n  background: #007AFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.send-button:hover {\\n  background: #0056CC;\\n  transform: scale(1.05);\\n}\\n\\n.send-button:disabled {\\n  background: rgba(255, 255, 255, 0.2);\\n  cursor: not-allowed;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-container {\\n  height: 100vh;\\n  max-width: 800px;\\n  margin: 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  background: rgba(0, 0, 0, 0.1);\\n  backdrop-filter: blur(20px);\\n  border-left: 1px solid rgba(255, 255, 255, 0.1);\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.chat-input-area {\\n  padding: 1rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n}\\n\\n.input-container {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: flex-end;\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 85%;\\n    min-width: 6rem;\\n    padding: 0.75rem 1rem;\\n    font-size: 0.875rem;\\n    line-height: 1.3;\\n  }\\n  .chat-main-container { max-width: 100%; margin: 0; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 90%;\\n    min-width: 5rem;\\n    padding: 0.625rem 0.875rem;\\n    font-size: 0.8rem;\\n    line-height: 1.2;\\n  }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n@keyframes liquidMove {\\n  0%, 100% {\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  25% {\\n    background:\\n      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  50% {\\n    background:\\n      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  75% {\\n    background:\\n      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css\n"));

/***/ })

});