"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { memory, addMessage, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory,\n        isLoading\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        addMessage(userMessage, true);\n        try {\n            // Prepara la cronologia per l'API (incluso il nuovo messaggio utente)\n            const conversationHistory = [\n                ...memory.conversationHistory.map((msg)=>({\n                        role: msg.isUser ? 'user' : 'assistant',\n                        content: msg.content\n                    })),\n                {\n                    role: 'user',\n                    content: userMessage\n                }\n            ];\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_4__.sendChatMessage)(userMessage, conversationHistory, {\n                name: memory.name,\n                intimacyLevel: memory.intimacyLevel,\n                traits: memory.traits,\n                emotions: memory.emotions\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Ricominciare da capo?')) {\n            resetMemory();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Soul Chat\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: 'white',\n                                    fontSize: '1.5rem',\n                                    fontWeight: 'bold',\n                                    margin: 0\n                                },\n                                children: \"Soul Chat\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"chat-messages\",\n                        children: [\n                            memory.conversationHistory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '2rem',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Dimmi, come ti chiami e come ti senti oggi?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message.content,\n                                    isUser: message.isUser\n                                }, message.id, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: '1rem',\n                                    color: 'white'\n                                },\n                                children: \"Soul sta scrivendo...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi il tuo messaggio...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"8+J15u+KW8sagTEnFnMD6Ztr7wU=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});