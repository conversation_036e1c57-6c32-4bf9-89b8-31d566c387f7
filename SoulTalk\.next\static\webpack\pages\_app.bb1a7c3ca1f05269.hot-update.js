"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);\\n  background-attachment: fixed;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 0.75rem;\\n  border-radius: 0.75rem;\\n  max-width: 16rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  text-align: left;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.8);\\n  margin-left: auto;\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.1);\\n  margin-right: auto;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem 0.75rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 40px;\\n  max-height: 80px;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n.input-field::-moz-placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.5rem;\\n  padding: 0.5rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar nascosta ma funzionale */\\n.chat-container {\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-container::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 0.75rem;\\n  min-height: 0; /* Importante per il flex */\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Responsive Design */\\n.h-screen { height: 100vh; }\\n.h-full { height: 100%; }\\n.min-h-0 { min-height: 0; }\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n/* Responsive */\\n@media (max-width: 768px) {\\n  .max-w-4xl { max-width: 100%; }\\n  .p-4 { padding: 0.5rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ,0EAA0E;EAC1E,4BAA4B;AAC9B;;AAEA,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,sBAAsB,EAAE;AAChC,qBAAqB,kBAAkB,EAAE;AACzC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,kBAAkB,EAAE,mBAAmB,EAAE;AACrD,WAAW,eAAe,EAAE,oBAAoB,EAAE;AAClD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,WAAW,mBAAmB,EAAE,qBAAqB,EAAE;AACvD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,YAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;;AAEzB,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,iDAAiD;EACjD,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;EAChB,sBAAsB;EACtB,gBAAgB;EAChB,6CAA6C;EAC7C,0CAA0C;EAC1C,qBAAqB;EACrB,yBAAyB;EACzB,mBAAmB;EACnB,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,iBAAiB;AACnB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,sBAAsB;EACtB,uBAAuB;EACvB,YAAY;EACZ,yBAAyB;EACzB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,oBAAoB;AACtB;;AAEA;EACE,+BAA+B;AACjC;;AAFA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,qBAAqB;EACrB,eAAe;EACf,YAAY;EACZ,eAAe;EACf,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,qCAAqC;AACrC;EACE,qBAAqB,EAAE,YAAY;EACnC,wBAAwB,EAAE,0BAA0B;EACpD,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,aAAa,EAAE,0BAA0B;AAC3C;;AAEA,+BAA+B;AAC/B;EACE,aAAa;EACb,aAAa;EACb,sBAAsB;EACtB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,OAAO;EACP,gBAAgB;EAChB,kBAAkB;EAClB,aAAa,EAAE,2BAA2B;EAC1C,qBAAqB,EAAE,YAAY;EACnC,wBAAwB,EAAE,0BAA0B;EACpD,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,aAAa,EAAE,0BAA0B;AAC3C;;AAEA;EACE,cAAc;EACd,gBAAgB;EAChB,8BAA8B;EAC9B,mCAA2B;UAA3B,2BAA2B;EAC3B,8CAA8C;AAChD;;AAEA,sBAAsB;AACtB,YAAY,aAAa,EAAE;AAC3B,UAAU,YAAY,EAAE;AACxB,WAAW,aAAa,EAAE;;AAE1B,4BAA4B;AAC5B;EACE,eAAe,gBAAgB,EAAE;EACjC,iBAAiB,iBAAiB,EAAE;EACpC,mBAAmB,eAAe,EAAE;EACpC,YAAY,eAAe,EAAE,oBAAoB,EAAE;EACnD,WAAW,mBAAmB,EAAE,iBAAiB,EAAE;EACnD,OAAO,gBAAgB,EAAE;EACzB,QAAQ,qBAAqB,EAAE;EAC/B,eAAe,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE;AACtE;;AAEA;EACE,eAAe,iBAAiB,EAAE;EAClC,iBAAiB,kBAAkB,EAAE;EACrC,mBAAmB,gBAAgB,EAAE;EACrC,YAAY,mBAAmB,EAAE,iBAAiB,EAAE;EACpD,qBAAqB,mBAAmB,EAAE;EAC1C,qBAAqB,kBAAkB,EAAE;EACzC,eAAe,cAAc,EAAE,iBAAiB,EAAE,mBAAmB,EAAE;EACvE,eAAe,gBAAgB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE;AACjF;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,6BAA6B;AAC7B;EACE,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA,eAAe;AACf;EACE,aAAa,eAAe,EAAE;EAC9B,OAAO,eAAe,EAAE;AAC1B;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA,6BAA6B;AAC7B;EACE,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);\\n  background-attachment: fixed;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 0.75rem;\\n  border-radius: 0.75rem;\\n  max-width: 16rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  text-align: left;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.8);\\n  margin-left: auto;\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.1);\\n  margin-right: auto;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem 0.75rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 40px;\\n  max-height: 80px;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.5rem;\\n  padding: 0.5rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar nascosta ma funzionale */\\n.chat-container {\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-container::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 0.75rem;\\n  min-height: 0; /* Importante per il flex */\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Responsive Design */\\n.h-screen { height: 100vh; }\\n.h-full { height: 100%; }\\n.min-h-0 { min-height: 0; }\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n/* Responsive */\\n@media (max-width: 768px) {\\n  .max-w-4xl { max-width: 100%; }\\n  .p-4 { padding: 0.5rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ })

});