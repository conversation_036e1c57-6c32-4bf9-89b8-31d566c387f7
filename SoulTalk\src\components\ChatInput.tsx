import React, { useState, useRef, useEffect } from 'react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false,
  placeholder = "Scrivi il tuo messaggio...",
  className = '' 
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize della textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    setIsTyping(e.target.value.length > 0);
  };

  return (
    <div className={`relative w-full ${className}`}>
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* Campo di input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={`input-field ${disabled ? 'opacity-50' : ''}`}
            style={{
              paddingRight: '3.5rem',
              fontSize: typeof window !== 'undefined' && window.innerWidth < 640 ? '16px' : '14px'
            }}
          />
          
          {/* Indicatore di digitazione */}
          {isTyping && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" />
                <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" 
                     style={{ animationDelay: '0.2s' }} />
                <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse" 
                     style={{ animationDelay: '0.4s' }} />
              </div>
            </div>
          )}
        </div>
        
        {/* Pulsante di invio */}
        <button
          type="submit"
          disabled={!message.trim() || disabled}
          className={`btn ${!message.trim() || disabled ? 'opacity-50' : ''}`}
        >
          <svg 
            className="w-5 h-5 text-white" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" 
            />
          </svg>
        </button>
      </form>
      
      {/* Suggerimento per Shift+Enter - nascosto su mobile */}
      <div className="absolute -bottom-6 left-0 text-xs opacity-40 hidden sm:block">
        Premi Invio per inviare, Shift+Invio per andare a capo
      </div>
    </div>
  );
};

export default ChatInput;
