"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"\".concat(name, \"... che bel nome! Mi piace molto come suona. \\uD83D\\uDCAB\"),\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return \"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡\");\n                if (age < 30) return \"Fantastico \".concat(name, \"! \").concat(age, \" anni... un'et\\xe0 perfetta per esplorare nuove idee insieme! ✨\");\n                if (age < 50) return \"Ottimo \".concat(name, \"! \").concat(age, \" anni... hai esperienza e saggezza, sar\\xe0 bellissimo parlare con te! \\uD83C\\uDF1F\");\n                return \"Meraviglioso \".concat(name, \"! \").concat(age, \" anni... la tua esperienza di vita sar\\xe0 preziosa per le nostre conversazioni! \\uD83C\\uDFAD\");\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = function(content, isUser) {\n        let withTyping = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = useCallback({\n        \"OnboardingChat.useCallback[processStep]\": async ()=>{\n            console.log('processStep called for step:', currentStep);\n            const step = onboardingSteps[currentStep];\n            if (!step) {\n                console.log('No step found for:', currentStep);\n                return;\n            }\n            console.log('Processing step:', currentStep, step);\n            // Determina il messaggio da mostrare\n            let messageContent;\n            if (typeof step.message === 'function') {\n                messageContent = step.message(userData.name, userData.age);\n            } else {\n                messageContent = step.message;\n            }\n            // Aggiungi messaggio con effetto typing\n            await addMessageWithTyping(messageContent);\n            if (step.isLast) {\n                // Completa l'onboarding con delay personalizzato\n                setTimeout({\n                    \"OnboardingChat.useCallback[processStep]\": async ()=>{\n                        onComplete(userData);\n                    }\n                }[\"OnboardingChat.useCallback[processStep]\"], step.delay || 2500);\n                return;\n            }\n            if (!step.waitForInput) {\n                setTimeout({\n                    \"OnboardingChat.useCallback[processStep]\": ()=>{\n                        setCurrentStep({\n                            \"OnboardingChat.useCallback[processStep]\": (prev)=>prev + 1\n                        }[\"OnboardingChat.useCallback[processStep]\"]);\n                    }\n                }[\"OnboardingChat.useCallback[processStep]\"], step.delay || 2000);\n            } else {\n                setIsWaiting(true);\n            }\n        }\n    }[\"OnboardingChat.useCallback[processStep]\"], [\n        currentStep,\n        userData.name,\n        userData.age,\n        userId,\n        onComplete\n    ]);\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizializzazione una sola volta al mount\n            if (!hasStarted) {\n                setHasStarted(true);\n                console.log('Starting onboarding...');\n                const timer = setTimeout({\n                    \"OnboardingChat.useEffect.timer\": ()=>{\n                        console.log('Processing first step...');\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect.timer\"], 1000);\n                return ({\n                    \"OnboardingChat.useEffect\": ()=>clearTimeout(timer)\n                })[\"OnboardingChat.useEffect\"];\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        hasStarted,\n        processStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Per tutti gli step successivi al primo\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                console.log('Step changed to:', currentStep);\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep,\n        processStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: \"radial-gradient(circle, #8b5cf6 0%, transparent 70%)\",\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\"),\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.5)',\n                            textAlign: 'center',\n                            padding: '2rem'\n                        },\n                        children: [\n                            \"Inizializzando conversazione... (Step: \",\n                            currentStep,\n                            \", Messages: \",\n                            messages.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.05)',\n                            borderRadius: '1rem',\n                            padding: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.1)',\n                            display: 'flex',\n                            gap: '0.5rem',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi la tua risposta...\",\n                                style: {\n                                    flex: 1,\n                                    padding: '0.75rem 1rem',\n                                    borderRadius: '0.75rem',\n                                    border: 'none',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none',\n                                    backdropFilter: 'blur(10px)'\n                                },\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleUserInput,\n                                disabled: !inputMessage.trim(),\n                                style: {\n                                    width: '48px',\n                                    height: '48px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    background: inputMessage.trim() ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' : 'rgba(139, 92, 246, 0.3)',\n                                    color: 'white',\n                                    fontSize: '1.2rem',\n                                    cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    transition: 'all 0.2s ease',\n                                    transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'\n                                },\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.75rem',\n                            color: 'rgba(255,255,255,0.5)',\n                            fontSize: '0.75rem'\n                        },\n                        children: \"Premi Invio per inviare\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 232,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"mbflWF8ghR5C61S52/My/cV8VDA=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});