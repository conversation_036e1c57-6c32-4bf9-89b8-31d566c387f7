"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-browser)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-browser)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se non è autenticato, mostra sempre il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Caricamento...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.9)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Caricando i tuoi dati...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            messages.length,\n                                            \" msg\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, this),\n                                    isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"ID: \",\n                                            userId.slice(-6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"1Ufb3RIuGn3DPiXmxRwWzC8X8Z4=\", false, function() {\n    return [\n        _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_9__.useSupabaseMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});