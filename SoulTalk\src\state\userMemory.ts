// Interfaccia per la memoria dell'utente
export interface UserMemory {
  name: string;
  age: number;
  pronouns: string;
  traits: string[];
  emotions: string[];
  keyMoments: string[];
  intimacyLevel: number; // da 0 a 5
  conversationHistory: ConversationMessage[];
  lastInteraction: Date;
  personalityInsights: PersonalityInsight[];
}

export interface ConversationMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  emotionalTone?: string;
  topics?: string[];
}

export interface PersonalityInsight {
  category: string; // es: "fears", "desires", "relationships", "past", "future"
  insight: string;
  confidence: number; // da 0 a 1
  timestamp: Date;
}

// Stato iniziale della memoria utente
const initialUserMemory: UserMemory = {
  name: '',
  age: 0,
  pronouns: '',
  traits: [],
  emotions: [],
  keyMoments: [],
  intimacyLevel: 0,
  conversationHistory: [],
  lastInteraction: new Date(),
  personalityInsights: []
};

// Chiave per localStorage
const STORAGE_KEY = 'soultalk_user_memory';

// Funzioni per gestire la memoria utente
export class UserMemoryManager {
  
  // Carica la memoria dal localStorage
  static loadMemory(): UserMemory {
    // Controlla se siamo nel browser
    if (typeof window === 'undefined') {
      return { ...initialUserMemory };
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Converte le date da string a Date objects
        parsed.lastInteraction = new Date(parsed.lastInteraction);
        parsed.conversationHistory = parsed.conversationHistory.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        parsed.personalityInsights = parsed.personalityInsights.map((insight: any) => ({
          ...insight,
          timestamp: new Date(insight.timestamp)
        }));
        return parsed;
      }
    } catch (error) {
      console.error('Errore nel caricamento della memoria utente:', error);
    }
    return { ...initialUserMemory };
  }

  // Salva la memoria nel localStorage
  static saveMemory(memory: UserMemory): void {
    // Controlla se siamo nel browser
    if (typeof window === 'undefined') {
      return;
    }

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(memory));
    } catch (error) {
      console.error('Errore nel salvataggio della memoria utente:', error);
    }
  }

  // Aggiunge un messaggio alla cronologia
  static addMessage(memory: UserMemory, content: string, isUser: boolean, emotionalTone?: string, topics?: string[]): UserMemory {
    const newMessage: ConversationMessage = {
      id: Date.now().toString(),
      content,
      isUser,
      timestamp: new Date(),
      emotionalTone,
      topics
    };

    const updatedMemory = {
      ...memory,
      conversationHistory: [...memory.conversationHistory, newMessage],
      lastInteraction: new Date()
    };

    this.saveMemory(updatedMemory);
    return updatedMemory;
  }

  // Aggiorna le informazioni base dell'utente
  static updateBasicInfo(memory: UserMemory, updates: Partial<Pick<UserMemory, 'name' | 'age' | 'pronouns'>>): UserMemory {
    const updatedMemory = {
      ...memory,
      ...updates,
      lastInteraction: new Date()
    };

    this.saveMemory(updatedMemory);
    return updatedMemory;
  }

  // Aggiunge un tratto della personalità
  static addTrait(memory: UserMemory, trait: string): UserMemory {
    if (!memory.traits.includes(trait)) {
      const updatedMemory = {
        ...memory,
        traits: [...memory.traits, trait],
        lastInteraction: new Date()
      };

      this.saveMemory(updatedMemory);
      return updatedMemory;
    }
    return memory;
  }

  // Aggiunge un'emozione
  static addEmotion(memory: UserMemory, emotion: string): UserMemory {
    if (!memory.emotions.includes(emotion)) {
      const updatedMemory = {
        ...memory,
        emotions: [...memory.emotions, emotion],
        lastInteraction: new Date()
      };

      this.saveMemory(updatedMemory);
      return updatedMemory;
    }
    return memory;
  }

  // Aggiunge un momento chiave
  static addKeyMoment(memory: UserMemory, moment: string): UserMemory {
    const updatedMemory = {
      ...memory,
      keyMoments: [...memory.keyMoments, moment],
      lastInteraction: new Date()
    };

    this.saveMemory(updatedMemory);
    return updatedMemory;
  }

  // Aggiorna il livello di intimità
  static updateIntimacyLevel(memory: UserMemory, level: number): UserMemory {
    const clampedLevel = Math.max(0, Math.min(5, level));
    const updatedMemory = {
      ...memory,
      intimacyLevel: clampedLevel,
      lastInteraction: new Date()
    };

    this.saveMemory(updatedMemory);
    return updatedMemory;
  }

  // Aggiunge un insight sulla personalità
  static addPersonalityInsight(memory: UserMemory, category: string, insight: string, confidence: number): UserMemory {
    const newInsight: PersonalityInsight = {
      category,
      insight,
      confidence: Math.max(0, Math.min(1, confidence)),
      timestamp: new Date()
    };

    const updatedMemory = {
      ...memory,
      personalityInsights: [...memory.personalityInsights, newInsight],
      lastInteraction: new Date()
    };

    this.saveMemory(updatedMemory);
    return updatedMemory;
  }

  // Ottiene il colore dell'aura basato sul livello di intimità
  static getAuraColor(intimacyLevel: number): string {
    const colorMap = {
      0: 'gray-blue',      // Inizio / conoscenza
      1: 'gray-blue',      // Inizio / conoscenza
      2: 'teal',           // Connessione emotiva in crescita
      3: 'yellow',         // Scambio personale attivo
      4: 'red',            // Discussioni o argomenti delicati
      5: 'purple-pink'     // Affinità / profonda connessione
    };
    return colorMap[intimacyLevel as keyof typeof colorMap] || 'gray-blue';
  }

  // Resetta la memoria utente
  static resetMemory(): UserMemory {
    const freshMemory = { ...initialUserMemory };
    this.saveMemory(freshMemory);
    return freshMemory;
  }

  // Esporta la memoria per backup
  static exportMemory(memory: UserMemory): string {
    return JSON.stringify(memory, null, 2);
  }

  // Importa la memoria da backup
  static importMemory(jsonData: string): UserMemory | null {
    try {
      const imported = JSON.parse(jsonData);
      // Validazione base della struttura
      if (imported && typeof imported.name === 'string' && typeof imported.intimacyLevel === 'number') {
        this.saveMemory(imported);
        return imported;
      }
    } catch (error) {
      console.error('Errore nell\'importazione della memoria:', error);
    }
    return null;
  }
}

// Hook React per gestire la memoria utente
import { useState, useEffect } from 'react';

export function useUserMemory() {
  const [memory, setMemory] = useState<UserMemory>(() => ({ ...initialUserMemory }));
  const [isLoaded, setIsLoaded] = useState(false);

  // Carica la memoria dal localStorage dopo l'idratazione
  useEffect(() => {
    const loadedMemory = UserMemoryManager.loadMemory();
    setMemory(loadedMemory);
    setIsLoaded(true);
  }, []);

  // Funzioni helper che aggiornano lo stato locale
  const addMessage = (content: string, isUser: boolean, emotionalTone?: string, topics?: string[]) => {
    const updatedMemory = UserMemoryManager.addMessage(memory, content, isUser, emotionalTone, topics);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const updateBasicInfo = (updates: Partial<Pick<UserMemory, 'name' | 'age' | 'pronouns'>>) => {
    const updatedMemory = UserMemoryManager.updateBasicInfo(memory, updates);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const addTrait = (trait: string) => {
    const updatedMemory = UserMemoryManager.addTrait(memory, trait);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const addEmotion = (emotion: string) => {
    const updatedMemory = UserMemoryManager.addEmotion(memory, emotion);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const addKeyMoment = (moment: string) => {
    const updatedMemory = UserMemoryManager.addKeyMoment(memory, moment);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const updateIntimacyLevel = (level: number) => {
    const updatedMemory = UserMemoryManager.updateIntimacyLevel(memory, level);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const addPersonalityInsight = (category: string, insight: string, confidence: number) => {
    const updatedMemory = UserMemoryManager.addPersonalityInsight(memory, category, insight, confidence);
    setMemory(updatedMemory);
    return updatedMemory;
  };

  const resetMemory = () => {
    const freshMemory = UserMemoryManager.resetMemory();
    setMemory(freshMemory);
    return freshMemory;
  };

  const getAuraColor = () => UserMemoryManager.getAuraColor(memory.intimacyLevel);

  return {
    memory,
    isLoaded,
    addMessage,
    updateBasicInfo,
    addTrait,
    addEmotion,
    addKeyMoment,
    updateIntimacyLevel,
    addPersonalityInsight,
    resetMemory,
    getAuraColor,
    exportMemory: () => UserMemoryManager.exportMemory(memory),
    importMemory: (jsonData: string) => {
      const imported = UserMemoryManager.importMemory(jsonData);
      if (imported) {
        setMemory(imported);
        return imported;
      }
      return null;
    }
  };
}
