"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOnboardingStatus: () => (/* binding */ checkOnboardingStatus),\n/* harmony export */   completeOnboarding: () => (/* binding */ completeOnboarding),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   resetOnboardingStatus: () => (/* binding */ resetOnboardingStatus),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(pages-dir-browser)/./node_modules/bcryptjs/index.js\");\n\n\n// Funzione per hash della password\nconst hashPassword = async (password)=>{\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n};\n// Funzione per verificare la password\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n};\n// Registrazione nuovo utente\nconst registerUser = async (username, password)=>{\n    try {\n        // Controlla se l'username esiste già\n        const { data: existingUser, error: checkError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('username').eq('username', username.toLowerCase()).single();\n        if (checkError && checkError.code !== 'PGRST116') {\n            console.error('Error checking existing user:', checkError);\n            return {\n                success: false,\n                error: 'Errore durante il controllo username'\n            };\n        }\n        if (existingUser) {\n            return {\n                success: false,\n                error: 'Username già esistente'\n            };\n        }\n        // Crea hash della password\n        const passwordHash = await hashPassword(password);\n        // Genera ID utente\n        const userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        // Inserisci nella tabella auth\n        const { error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            password_hash: passwordHash\n        });\n        if (authError) {\n            console.error('Error creating auth:', authError);\n            return {\n                success: false,\n                error: 'Errore durante la registrazione'\n            };\n        }\n        // Crea profilo utente\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            name: '',\n            is_onboarded: false\n        });\n        if (profileError) {\n            console.error('Error creating profile:', profileError);\n            // Rollback: elimina l'auth se il profilo fallisce\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').delete().eq('id', userId);\n            return {\n                success: false,\n                error: 'Errore durante la creazione del profilo'\n            };\n        }\n        return {\n            success: true,\n            userId\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Login utente\nconst loginUser = async (username, password)=>{\n    try {\n        // Trova l'utente\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('*').eq('username', username.toLowerCase()).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Verifica password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Aggiorna last_login\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').update({\n            last_login: new Date().toISOString()\n        }).eq('id', user.id);\n        return {\n            success: true,\n            userId: user.id\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Verifica se l'utente è autenticato (controlla localStorage)\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const authData = localStorage.getItem('soul_auth');\n    if (!authData) return null;\n    try {\n        const { userId, timestamp } = JSON.parse(authData);\n        // Controlla se la sessione è scaduta (24 ore)\n        const now = Date.now();\n        const sessionAge = now - timestamp;\n        const maxAge = 24 * 60 * 60 * 1000 // 24 ore\n        ;\n        if (sessionAge > maxAge) {\n            localStorage.removeItem('soul_auth');\n            return null;\n        }\n        return userId;\n    } catch (e) {\n        localStorage.removeItem('soul_auth');\n        return null;\n    }\n};\n// Salva sessione utente\nconst saveUserSession = (userId)=>{\n    if (false) {}\n    const authData = {\n        userId,\n        timestamp: Date.now()\n    };\n    localStorage.setItem('soul_auth', JSON.stringify(authData));\n};\n// Logout utente\nconst logoutUser = ()=>{\n    if (false) {}\n    localStorage.removeItem('soul_auth');\n    localStorage.removeItem('soul_user_id'); // Rimuovi anche il vecchio ID\n};\n// Controlla se l'utente ha completato l'onboarding\nconst checkOnboardingStatus = async (userId)=>{\n    try {\n        console.log('Checking onboarding status for user:', userId);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('is_onboarded').eq('id', userId).single();\n        console.log('Onboarding check result:', {\n            data,\n            error\n        });\n        if (error || !data) {\n            console.log('User needs onboarding - no data or error');\n            return false;\n        }\n        const isOnboarded = data.is_onboarded || false;\n        console.log('User onboarding status:', isOnboarded);\n        return isOnboarded;\n    } catch (error) {\n        console.error('Exception in checkOnboardingStatus:', error);\n        return false;\n    }\n};\n// Completa l'onboarding\nconst completeOnboarding = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: true\n        }).eq('id', userId);\n        return !error;\n    } catch (e) {\n        return false;\n    }\n};\n// Funzione di debug per resettare l'onboarding (solo per test)\nconst resetOnboardingStatus = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: false,\n            name: '',\n            age: null\n        }).eq('id', userId);\n        return !error;\n    } catch (e) {\n        return false;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/lib/auth.ts\n"));

/***/ })

});