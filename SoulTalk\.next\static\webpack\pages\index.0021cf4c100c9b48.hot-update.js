"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChatSession: () => (/* binding */ createChatSession),\n/* harmony export */   createOrUpdateUserProfile: () => (/* binding */ createOrUpdateUserProfile),\n/* harmony export */   deleteUserData: () => (/* binding */ deleteUserData),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserSessions: () => (/* binding */ getUserSessions),\n/* harmony export */   saveChatMessage: () => (/* binding */ saveChatMessage)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-browser)/./src/lib/supabase.ts\");\n\n// Genera un ID utente unico basato su browser fingerprint\nconst generateUserId = ()=>{\n    // Controlla se siamo nel browser (non SSR)\n    if (false) {}\n    const stored = localStorage.getItem('soul_user_id');\n    if (stored) return stored;\n    const userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    localStorage.setItem('soul_user_id', userId);\n    return userId;\n};\n// Funzioni per UserProfile\nconst getUserProfile = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('*').eq('id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in getUserProfile:', error);\n        return null;\n    }\n};\nconst createOrUpdateUserProfile = async (profile)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').upsert({\n            ...profile,\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createOrUpdateUserProfile:', error);\n        return null;\n    }\n};\n// Funzioni per ChatMessage\nconst getChatMessages = async function(userId) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').select('*').eq('user_id', userId).order('timestamp', {\n            ascending: true\n        }).limit(limit);\n        if (error) {\n            console.error('Error fetching chat messages:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getChatMessages:', error);\n        return [];\n    }\n};\nconst saveChatMessage = async (message)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').insert({\n            ...message,\n            created_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error saving chat message:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in saveChatMessage:', error);\n        return null;\n    }\n};\n// Funzioni per ChatSession\nconst createChatSession = async (userId, sessionName)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').insert({\n            id: \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            user_id: userId,\n            session_name: sessionName || \"Chat \".concat(new Date().toLocaleDateString()),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        }).select().single();\n        if (error) {\n            console.error('Error creating chat session:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createChatSession:', error);\n        return null;\n    }\n};\nconst getUserSessions = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').select('*').eq('user_id', userId).order('updated_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching user sessions:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getUserSessions:', error);\n        return [];\n    }\n};\n// Funzione per eliminare tutti i dati utente (reset completo)\nconst deleteUserData = async (userId)=>{\n    try {\n        // Elimina messaggi\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_messages').delete().eq('user_id', userId);\n        // Elimina sessioni\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('chat_sessions').delete().eq('user_id', userId);\n        // Elimina profilo\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').delete().eq('id', userId);\n        // Rimuovi anche dal localStorage\n        localStorage.removeItem('soul_user_id');\n        return true;\n    } catch (error) {\n        console.error('Error deleting user data:', error);\n        return false;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/lib/database.ts\n"));

/***/ })

});