"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-browser)/./src/lib/database.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [needsOnboarding, setNeedsOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Inizializza solo lato client\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            setIsClient(true);\n            // Non controlliamo sessioni esistenti - sempre login prima\n            setIsLoading(false);\n        }\n    }[\"useSupabaseMemory.useEffect\"], []);\n    // Funzione per autenticare l'utente\n    const authenticateUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[authenticateUser]\": (newUserId)=>{\n            setUserId(newUserId);\n            setIsAuthenticated(true);\n            setIsLoading(true); // Inizia il caricamento dati\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveUserSession)(newUserId);\n        }\n    }[\"useSupabaseMemory.useCallback[authenticateUser]\"], []);\n    // Funzione per logout\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logoutUser)();\n            setUserId('');\n            setIsAuthenticated(false);\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setNeedsOnboarding(false);\n        }\n    }[\"useSupabaseMemory.useCallback[logout]\"], []);\n    // Carica i dati iniziali solo quando siamo lato client, autenticati e abbiamo un userId\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || !isAuthenticated) return;\n            const loadData = {\n                \"useSupabaseMemory.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Controlla se l'utente ha completato l'onboarding\n                        const isOnboarded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.checkOnboardingStatus)(userId);\n                        setNeedsOnboarding(!isOnboarded);\n                        // Carica profilo utente\n                        const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(userId);\n                        if (profile) {\n                            setUserMemory({\n                                name: profile.name || '',\n                                age: profile.age || 0,\n                                traits: profile.traits || [],\n                                emotions: profile.emotions || [],\n                                intimacyLevel: profile.intimacy_level || 0,\n                                keyMoments: profile.key_moments || []\n                            });\n                        }\n                        // Carica messaggi solo se l'onboarding è completato\n                        if (isOnboarded) {\n                            const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(userId);\n                            const formattedMessages = chatMessages.map({\n                                \"useSupabaseMemory.useEffect.loadData.formattedMessages\": (msg)=>({\n                                        id: msg.id,\n                                        content: msg.content,\n                                        isUser: msg.is_user,\n                                        timestamp: new Date(msg.timestamp)\n                                    })\n                            }[\"useSupabaseMemory.useEffect.loadData.formattedMessages\"]);\n                            setMessages(formattedMessages);\n                        }\n                    } catch (error) {\n                        console.error('Error loading data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseMemory.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        isAuthenticated\n    ]);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || isLoading || !userMemory.name) return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: \"msg_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se siamo lato client e abbiamo userId\n            if (isClient && userId) {\n                setIsSaving(true);\n                try {\n                    const chatMessage = {\n                        id: newMessage.id,\n                        user_id: userId,\n                        content: newMessage.content,\n                        is_user: newMessage.isUser,\n                        timestamp: newMessage.timestamp.toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.saveChatMessage)(chatMessage);\n                } catch (error) {\n                    console.error('Error saving message:', error);\n                } finally{\n                    setIsSaving(false);\n                }\n            }\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        isClient,\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!isClient || !userId) return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                logout();\n                if (true) {\n                    window.location.reload();\n                }\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        isClient,\n        userId,\n        logout\n    ]);\n    // Completa l'onboarding con i dati dell'utente\n    const completeOnboardingWithData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": async (userData)=>{\n            if (!userId) return;\n            try {\n                // Aggiorna il profilo utente con i dati dell'onboarding\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)({\n                    id: userId,\n                    name: userData.name,\n                    age: userData.age,\n                    is_onboarded: true\n                });\n                // Aggiorna lo stato locale\n                setUserMemory({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": (prev)=>({\n                            ...prev,\n                            name: userData.name,\n                            age: userData.age\n                        })\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"]);\n                // Marca l'onboarding come completato\n                setNeedsOnboarding(false);\n                // Forza un refresh per caricare la chat normale\n                setTimeout({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": ()=>{\n                        window.location.reload();\n                    }\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], 1000);\n            } catch (error) {\n                console.error('Error completing onboarding:', error);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], [\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        isAuthenticated,\n        needsOnboarding,\n        addMessage,\n        updateUserMemory,\n        resetMemory,\n        authenticateUser,\n        logout,\n        completeOnboardingWithData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\n"));

/***/ })

});