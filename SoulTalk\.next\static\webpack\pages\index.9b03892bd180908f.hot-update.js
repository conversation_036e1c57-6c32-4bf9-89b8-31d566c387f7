"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-browser)/./src/lib/database.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [needsOnboarding, setNeedsOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Inizializza solo lato client\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            setIsClient(true);\n            // Controlla se l'utente è già autenticato\n            if (true) {\n                const currentUserId = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n                if (currentUserId) {\n                    setUserId(currentUserId);\n                    setIsAuthenticated(true);\n                }\n            }\n        }\n    }[\"useSupabaseMemory.useEffect\"], []);\n    // Funzione per autenticare l'utente\n    const authenticateUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[authenticateUser]\": (newUserId)=>{\n            setUserId(newUserId);\n            setIsAuthenticated(true);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveUserSession)(newUserId);\n        }\n    }[\"useSupabaseMemory.useCallback[authenticateUser]\"], []);\n    // Funzione per logout\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logoutUser)();\n            setUserId('');\n            setIsAuthenticated(false);\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setNeedsOnboarding(false);\n        }\n    }[\"useSupabaseMemory.useCallback[logout]\"], []);\n    // Carica i dati iniziali solo quando siamo lato client, autenticati e abbiamo un userId\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || !isAuthenticated) return;\n            const loadData = {\n                \"useSupabaseMemory.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Controlla se l'utente ha completato l'onboarding\n                        const isOnboarded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.checkOnboardingStatus)(userId);\n                        setNeedsOnboarding(!isOnboarded);\n                        // Carica profilo utente\n                        const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(userId);\n                        if (profile) {\n                            setUserMemory({\n                                name: profile.name || '',\n                                age: profile.age || 0,\n                                traits: profile.traits || [],\n                                emotions: profile.emotions || [],\n                                intimacyLevel: profile.intimacy_level || 0,\n                                keyMoments: profile.key_moments || []\n                            });\n                        }\n                        // Carica messaggi solo se l'onboarding è completato\n                        if (isOnboarded) {\n                            const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(userId);\n                            const formattedMessages = chatMessages.map({\n                                \"useSupabaseMemory.useEffect.loadData.formattedMessages\": (msg)=>({\n                                        id: msg.id,\n                                        content: msg.content,\n                                        isUser: msg.is_user,\n                                        timestamp: new Date(msg.timestamp)\n                                    })\n                            }[\"useSupabaseMemory.useEffect.loadData.formattedMessages\"]);\n                            setMessages(formattedMessages);\n                        }\n                    } catch (error) {\n                        console.error('Error loading data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseMemory.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        isAuthenticated\n    ]);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || isLoading || !userMemory.name) return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: \"msg_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se siamo lato client e abbiamo userId\n            if (isClient && userId) {\n                setIsSaving(true);\n                try {\n                    const chatMessage = {\n                        id: newMessage.id,\n                        user_id: userId,\n                        content: newMessage.content,\n                        is_user: newMessage.isUser,\n                        timestamp: newMessage.timestamp.toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.saveChatMessage)(chatMessage);\n                } catch (error) {\n                    console.error('Error saving message:', error);\n                } finally{\n                    setIsSaving(false);\n                }\n            }\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        isClient,\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!isClient || !userId) return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                // Reset local state\n                setMessages([]);\n                setUserMemory({\n                    name: '',\n                    age: 0,\n                    traits: [],\n                    emotions: [],\n                    intimacyLevel: 0,\n                    keyMoments: []\n                });\n                // Genera nuovo user ID\n                if (true) {\n                    localStorage.removeItem('soul_user_id');\n                    window.location.reload();\n                }\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        isClient,\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        addMessage,\n        updateUserMemory,\n        resetMemory\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\n"));

/***/ })

});