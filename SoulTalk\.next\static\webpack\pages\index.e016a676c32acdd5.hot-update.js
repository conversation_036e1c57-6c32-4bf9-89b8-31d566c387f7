"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"\".concat(name, \"... che bel nome! Mi piace molto come suona. \\uD83D\\uDCAB\"),\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return \"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡\");\n                if (age < 30) return \"Fantastico \".concat(name, \"! \").concat(age, \" anni... un'et\\xe0 perfetta per esplorare nuove idee insieme! ✨\");\n                if (age < 50) return \"Ottimo \".concat(name, \"! \").concat(age, \" anni... hai esperienza e saggezza, sar\\xe0 bellissimo parlare con te! \\uD83C\\uDF1F\");\n                return \"Meraviglioso \".concat(name, \"! \").concat(age, \" anni... la tua esperienza di vita sar\\xe0 preziosa per le nostre conversazioni! \\uD83C\\uDFAD\");\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = function(content, isUser) {\n        let withTyping = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        // Determina il messaggio da mostrare\n        let messageContent;\n        if (typeof step.message === 'function') {\n            messageContent = step.message(userData.name, userData.age);\n        } else {\n            messageContent = step.message;\n        }\n        // Aggiungi messaggio con effetto typing\n        await addMessageWithTyping(messageContent);\n        if (step.isLast) {\n            // Completa l'onboarding con delay personalizzato\n            setTimeout(async ()=>{\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.completeOnboarding)(userId);\n                onComplete(userData);\n            }, step.delay || 2500);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, step.delay || 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizia l'onboarding solo al primo step\n            if (currentStep === 0) {\n                setTimeout({\n                    \"OnboardingChat.useEffect\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect\"], 1000);\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: \"radial-gradient(circle, #8b5cf6 0%, transparent 70%)\",\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\"),\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.05)',\n                            borderRadius: '1rem',\n                            padding: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.1)',\n                            display: 'flex',\n                            gap: '0.5rem',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi la tua risposta...\",\n                                style: {\n                                    flex: 1,\n                                    padding: '0.75rem 1rem',\n                                    borderRadius: '0.75rem',\n                                    border: 'none',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none',\n                                    backdropFilter: 'blur(10px)'\n                                },\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleUserInput,\n                                disabled: !inputMessage.trim(),\n                                style: {\n                                    width: '48px',\n                                    height: '48px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    background: inputMessage.trim() ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' : 'rgba(139, 92, 246, 0.3)',\n                                    color: 'white',\n                                    fontSize: '1.2rem',\n                                    cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    transition: 'all 0.2s ease',\n                                    transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'\n                                },\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.75rem',\n                            color: 'rgba(255,255,255,0.5)',\n                            fontSize: '0.75rem'\n                        },\n                        children: \"Premi Invio per inviare\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"ESoKxAMN2ZwgLwKjgQ4pqXB0nn8=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});