# SoulTalk

Una piattaforma AI che si comporta come un'amicizia da costruire, con lo scopo di conoscere davvero l'utente nel tempo, e non semplicemente assecondarlo.

## 🎯 Obiettivo del Progetto

SoulTalk crea una relazione progressiva con l'utente attraverso:

- **Personalità autentica**: L'AI si comporta come una persona reale di nome "Soul"
- **Memoria persistente**: Ricorda e struttura un profilo psicologico dell'utente
- **Connessione emotiva**: Reagisce in modo emotivo ed empatico
- **Aura visiva**: Usa elementi visivi per comunicare il livello di connessione raggiunto
- **Crescita progressiva**: Il rapporto si evolve attraverso 6 livelli di intimità

## 🧱 Tecnologie Utilizzate

- **Frontend**: Next.js 15 + React + TypeScript
- **Styling**: Tailwind CSS con effetti glass e animazioni
- **AI**: OpenRouter API con modello Moonshot AI Kimi-K2
- **Memoria**: localStorage per persistenza client-side
- **Stato**: Hook React personalizzati

## 🌈 Sistema Aura Emotiva

L'aura cambia colore in base al livello di intimità:

| Livello | Colore | Stato Relazione |
|---------|--------|-----------------|
| 0-1 | Grigio-blu | Inizio / conoscenza |
| 2 | Verde acqua | Connessione emotiva in crescita |
| 3 | Giallo | Scambio personale attivo |
| 4 | Rosso tenue | Discussioni o argomenti delicati |
| 5 | Viola rosa | Affinità / profonda connessione |

## 🚀 Installazione e Avvio

1. **Clona il repository**
   ```bash
   git clone <repository-url>
   cd SoulTalk
   ```

2. **Installa le dipendenze**
   ```bash
   npm install
   ```

3. **Configura le variabili d'ambiente**
   
   Crea un file `.env.local` con:
   ```env
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   OPENROUTER_MODEL=moonshotai/kimi-k2:free
   NEXT_PUBLIC_APP_NAME=SoulTalk
   ```

4. **Avvia il server di sviluppo**
   ```bash
   npm run dev
   ```

5. **Apri il browser**
   
   Vai su [http://localhost:3000](http://localhost:3000)

## 📁 Struttura del Progetto

```
SoulTalk/
├── public/
│   └── assets/
├── src/
│   ├── components/
│   │   ├── ChatInput.tsx      # Campo di input per messaggi
│   │   ├── ChatBubble.tsx     # Bolle dei messaggi
│   │   ├── GlassFrame.tsx     # Cornice con effetto glass
│   │   └── AuraIndicator.tsx  # Indicatore dell'aura emotiva
│   ├── pages/
│   │   ├── _app.tsx          # Configurazione app Next.js
│   │   └── index.tsx         # Pagina principale
│   ├── state/
│   │   └── userMemory.ts     # Gestione memoria utente
│   ├── styles/
│   │   └── globals.css       # Stili globali
│   └── utils/
│       └── openrouter.ts     # Client API OpenRouter
├── .env.local                # Variabili d'ambiente
├── next.config.js           # Configurazione Next.js
├── tailwind.config.js       # Configurazione Tailwind
└── tsconfig.json           # Configurazione TypeScript
```

## 🧠 Sistema di Memoria

L'applicazione mantiene un profilo dettagliato dell'utente che include:

- **Informazioni base**: nome, età, pronomi
- **Tratti della personalità**: caratteristiche identificate
- **Emozioni**: stati emotivi espressi
- **Momenti chiave**: eventi significativi condivisi
- **Livello di intimità**: da 0 a 5
- **Cronologia conversazioni**: tutti i messaggi scambiati
- **Insights personalità**: analisi psicologiche

## 🎨 Caratteristiche UI

- **Effetto Glass**: Interfaccia con sfondo sfumato e trasparenze
- **Animazioni fluide**: Transizioni e micro-interazioni
- **Aura dinamica**: Glow animato che cambia colore
- **Design responsivo**: Ottimizzato per desktop e mobile
- **Tema scuro**: Palette di colori elegante

## 🔧 Comandi Disponibili

```bash
npm run dev      # Avvia server di sviluppo
npm run build    # Build per produzione
npm run start    # Avvia server di produzione
npm run lint     # Controllo codice con ESLint
```

## 🌟 Funzionalità Principali

### Chat Intelligente
- Conversazioni naturali con l'AI "Soul"
- Memoria persistente delle interazioni
- Analisi emotiva dei messaggi

### Progressione Relazionale
- 6 livelli di intimità
- Aura visiva che evolve
- Domande sempre più personali

### Interfaccia Immersiva
- Effetti visivi coinvolgenti
- Animazioni fluide
- Design moderno e pulito

## 🔒 Privacy e Sicurezza

- I dati sono salvati solo nel localStorage del browser
- Nessuna informazione personale viene inviata a server esterni
- Le conversazioni rimangono private sul dispositivo dell'utente

## 🚧 Sviluppi Futuri

- [ ] Integrazione con Supabase per backup cloud
- [ ] Sistema di esportazione/importazione memoria
- [ ] Analisi emotiva avanzata
- [ ] Personalità AI dinamica
- [ ] Supporto multilingua
- [ ] App mobile

## 📄 Licenza

Questo progetto è rilasciato sotto licenza ISC.

---

**SoulTalk** - Dove l'intelligenza artificiale incontra l'amicizia autentica. 💙
