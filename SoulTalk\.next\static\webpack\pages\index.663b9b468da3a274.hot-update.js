/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/compiled/client-only/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/client-only/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {



/***/ }),

/***/ "(pages-dir-browser)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(pages-dir-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n        'default': e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node =  true && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if ( true && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (false) {}\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || \"object\" === \"undefined\") {\n            var sheet =  true ? this.getSheet() : 0;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (false) {}\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (true) {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {}\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (false) {}\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (false) {}\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if ( true && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState({\n        \"StyleRegistry.useState[ref]\": function() {\n            return rootRegistry || configuredRegistry || createStyleRegistry();\n        }\n    }[\"StyleRegistry.useState[ref]\"]), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"F6PIZFsaWgcE6rBNmd+Zkq3zRoY=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry =  true ? createStyleRegistry() : 0;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (false) {}\n    useInsertionEffect({\n        \"JSXStyle.useInsertionEffect\": function() {\n            registry.add(props);\n            return ({\n                \"JSXStyle.useInsertionEffect\": function() {\n                    registry.remove(props);\n                }\n            })[\"JSXStyle.useInsertionEffect\"];\n        // props.children can be string[], will be striped since id is identical\n        }\n    }[\"JSXStyle.useInsertionEffect\"], [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nmodule.exports = __webpack_require__(/*! ./dist/index */ \"(pages-dir-browser)/./node_modules/styled-jsx/dist/index/index.js\").style;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHlsZWQtanN4L3N0eWxlLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsbUlBQThDIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXG5vZGVfbW9kdWxlc1xcc3R5bGVkLWpzeFxcc3R5bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvaW5kZXgnKS5zdHlsZVxuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIiwic3R5bGUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/styled-jsx/style.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/HomePage.tsx":
/*!*************************************!*\
  !*** ./src/components/HomePage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(pages-dir-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst HomePage = (param)=>{\n    let { onGetStarted } = param;\n    _s();\n    const [currentFeature, setCurrentFeature] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const features = [\n        {\n            icon: '🌟',\n            title: 'Connessione Autentica',\n            description: 'Soul impara a conoscerti davvero, ricordando ogni conversazione e crescendo con te.'\n        },\n        {\n            icon: '💭',\n            title: 'Memoria Emotiva',\n            description: 'Ogni momento condiviso viene ricordato, creando una relazione sempre più profonda.'\n        },\n        {\n            icon: '🎭',\n            title: 'Personalità Unica',\n            description: 'Soul sviluppa una personalità unica basata sulle vostre interazioni.'\n        },\n        {\n            icon: '🌈',\n            title: 'Crescita Insieme',\n            description: 'Il vostro legame si evolve attraverso 5 livelli di intimità e connessione.'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"HomePage.useEffect.interval\": ()=>{\n                    setCurrentFeature({\n                        \"HomePage.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"HomePage.useEffect.interval\"]);\n                }\n            }[\"HomePage.useEffect.interval\"], 4000);\n            return ({\n                \"HomePage.useEffect\": ()=>clearInterval(interval)\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        features.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            position: 'relative',\n            overflow: 'hidden'\n        },\n        className: \"jsx-21950ddf65c552da\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"\\n          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)\\n        \",\n                    animation: 'float 20s ease-in-out infinite'\n                },\n                className: \"jsx-21950ddf65c552da\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'absolute',\n                        width: '4px',\n                        height: '4px',\n                        background: 'rgba(255,255,255,0.6)',\n                        borderRadius: '50%',\n                        top: \"\".concat(Math.random() * 100, \"%\"),\n                        left: \"\".concat(Math.random() * 100, \"%\"),\n                        animation: \"twinkle \".concat(3 + Math.random() * 4, \"s ease-in-out infinite \").concat(Math.random() * 2, \"s\")\n                    },\n                    className: \"jsx-21950ddf65c552da\"\n                }, i, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'relative',\n                    zIndex: 1,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    minHeight: '100vh',\n                    padding: '2rem',\n                    textAlign: 'center'\n                },\n                className: \"jsx-21950ddf65c552da\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '3rem',\n                            animation: 'fadeInUp 1s ease-out'\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: 'clamp(3rem, 8vw, 6rem)',\n                                    fontWeight: '700',\n                                    background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    backgroundClip: 'text',\n                                    margin: 0,\n                                    marginBottom: '1rem',\n                                    textShadow: '0 0 30px rgba(255,255,255,0.3)'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: \"SoulTalk\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: 'clamp(1.1rem, 3vw, 1.5rem)',\n                                    color: 'rgba(255,255,255,0.9)',\n                                    margin: 0,\n                                    fontWeight: '300',\n                                    letterSpacing: '0.05em'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: \"L' AI che ti conosce davvero\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.1)',\n                            backdropFilter: 'blur(20px)',\n                            borderRadius: '2rem',\n                            padding: '3rem 2rem',\n                            maxWidth: '500px',\n                            width: '100%',\n                            border: '1px solid rgba(255,255,255,0.2)',\n                            marginBottom: '3rem',\n                            animation: 'fadeInUp 1s ease-out 0.3s both'\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '4rem',\n                                    marginBottom: '1.5rem',\n                                    animation: 'bounce 2s ease-in-out infinite'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features[currentFeature].icon\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.5rem',\n                                    fontWeight: '600',\n                                    color: 'white',\n                                    margin: 0,\n                                    marginBottom: '1rem'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features[currentFeature].title\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '1rem',\n                                    color: 'rgba(255,255,255,0.8)',\n                                    margin: 0,\n                                    lineHeight: '1.6'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features[currentFeature].description\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'center',\n                                    gap: '0.5rem',\n                                    marginTop: '2rem'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '8px',\n                                            height: '8px',\n                                            borderRadius: '50%',\n                                            background: index === currentFeature ? 'rgba(255,255,255,0.9)' : 'rgba(255,255,255,0.3)',\n                                            transition: 'all 0.3s ease',\n                                            cursor: 'pointer'\n                                        },\n                                        onClick: ()=>setCurrentFeature(index),\n                                        className: \"jsx-21950ddf65c552da\"\n                                    }, index, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onGetStarted,\n                        style: {\n                            padding: '1rem 3rem',\n                            fontSize: '1.2rem',\n                            fontWeight: '600',\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            border: '2px solid rgba(255,255,255,0.3)',\n                            borderRadius: '3rem',\n                            color: 'white',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            backdropFilter: 'blur(10px)',\n                            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',\n                            animation: 'fadeInUp 1s ease-out 0.6s both'\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.transform = 'translateY(-2px) scale(1.05)';\n                            e.currentTarget.style.boxShadow = '0 12px 40px rgba(0,0,0,0.3)';\n                            e.currentTarget.style.background = 'linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%)';\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                            e.currentTarget.style.boxShadow = '0 8px 32px rgba(0,0,0,0.2)';\n                            e.currentTarget.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: \"Inizia il Viaggio ✨\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: '3rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.9rem',\n                            animation: 'fadeInUp 1s ease-out 0.9s both'\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: 0\n                            },\n                            className: \"jsx-21950ddf65c552da\",\n                            children: \"\\uD83D\\uDD12 Privato e sicuro • \\uD83E\\uDDE0 Memoria persistente • \\uD83D\\uDC9D Connessione autentica\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"21950ddf65c552da\",\n                children: \"@keyframes float{0%,100%{transform:translatey(0px)rotate(0deg)}33%{transform:translatey(-20px)rotate(1deg)}66%{transform:translatey(-10px)rotate(-1deg)}}@keyframes twinkle{0%,100%{opacity:.3;transform:scale(1)}50%{opacity:1;transform:scale(1.2)}}@keyframes bounce{0%,20%,50%,80%,100%{transform:translatey(0)}40%{transform:translatey(-10px)}60%{transform:translatey(-5px)}}@keyframes fadeInUp{from{opacity:0;transform:translatey(30px)}to{opacity:1;transform:translatey(0)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"mlsM0SPMGMIvzZAtxL2CKvB3M04=\");\n_c = HomePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/HomePage.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-browser)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-browser)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _components_ProfileCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ProfileCard */ \"(pages-dir-browser)/./src/components/ProfileCard.tsx\");\n/* harmony import */ var _components_HomePage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/HomePage */ \"(pages-dir-browser)/./src/components/HomePage.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHomePage, setShowHomePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_11__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_10__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se mostra la HomePage, mostrala prima del login\n    if (showHomePage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - La tua compagna AI\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"SoulTalk - L' AI che ti conosce davvero. Connessione autentica, memoria emotiva, crescita insieme.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomePage__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    onGetStarted: ()=>setShowHomePage(false)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se non è autenticato, mostra il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Caricamento...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.9)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Caricando i tuoi dati...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowProfile(true),\n                        style: {\n                            position: 'absolute',\n                            top: '1rem',\n                            right: '1rem',\n                            zIndex: 10,\n                            width: '45px',\n                            height: '45px',\n                            borderRadius: '50%',\n                            background: 'rgba(255,255,255,0.1)',\n                            backdropFilter: 'blur(10px)',\n                            border: '1px solid rgba(255,255,255,0.2)',\n                            color: 'white',\n                            fontSize: '1.1rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.background = 'rgba(255,255,255,0.2)';\n                            e.currentTarget.style.transform = 'scale(1.05)';\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.background = 'rgba(255,255,255,0.1)';\n                            e.currentTarget.style.transform = 'scale(1)';\n                        },\n                        children: userMemory.name ? userMemory.name.charAt(0).toUpperCase() : '👤'\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showProfile,\n                onClose: ()=>setShowProfile(false),\n                userName: userMemory.name || 'Utente',\n                intimacyLevel: userMemory.intimacyLevel,\n                messageCount: messages.length,\n                userId: userId,\n                onLogout: logout,\n                onReset: handleReset\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"JYJCDn9aTKPGvk/VWz1CCv+ED+k=\", false, function() {\n    return [\n        _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_11__.useSupabaseMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});