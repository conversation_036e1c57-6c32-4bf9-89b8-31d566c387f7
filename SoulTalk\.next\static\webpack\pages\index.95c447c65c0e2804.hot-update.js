"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString() + Math.random(),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                setUserMemory((prev)=>({\n                        ...prev,\n                        name\n                    }));\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        age\n                    }));\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        emotions: prev.emotions.includes(emotion) ? prev.emotions : [\n                            ...prev.emotions,\n                            emotion\n                        ]\n                    }));\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            setUserMemory((prev)=>({\n                    ...prev,\n                    intimacyLevel: Math.min(prev.intimacyLevel + 1, 4)\n                }));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoading\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_4__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setShowWelcome(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Soul Chat\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: 'white',\n                                    fontSize: '1.5rem',\n                                    fontWeight: 'bold',\n                                    margin: 0\n                                },\n                                children: \"Soul Chat\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"chat-messages\",\n                        children: [\n                            messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '2rem',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Dimmi, come ti chiami e come ti senti oggi?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message.content,\n                                    isUser: message.isUser\n                                }, message.id, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: '1rem',\n                                    color: 'white'\n                                },\n                                children: \"Soul sta scrivendo...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi il tuo messaggio...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"cgrLIBRXCj1AA7cqIMfvoMc77lM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});