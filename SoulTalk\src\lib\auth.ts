import { supabase } from './supabase'
import bcrypt from 'bcryptjs'

export interface UserAuth {
  id: string
  username: string
  password_hash: string
  created_at: string
  last_login: string
}

export interface AuthResult {
  success: boolean
  userId?: string
  error?: string
}

// Funzione per hash della password
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12
  return await bcrypt.hash(password, saltRounds)
}

// Funzione per verificare la password
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash)
}

// Registrazione nuovo utente
export const registerUser = async (username: string, password: string): Promise<AuthResult> => {
  try {
    // Controlla se l'username esiste già
    const { data: existingUser, error: checkError } = await supabase
      .from('user_auth')
      .select('username')
      .eq('username', username.toLowerCase())
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing user:', checkError)
      return { success: false, error: 'Errore durante il controllo username' }
    }

    if (existingUser) {
      return { success: false, error: 'Username già esistente' }
    }

    // Crea hash della password
    const passwordHash = await hashPassword(password)

    // Genera ID utente
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Inserisci nella tabella auth
    const { error: authError } = await supabase
      .from('user_auth')
      .insert({
        id: userId,
        username: username.toLowerCase(),
        password_hash: passwordHash
      })

    if (authError) {
      console.error('Error creating auth:', authError)
      return { success: false, error: 'Errore durante la registrazione' }
    }

    // Crea profilo utente
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: userId,
        username: username.toLowerCase(),
        name: '',
        is_onboarded: false
      })

    if (profileError) {
      console.error('Error creating profile:', profileError)
      // Rollback: elimina l'auth se il profilo fallisce
      await supabase.from('user_auth').delete().eq('id', userId)
      return { success: false, error: 'Errore durante la creazione del profilo' }
    }

    return { success: true, userId }

  } catch (error) {
    console.error('Registration error:', error)
    return { success: false, error: 'Errore interno del server' }
  }
}

// Login utente
export const loginUser = async (username: string, password: string): Promise<AuthResult> => {
  try {
    // Trova l'utente
    const { data: user, error } = await supabase
      .from('user_auth')
      .select('*')
      .eq('username', username.toLowerCase())
      .single()

    if (error || !user) {
      return { success: false, error: 'Username o password non corretti' }
    }

    // Verifica password
    const isValidPassword = await verifyPassword(password, user.password_hash)
    
    if (!isValidPassword) {
      return { success: false, error: 'Username o password non corretti' }
    }

    // Aggiorna last_login
    await supabase
      .from('user_auth')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id)

    return { success: true, userId: user.id }

  } catch (error) {
    console.error('Login error:', error)
    return { success: false, error: 'Errore interno del server' }
  }
}

// Verifica se l'utente è autenticato (controlla localStorage)
export const getCurrentUser = (): string | null => {
  if (typeof window === 'undefined') return null
  
  const authData = localStorage.getItem('soul_auth')
  if (!authData) return null

  try {
    const { userId, timestamp } = JSON.parse(authData)
    
    // Controlla se la sessione è scaduta (24 ore)
    const now = Date.now()
    const sessionAge = now - timestamp
    const maxAge = 24 * 60 * 60 * 1000 // 24 ore

    if (sessionAge > maxAge) {
      localStorage.removeItem('soul_auth')
      return null
    }

    return userId
  } catch {
    localStorage.removeItem('soul_auth')
    return null
  }
}

// Salva sessione utente
export const saveUserSession = (userId: string): void => {
  if (typeof window === 'undefined') return

  const authData = {
    userId,
    timestamp: Date.now()
  }
  
  localStorage.setItem('soul_auth', JSON.stringify(authData))
}

// Logout utente
export const logoutUser = (): void => {
  if (typeof window === 'undefined') return
  
  localStorage.removeItem('soul_auth')
  localStorage.removeItem('soul_user_id') // Rimuovi anche il vecchio ID
}

// Controlla se l'utente ha completato l'onboarding
export const checkOnboardingStatus = async (userId: string): Promise<boolean> => {
  try {
    console.log('Checking onboarding status for user:', userId)

    const { data, error } = await supabase
      .from('user_profiles')
      .select('is_onboarded')
      .eq('id', userId)
      .single()

    console.log('Onboarding check result:', { data, error })

    if (error || !data) {
      console.log('User needs onboarding - no data or error')
      return false
    }

    const isOnboarded = data.is_onboarded || false
    console.log('User onboarding status:', isOnboarded)
    return isOnboarded
  } catch (error) {
    console.error('Exception in checkOnboardingStatus:', error)
    return false
  }
}

// Completa l'onboarding
export const completeOnboarding = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({ is_onboarded: true })
      .eq('id', userId)

    return !error
  } catch {
    return false
  }
}

// Funzione di debug per resettare l'onboarding (solo per test)
export const resetOnboardingStatus = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({ is_onboarded: false, name: '', age: null })
      .eq('id', userId)

    return !error
  } catch {
    return false
  }
}
