import React from 'react';

interface GlassFrameProps {
  children: React.ReactNode;
  auraColor?: string;
  className?: string;
}

const GlassFrame: React.FC<GlassFrameProps> = ({ 
  children, 
  auraColor = 'blue', 
  className = '' 
}) => {
  const getAuraColorClass = (color: string) => {
    const colorMap = {
      'gray-blue': 'shadow-blue-500/30',
      'teal': 'shadow-teal-500/40',
      'yellow': 'shadow-yellow-500/40',
      'red': 'shadow-red-500/30',
      'purple-pink': 'shadow-purple-500/40',
      'blue': 'shadow-blue-500/30'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getAuraColorRgba = (color: string) => {
    const colorMap = {
      'gray-blue': 'rgba(59, 130, 246, 0.3)',
      'teal': 'rgba(20, 184, 166, 0.4)',
      'yellow': 'rgba(245, 158, 11, 0.4)',
      'red': 'rgba(239, 68, 68, 0.3)',
      'purple-pink': 'rgba(139, 92, 246, 0.4)',
      'blue': 'rgba(59, 130, 246, 0.3)'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Aura esterna animata */}
      <div
        className="absolute inset-0 rounded-2xl aura-animation"
        style={{
          filter: 'blur(2px)',
          transform: 'scale(1.02)',
          boxShadow: `0 0 40px ${getAuraColorRgba(auraColor)}`,
        }}
      />

      {/* Frame principale con effetto glass */}
      <div className="relative glass-effect transition-all duration-500" style={{
        border: `2px solid rgba(255, 255, 255, 0.2)`,
        boxShadow: `0 0 30px ${getAuraColorRgba(auraColor)}`
      }}>
        {children}
      </div>
    </div>
  );
};

export default GlassFrame;
