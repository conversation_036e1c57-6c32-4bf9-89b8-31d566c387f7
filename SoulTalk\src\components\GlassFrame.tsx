import React from 'react';

interface GlassFrameProps {
  children: React.ReactNode;
  auraColor?: string;
  className?: string;
}

const GlassFrame: React.FC<GlassFrameProps> = ({ 
  children, 
  auraColor = 'blue', 
  className = '' 
}) => {
  const getAuraColorClass = (color: string) => {
    const colorMap = {
      'gray-blue': 'shadow-blue-500/30',
      'teal': 'shadow-teal-500/40',
      'yellow': 'shadow-yellow-500/40',
      'red': 'shadow-red-500/30',
      'purple-pink': 'shadow-purple-500/40',
      'blue': 'shadow-blue-500/30'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getAuraGlow = (color: string) => {
    const glowMap = {
      'gray-blue': 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',
      'teal': 'drop-shadow-[0_0_20px_rgba(20,184,166,0.4)]',
      'yellow': 'drop-shadow-[0_0_20px_rgba(245,158,11,0.4)]',
      'red': 'drop-shadow-[0_0_20px_rgba(239,68,68,0.3)]',
      'purple-pink': 'drop-shadow-[0_0_20px_rgba(139,92,246,0.4)]',
      'blue': 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]'
    };
    return glowMap[color as keyof typeof glowMap] || glowMap.blue;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Aura esterna animata */}
      <div 
        className={`
          absolute inset-0 rounded-2xl 
          ${getAuraColorClass(auraColor)}
          ${getAuraGlow(auraColor)}
          aura-animation
          shadow-2xl
        `}
        style={{
          filter: 'blur(2px)',
          transform: 'scale(1.02)',
        }}
      />
      
      {/* Frame principale con effetto glass */}
      <div className={`
        relative glass-effect
        border-2 border-white/20
        ${getAuraColorClass(auraColor)}
        transition-all duration-500 ease-in-out
        hover:border-white/30
        hover:shadow-2xl
      `}>
        {children}
      </div>
    </div>
  );
};

export default GlassFrame;
