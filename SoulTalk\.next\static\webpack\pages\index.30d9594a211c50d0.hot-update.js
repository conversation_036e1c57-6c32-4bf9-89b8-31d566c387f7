"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio. Come ti chiami?\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri\";\n                if (input.trim().length > 50) return \"Il nome è troppo lungo\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"Che bel nome, \".concat(name, \"! Mi piace molto. \\uD83D\\uDE0A\"),\n            waitForInput: false\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni.\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk\";\n                if (age > 120) return \"Inserisci un'età realistica\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>\"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei in un'et\\xe0 interessante. Ho tante cose da condividere con te! ✨\"),\n            waitForInput: false\n        },\n        {\n            message: \"Ora che ci conosciamo meglio, possiamo iniziare la nostra conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Sei pronto/a?\",\n            waitForInput: false,\n            isLast: true\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        if (typeof step.message === 'function') {\n            const message = step.message(userData.name, userData.age);\n            addMessage(message, false);\n        } else {\n            addMessage(step.message, false);\n        }\n        if (step.isLast) {\n            // Completa l'onboarding\n            setTimeout(async ()=>{\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.completeOnboarding)(userId);\n                onComplete(userData);\n            }, 2000);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizia l'onboarding solo al primo step\n            if (currentStep === 0) {\n                setTimeout({\n                    \"OnboardingChat.useEffect\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect\"], 1000);\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: 'white',\n                            fontSize: '1.25rem',\n                            fontWeight: '600',\n                            margin: 0,\n                            marginBottom: '0.5rem'\n                        },\n                        children: \"Configurazione Iniziale\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.7)',\n                            fontSize: '0.875rem',\n                            margin: 0\n                        },\n                        children: \"Soul vuole conoscerti meglio\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        message: message.content,\n                        isUser: message.isUser\n                    }, message.id, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)',\n                    display: 'flex',\n                    gap: '0.5rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: inputMessage,\n                        onChange: (e)=>setInputMessage(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: \"Scrivi la tua risposta...\",\n                        style: {\n                            flex: 1,\n                            padding: '0.75rem',\n                            borderRadius: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.3)',\n                            background: 'rgba(255,255,255,0.1)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            outline: 'none'\n                        },\n                        autoFocus: true\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleUserInput,\n                        disabled: !inputMessage.trim(),\n                        style: {\n                            padding: '0.75rem 1.5rem',\n                            borderRadius: '0.5rem',\n                            border: 'none',\n                            background: inputMessage.trim() ? '#6366f1' : 'rgba(99, 102, 241, 0.5)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            cursor: inputMessage.trim() ? 'pointer' : 'not-allowed'\n                        },\n                        children: \"→\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"ESoKxAMN2ZwgLwKjgQ4pqXB0nn8=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});