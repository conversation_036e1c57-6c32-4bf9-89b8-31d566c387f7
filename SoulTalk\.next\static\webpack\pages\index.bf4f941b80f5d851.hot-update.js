"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString() + Math.random(),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                setUserMemory((prev)=>({\n                        ...prev,\n                        name\n                    }));\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        age\n                    }));\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        emotions: prev.emotions.includes(emotion) ? prev.emotions : [\n                            ...prev.emotions,\n                            emotion\n                        ]\n                    }));\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            setUserMemory((prev)=>({\n                    ...prev,\n                    intimacyLevel: Math.min(prev.intimacyLevel + 1, 4)\n                }));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoading\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_6__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setShowWelcome(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '0.875rem'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    messages.length,\n                                    \" msg\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"cgrLIBRXCj1AA7cqIMfvoMc77lM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});