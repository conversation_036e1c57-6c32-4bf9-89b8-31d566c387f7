"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  background-attachment: fixed;\\n  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;\\n  animation: liquidMove 20s ease-in-out infinite;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 1rem 1.25rem;\\n  border-radius: 1rem;\\n  min-width: 8rem;\\n  max-width: 24rem;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  font-size: 0.95rem;\\n  line-height: 1.4rem;\\n  display: flex;\\n  align-items: center;\\n  text-align: left;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.6);\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  border: 1px solid rgba(37, 99, 235, 0.3);\\n  margin-left: auto;\\n  box-shadow:\\n    0 4px 16px rgba(37, 99, 235, 0.2),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.08);\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  margin-right: auto;\\n  box-shadow:\\n    0 4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem 0.75rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 40px;\\n  max-height: 80px;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  box-shadow:\\n    0 4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  border-radius: 0.5rem;\\n  padding: 0.5rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n  box-shadow:\\n    0 4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-main-container {\\n  height: calc(100vh - 120px);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.glass-frame-container {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-messages {\\n  flex: 1 1;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden;\\n  padding: 0 0.75rem;\\n  min-height: 0;\\n  max-height: calc(100vh - 200px);\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none;\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  background: rgba(255, 255, 255, 0.05);\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  border-top: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 -4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; line-height: 1.2; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; line-height: 1.1; }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n@keyframes liquidMove {\\n  0%, 100% {\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  25% {\\n    background:\\n      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  50% {\\n    background:\\n      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  75% {\\n    background:\\n      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/global.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ;;;;kEAIgE;EAChE,4BAA4B;EAC5B,2DAA2D;EAC3D,8CAA8C;AAChD;;AAEA,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,mBAAmB,kBAAkB,EAAE;AACvC,uBAAuB,qBAAqB,EAAE;AAC9C,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,sBAAsB,EAAE;AAChC,qBAAqB,kBAAkB,EAAE;AACzC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,kBAAkB,EAAE,mBAAmB,EAAE;AACrD,WAAW,eAAe,EAAE,oBAAoB,EAAE;AAClD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,WAAW,mBAAmB,EAAE,qBAAqB,EAAE;AACvD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,SAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;;AAEzB,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,kDAA0C;UAA1C,0CAA0C;EAC1C,qCAAqC;EACrC,2CAA2C;EAC3C;;;4CAG0C;EAC1C,mBAAmB;AACrB;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,eAAe;EACf,gBAAgB;EAChB,uBAAkB;EAAlB,kBAAkB;EAClB,6CAA6C;EAC7C,0CAA0C;EAC1C,qBAAqB;EACrB,yBAAyB;EACzB,kBAAkB;EAClB,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,kDAA0C;UAA1C,0CAA0C;EAC1C,wCAAwC;EACxC,iBAAiB;EACjB;;0CAEwC;AAC1C;;AAEA;EACE,qCAAqC;EACrC,kDAA0C;UAA1C,0CAA0C;EAC1C,2CAA2C;EAC3C,kBAAkB;EAClB;;0CAEwC;AAC1C;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,kDAA0C;UAA1C,0CAA0C;EAC1C,qCAAqC;EACrC,2CAA2C;EAC3C,sBAAsB;EACtB,uBAAuB;EACvB,YAAY;EACZ,yBAAyB;EACzB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,oBAAoB;EACpB;;0CAEwC;AAC1C;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,kDAA0C;UAA1C,0CAA0C;EAC1C,qCAAqC;EACrC,2CAA2C;EAC3C,qBAAqB;EACrB,eAAe;EACf,YAAY;EACZ,eAAe;EACf,yBAAyB;EACzB,YAAY;EACZ;;0CAEwC;AAC1C;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,+BAA+B;AAC/B;EACE,aAAa;EACb,aAAa;EACb,sBAAsB;EACtB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,2BAA2B;EAC3B,aAAa;EACb,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,sBAAsB;AACxB;;AAEA;EACE,SAAO;EACP,6BAA6B;EAC7B,kBAAkB;EAClB,kBAAkB;EAClB,aAAa;EACb,+BAA+B;EAC/B,qBAAqB;EACrB,wBAAwB;EACxB,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,aAAa;AACf;;AAEA;EACE,cAAc;EACd,gBAAgB;EAChB,qCAAqC;EACrC,kDAA0C;UAA1C,0CAA0C;EAC1C,+CAA+C;EAC/C;;0CAEwC;AAC1C;;AAEA,4BAA4B;AAC5B;EACE,eAAe,gBAAgB,EAAE;EACjC,iBAAiB,iBAAiB,EAAE;EACpC,mBAAmB,eAAe,EAAE;EACpC,YAAY,eAAe,EAAE,oBAAoB,EAAE;EACnD,WAAW,mBAAmB,EAAE,iBAAiB,EAAE;EACnD,OAAO,gBAAgB,EAAE;EACzB,QAAQ,qBAAqB,EAAE;EAC/B,eAAe,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE;AACxF;;AAEA;EACE,eAAe,iBAAiB,EAAE;EAClC,iBAAiB,kBAAkB,EAAE;EACrC,mBAAmB,gBAAgB,EAAE;EACrC,YAAY,mBAAmB,EAAE,iBAAiB,EAAE;EACpD,qBAAqB,mBAAmB,EAAE;EAC1C,qBAAqB,kBAAkB,EAAE;EACzC,eAAe,cAAc,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE;EACzF,eAAe,gBAAgB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE;AACjF;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA;EACE;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;AACF\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  background-attachment: fixed;\\n  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;\\n  animation: liquidMove 20s ease-in-out infinite;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 1rem 1.25rem;\\n  border-radius: 1rem;\\n  min-width: 8rem;\\n  max-width: 24rem;\\n  width: fit-content;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  font-size: 0.95rem;\\n  line-height: 1.4rem;\\n  display: flex;\\n  align-items: center;\\n  text-align: left;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.6);\\n  backdrop-filter: blur(20px) saturate(180%);\\n  border: 1px solid rgba(37, 99, 235, 0.3);\\n  margin-left: auto;\\n  box-shadow:\\n    0 4px 16px rgba(37, 99, 235, 0.2),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.08);\\n  backdrop-filter: blur(20px) saturate(180%);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  margin-right: auto;\\n  box-shadow:\\n    0 4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem 0.75rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 40px;\\n  max-height: 80px;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  box-shadow:\\n    0 4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  border-radius: 0.5rem;\\n  padding: 0.5rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n  box-shadow:\\n    0 4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-main-container {\\n  height: calc(100vh - 120px);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.glass-frame-container {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden;\\n  padding: 0 0.75rem;\\n  min-height: 0;\\n  max-height: calc(100vh - 200px);\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none;\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  background: rgba(255, 255, 255, 0.05);\\n  backdrop-filter: blur(20px) saturate(180%);\\n  border-top: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 -4px 16px rgba(0, 0, 0, 0.1),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; line-height: 1.2; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; line-height: 1.1; }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n@keyframes liquidMove {\\n  0%, 100% {\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  25% {\\n    background:\\n      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  50% {\\n    background:\\n      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  75% {\\n    background:\\n      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzEzXS51c2VbMV0hLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbN10ub25lT2ZbMTNdLnVzZVsyXSEuL3NyYy9zdHlsZXMvZ2xvYmFsLmNzcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMySDtBQUMzSCw4QkFBOEIsa0hBQTJCO0FBQ3pEO0FBQ0Esd0VBQXdFLGNBQWMsZUFBZSwyQkFBMkIsR0FBRyxVQUFVLGdEQUFnRCxHQUFHLFVBQVUsc0JBQXNCLGlCQUFpQix5VkFBeVYsaUNBQWlDLGdFQUFnRSxtREFBbUQsR0FBRyxtQ0FBbUMsZ0JBQWdCLGNBQWMseUJBQXlCLGtCQUFrQixzQkFBc0IsZUFBZSx3QkFBd0Isb0JBQW9CLDBCQUEwQixxQkFBcUIsaUNBQWlDLGlCQUFpQixxQkFBcUIsZUFBZSxtQkFBbUIsa0JBQWtCLG9CQUFvQixZQUFZLGNBQWMsWUFBWSxlQUFlLHFCQUFxQixxQkFBcUIseUJBQXlCLHdCQUF3QixlQUFlLG1CQUFtQixhQUFhLG1CQUFtQixxQkFBcUIsU0FBUyxnQkFBZ0IsU0FBUyxrQkFBa0IsVUFBVSx5QkFBeUIsVUFBVSx3QkFBd0IsVUFBVSx5QkFBeUIsdUJBQXVCLHFCQUFxQix1QkFBdUIsc0JBQXNCLHVCQUF1QixzQkFBc0IsdUJBQXVCLHVCQUF1QixjQUFjLG9CQUFvQixzQkFBc0IsYUFBYSxpQkFBaUIsdUJBQXVCLGFBQWEsb0JBQW9CLG9CQUFvQixhQUFhLHFCQUFxQix3QkFBd0IsZUFBZSxtQkFBbUIsbUJBQW1CLG1CQUFtQixnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxpQkFBaUIsc0JBQXNCLGdCQUFnQix5QkFBeUIsZUFBZSxzRkFBc0Ysb0JBQW9CLDZCQUE2QixrQkFBa0IsNkJBQTZCLGtCQUFrQiw2QkFBNkIsa0JBQWtCLDZCQUE2Qiw2QkFBNkIseUJBQXlCLGtCQUFrQixxQ0FBcUMsb0JBQW9CLGdDQUFnQyxtQkFBbUIsNERBQTRELFlBQVksWUFBWSxxQkFBcUIsbUJBQW1CLGNBQWMscUJBQXFCLGNBQWMscUJBQXFCLGFBQWEsUUFBUSxVQUFVLFdBQVcsVUFBVSxjQUFjLHdCQUF3QixZQUFZLGdCQUFnQixXQUFXLGlCQUFpQiw0REFBNEQsa0JBQWtCLGlCQUFpQixtQkFBbUIsZ0JBQWdCLEdBQUcsNENBQTRDLHVEQUF1RCx1REFBdUQsMENBQTBDLGdEQUFnRCxnSkFBZ0osd0JBQXdCLEdBQUcsa0JBQWtCLDBCQUEwQix3QkFBd0Isb0JBQW9CLHFCQUFxQiw0QkFBNEIsdUJBQXVCLGtEQUFrRCwrQ0FBK0MsMEJBQTBCLDhCQUE4Qix1QkFBdUIsd0JBQXdCLGtCQUFrQix3QkFBd0IscUJBQXFCLEdBQUcsdUJBQXVCLHVDQUF1Qyx1REFBdUQsdURBQXVELDZDQUE2QyxzQkFBc0Isb0dBQW9HLEdBQUcscUJBQXFCLDBDQUEwQyx1REFBdUQsdURBQXVELGdEQUFnRCx1QkFBdUIsZ0dBQWdHLEdBQUcscUJBQXFCLDhEQUE4RCxzQkFBc0IsR0FBRyxrQkFBa0IsdURBQXVELHVEQUF1RCwwQ0FBMEMsZ0RBQWdELDJCQUEyQiw0QkFBNEIsaUJBQWlCLDhCQUE4QixnQkFBZ0IsaUJBQWlCLHFCQUFxQixxQkFBcUIsd0JBQXdCLHlCQUF5QixnR0FBZ0csR0FBRywrQkFBK0Isb0NBQW9DLEdBQUcsd0JBQXdCLGtCQUFrQixrREFBa0QsR0FBRyxVQUFVLHVEQUF1RCx1REFBdUQsMENBQTBDLGdEQUFnRCwwQkFBMEIsb0JBQW9CLGlCQUFpQixvQkFBb0IsOEJBQThCLGlCQUFpQixnR0FBZ0csR0FBRyxnQkFBZ0IsMkJBQTJCLG9EQUFvRCxHQUFHLGlCQUFpQiwyQkFBMkIsR0FBRyxtQkFBbUIsaUJBQWlCLHdCQUF3QixHQUFHLHlCQUF5QixvQkFBb0IsR0FBRyxvREFBb0Qsa0JBQWtCLGtCQUFrQiwyQkFBMkIscUJBQXFCLHFCQUFxQixHQUFHLGtCQUFrQixtQkFBbUIsb0JBQW9CLEdBQUcsMEJBQTBCLGdDQUFnQyxrQkFBa0IsMkJBQTJCLEdBQUcsNEJBQTRCLGlCQUFpQixrQkFBa0IsMkJBQTJCLEdBQUcsb0JBQW9CLGNBQWMsa0NBQWtDLHVCQUF1Qix1QkFBdUIsa0JBQWtCLG9DQUFvQywwQkFBMEIsNkJBQTZCLDRCQUE0QixpQ0FBaUMsR0FBRyx1Q0FBdUMsYUFBYSxjQUFjLGtCQUFrQixHQUFHLHNCQUFzQixtQkFBbUIscUJBQXFCLDBDQUEwQyx1REFBdUQsdURBQXVELG9EQUFvRCxpR0FBaUcsR0FBRyw4REFBOEQsbUJBQW1CLG1CQUFtQixxQkFBcUIsb0JBQW9CLHVCQUF1QixrQkFBa0IsZ0JBQWdCLGlCQUFpQix1QkFBdUIsZUFBZSxxQkFBcUIsb0JBQW9CLFdBQVcsbUJBQW1CLFlBQVksd0JBQXdCLG1CQUFtQixnQkFBZ0IsaUJBQWlCLG9CQUFvQixtQkFBbUIsR0FBRywrQkFBK0IsbUJBQW1CLG9CQUFvQixxQkFBcUIscUJBQXFCLHVCQUF1QixtQkFBbUIsZ0JBQWdCLHFCQUFxQixvQkFBb0IseUJBQXlCLHNCQUFzQix5QkFBeUIscUJBQXFCLG1CQUFtQixnQkFBZ0IsbUJBQW1CLHFCQUFxQixtQkFBbUIsbUJBQW1CLGtCQUFrQixvQkFBb0IsMkJBQTJCLEdBQUcsK0JBQStCLG1CQUFtQixnQkFBZ0IsaUJBQWlCLEdBQUcsZ0NBQWdDLHFCQUFxQixrQkFBa0IsdUJBQXVCLHVCQUF1QixHQUFHLDZEQUE2RCxVQUFVLGlCQUFpQixpQ0FBaUMsS0FBSyxRQUFRLGlCQUFpQiwrQkFBK0IsS0FBSyxHQUFHLHFCQUFxQixXQUFXLDBCQUEwQixTQUFTLDRCQUE0QixHQUFHLHVCQUF1QixjQUFjLGtDQUFrQyw0REFBNEQsS0FBSyxTQUFTLCtCQUErQiw0REFBNEQsS0FBSyxHQUFHLHNCQUFzQixlQUFlLGFBQWEsVUFBVSxlQUFlLEdBQUcsMkJBQTJCLGNBQWMsbVdBQW1XLEtBQUssU0FBUyxtV0FBbVcsS0FBSyxTQUFTLG1XQUFtVyxLQUFLLFNBQVMsbVdBQW1XLEtBQUssR0FBRyxTQUFTLDZGQUE2RixNQUFNLFVBQVUsVUFBVSxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLFdBQVcsUUFBUSxPQUFPLGFBQWEsYUFBYSxhQUFhLE9BQU8sWUFBWSxxQkFBcUIsdUJBQXVCLHlCQUF5Qix1QkFBdUIseUJBQXlCLHlCQUF5Qix1QkFBdUIsdUJBQXVCLHlCQUF5QixxQkFBcUIscUJBQXFCLHlCQUF5Qix5QkFBeUIsdUJBQXVCLG1DQUFtQyxxQkFBcUIscUJBQXFCLHVCQUF1Qix1QkFBdUIsdUJBQXVCLHlCQUF5Qix5QkFBeUIseUJBQXlCLHlCQUF5QixtQ0FBbUMsaUNBQWlDLG1DQUFtQyxtQ0FBbUMsdUJBQXVCLHlCQUF5QixxQkFBcUIscUJBQXFCLHFCQUFxQixxQkFBcUIsdUJBQXVCLHVCQUF1Qix1QkFBdUIseUJBQXlCLHlCQUF5Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix5QkFBeUIseUJBQXlCLHlCQUF5QixxQkFBcUIseUJBQXlCLHVCQUF1Qix1QkFBdUIsbURBQW1ELHVCQUF1QixxQkFBcUIsc0JBQXNCLGFBQWEsTUFBTSxvQkFBb0IscUJBQXFCLE9BQU8sWUFBWSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsUUFBUSxPQUFPLGFBQWEsT0FBTyxLQUFLLFlBQVksYUFBYSxXQUFXLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFdBQVcsWUFBWSxhQUFhLE9BQU8sS0FBSyxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsT0FBTyxPQUFPLE9BQU8sS0FBSyxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsT0FBTyxPQUFPLE9BQU8sS0FBSyxZQUFZLGFBQWEsT0FBTyxLQUFLLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFdBQVcsWUFBWSxXQUFXLFVBQVUsWUFBWSxhQUFhLGFBQWEsYUFBYSxPQUFPLE9BQU8sT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFVBQVUsWUFBWSxPQUFPLEtBQUssWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLFdBQVcsVUFBVSxVQUFVLFlBQVksV0FBVyxNQUFNLE9BQU8sT0FBTyxLQUFLLFlBQVksYUFBYSxPQUFPLEtBQUssWUFBWSxPQUFPLEtBQUssVUFBVSxZQUFZLE9BQU8sS0FBSyxVQUFVLE9BQU8sWUFBWSxNQUFNLFVBQVUsVUFBVSxZQUFZLGFBQWEsYUFBYSxPQUFPLEtBQUssVUFBVSxVQUFVLE9BQU8sS0FBSyxZQUFZLFdBQVcsWUFBWSxPQUFPLEtBQUssVUFBVSxVQUFVLFlBQVksT0FBTyxLQUFLLFVBQVUsWUFBWSxhQUFhLGFBQWEsV0FBVyxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsT0FBTyxLQUFLLFVBQVUsVUFBVSxVQUFVLE1BQU0sS0FBSyxVQUFVLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxPQUFPLE9BQU8sT0FBTyxZQUFZLE1BQU0sc0JBQXNCLHlCQUF5Qix1QkFBdUIsaUNBQWlDLG1DQUFtQyx1QkFBdUIsdUJBQXVCLHVEQUF1RCxPQUFPLEtBQUssc0JBQXNCLHlCQUF5Qix5QkFBeUIsbUNBQW1DLHlCQUF5Qix5QkFBeUIseURBQXlELCtDQUErQyxPQUFPLEtBQUssOEJBQThCLE9BQU8sS0FBSyxzQkFBc0IseUJBQXlCLE9BQU8sWUFBWSxNQUFNLEtBQUssVUFBVSxZQUFZLE1BQU0sS0FBSyxVQUFVLFlBQVksTUFBTSxNQUFNLEtBQUssc0JBQXNCLHVCQUF1QixPQUFPLEtBQUssS0FBSyxZQUFZLGFBQWEsTUFBTSxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sS0FBSyxvQkFBb0IscUJBQXFCLE9BQU8sS0FBSyxLQUFLLFFBQVEsT0FBTyxNQUFNLEtBQUssUUFBUSxPQUFPLE1BQU0sS0FBSyxRQUFRLE9BQU8sTUFBTSxLQUFLLFFBQVEsT0FBTyxNQUFNLHVEQUF1RCxjQUFjLGVBQWUsMkJBQTJCLEdBQUcsVUFBVSxnREFBZ0QsR0FBRyxVQUFVLHNCQUFzQixpQkFBaUIseVZBQXlWLGlDQUFpQyxnRUFBZ0UsbURBQW1ELEdBQUcsbUNBQW1DLGdCQUFnQixjQUFjLHlCQUF5QixrQkFBa0Isc0JBQXNCLGVBQWUsd0JBQXdCLG9CQUFvQiwwQkFBMEIscUJBQXFCLGlDQUFpQyxpQkFBaUIscUJBQXFCLGVBQWUsbUJBQW1CLGtCQUFrQixvQkFBb0IsWUFBWSxjQUFjLFlBQVksZUFBZSxxQkFBcUIscUJBQXFCLHlCQUF5Qix3QkFBd0IsZUFBZSxtQkFBbUIsYUFBYSxtQkFBbUIscUJBQXFCLFNBQVMsZ0JBQWdCLFNBQVMsa0JBQWtCLFVBQVUseUJBQXlCLFVBQVUsd0JBQXdCLFVBQVUseUJBQXlCLHVCQUF1QixxQkFBcUIsdUJBQXVCLHNCQUFzQix1QkFBdUIsc0JBQXNCLHVCQUF1Qix1QkFBdUIsY0FBYyxvQkFBb0Isc0JBQXNCLGFBQWEsaUJBQWlCLHVCQUF1QixhQUFhLG9CQUFvQixvQkFBb0IsYUFBYSxxQkFBcUIsd0JBQXdCLGVBQWUsbUJBQW1CLG1CQUFtQixtQkFBbUIsZ0JBQWdCLGVBQWUsZ0JBQWdCLGVBQWUsZ0JBQWdCLGVBQWUsZ0JBQWdCLGVBQWUsaUJBQWlCLHNCQUFzQixnQkFBZ0IseUJBQXlCLGVBQWUsc0ZBQXNGLG9CQUFvQiw2QkFBNkIsa0JBQWtCLDZCQUE2QixrQkFBa0IsNkJBQTZCLGtCQUFrQiw2QkFBNkIsNkJBQTZCLHlCQUF5QixrQkFBa0IscUNBQXFDLG9CQUFvQixnQ0FBZ0MsbUJBQW1CLDREQUE0RCxZQUFZLGVBQWUscUJBQXFCLG1CQUFtQixjQUFjLHFCQUFxQixjQUFjLHFCQUFxQixhQUFhLFFBQVEsVUFBVSxXQUFXLFVBQVUsY0FBYyx3QkFBd0IsWUFBWSxnQkFBZ0IsV0FBVyxpQkFBaUIsNERBQTRELGtCQUFrQixpQkFBaUIsbUJBQW1CLGdCQUFnQixHQUFHLDRDQUE0QywrQ0FBK0MsMENBQTBDLGdEQUFnRCxnSkFBZ0osd0JBQXdCLEdBQUcsa0JBQWtCLDBCQUEwQix3QkFBd0Isb0JBQW9CLHFCQUFxQix1QkFBdUIsa0RBQWtELCtDQUErQywwQkFBMEIsOEJBQThCLHVCQUF1Qix3QkFBd0Isa0JBQWtCLHdCQUF3QixxQkFBcUIsR0FBRyx1QkFBdUIsdUNBQXVDLCtDQUErQyw2Q0FBNkMsc0JBQXNCLG9HQUFvRyxHQUFHLHFCQUFxQiwwQ0FBMEMsK0NBQStDLGdEQUFnRCx1QkFBdUIsZ0dBQWdHLEdBQUcscUJBQXFCLDhEQUE4RCxzQkFBc0IsR0FBRyxrQkFBa0IsK0NBQStDLDBDQUEwQyxnREFBZ0QsMkJBQTJCLDRCQUE0QixpQkFBaUIsOEJBQThCLGdCQUFnQixpQkFBaUIscUJBQXFCLHFCQUFxQix3QkFBd0IseUJBQXlCLGdHQUFnRyxHQUFHLCtCQUErQixvQ0FBb0MsR0FBRyx3QkFBd0Isa0JBQWtCLGtEQUFrRCxHQUFHLFVBQVUsK0NBQStDLDBDQUEwQyxnREFBZ0QsMEJBQTBCLG9CQUFvQixpQkFBaUIsb0JBQW9CLDhCQUE4QixpQkFBaUIsZ0dBQWdHLEdBQUcsZ0JBQWdCLDJCQUEyQixvREFBb0QsR0FBRyxpQkFBaUIsMkJBQTJCLEdBQUcsbUJBQW1CLGlCQUFpQix3QkFBd0IsR0FBRyx5QkFBeUIsb0JBQW9CLEdBQUcsb0RBQW9ELGtCQUFrQixrQkFBa0IsMkJBQTJCLHFCQUFxQixxQkFBcUIsR0FBRyxrQkFBa0IsbUJBQW1CLG9CQUFvQixHQUFHLDBCQUEwQixnQ0FBZ0Msa0JBQWtCLDJCQUEyQixHQUFHLDRCQUE0QixpQkFBaUIsa0JBQWtCLDJCQUEyQixHQUFHLG9CQUFvQixZQUFZLGtDQUFrQyx1QkFBdUIsdUJBQXVCLGtCQUFrQixvQ0FBb0MsMEJBQTBCLDZCQUE2Qiw0QkFBNEIsaUNBQWlDLEdBQUcsdUNBQXVDLGFBQWEsY0FBYyxrQkFBa0IsR0FBRyxzQkFBc0IsbUJBQW1CLHFCQUFxQiwwQ0FBMEMsK0NBQStDLG9EQUFvRCxpR0FBaUcsR0FBRyw4REFBOEQsbUJBQW1CLG1CQUFtQixxQkFBcUIsb0JBQW9CLHVCQUF1QixrQkFBa0IsZ0JBQWdCLGlCQUFpQix1QkFBdUIsZUFBZSxxQkFBcUIsb0JBQW9CLFdBQVcsbUJBQW1CLFlBQVksd0JBQXdCLG1CQUFtQixnQkFBZ0IsaUJBQWlCLG9CQUFvQixtQkFBbUIsR0FBRywrQkFBK0IsbUJBQW1CLG9CQUFvQixxQkFBcUIscUJBQXFCLHVCQUF1QixtQkFBbUIsZ0JBQWdCLHFCQUFxQixvQkFBb0IseUJBQXlCLHNCQUFzQix5QkFBeUIscUJBQXFCLG1CQUFtQixnQkFBZ0IsbUJBQW1CLHFCQUFxQixtQkFBbUIsbUJBQW1CLGtCQUFrQixvQkFBb0IsMkJBQTJCLEdBQUcsK0JBQStCLG1CQUFtQixnQkFBZ0IsaUJBQWlCLEdBQUcsZ0NBQWdDLHFCQUFxQixrQkFBa0IsdUJBQXVCLHVCQUF1QixHQUFHLDZEQUE2RCxVQUFVLGlCQUFpQixpQ0FBaUMsS0FBSyxRQUFRLGlCQUFpQiwrQkFBK0IsS0FBSyxHQUFHLHFCQUFxQixXQUFXLDBCQUEwQixTQUFTLDRCQUE0QixHQUFHLHVCQUF1QixjQUFjLGtDQUFrQyw0REFBNEQsS0FBSyxTQUFTLCtCQUErQiw0REFBNEQsS0FBSyxHQUFHLHNCQUFzQixlQUFlLGFBQWEsVUFBVSxlQUFlLEdBQUcsMkJBQTJCLGNBQWMsbVdBQW1XLEtBQUssU0FBUyxtV0FBbVcsS0FBSyxTQUFTLG1XQUFtVyxLQUFLLFNBQVMsbVdBQW1XLEtBQUssR0FBRyxxQkFBcUI7QUFDbjV3QjtBQUNBLGlFQUFlLHVCQUF1QixFQUFDIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xcc3R5bGVzXFxnbG9iYWwuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEltcG9ydHNcbmltcG9ydCBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18gZnJvbSBcIi4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL3J1bnRpbWUvYXBpLmpzXCI7XG52YXIgX19fQ1NTX0xPQURFUl9FWFBPUlRfX18gPSBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18odHJ1ZSk7XG4vLyBNb2R1bGVcbl9fX0NTU19MT0FERVJfRVhQT1JUX19fLnB1c2goW21vZHVsZS5pZCwgXCIvKiBSZXNldCBlIGJhc2Ugc3R5bGVzICovXFxuKiB7XFxuICBtYXJnaW46IDA7XFxuICBwYWRkaW5nOiAwO1xcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcXG59XFxuXFxuaHRtbCB7XFxuICBmb250LWZhbWlseTogJ0ludGVyJywgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG5ib2R5IHtcXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xcbiAgY29sb3I6IHdoaXRlO1xcbiAgYmFja2dyb3VuZDpcXG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDIwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xcbiAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCUsIDEwMCUgMTAwJSwgMTAwJSAxMDAlLCAxMDAlIDEwMCU7XFxuICBhbmltYXRpb246IGxpcXVpZE1vdmUgMjBzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbn1cXG5cXG4vKiBVdGlsaXR5IENsYXNzZXMgKi9cXG4uZmxleCB7IGRpc3BsYXk6IGZsZXg7IH1cXG4uZmxleC1jb2wgeyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyB9XFxuLml0ZW1zLWNlbnRlciB7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IH1cXG4uaXRlbXMtZW5kIHsgYWxpZ24taXRlbXM6IGZsZXgtZW5kOyB9XFxuLmp1c3RpZnktY2VudGVyIHsganVzdGlmeS1jb250ZW50OiBjZW50ZXI7IH1cXG4uanVzdGlmeS1iZXR3ZWVuIHsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyB9XFxuLnRleHQtY2VudGVyIHsgdGV4dC1hbGlnbjogY2VudGVyOyB9XFxuLnRleHQtbGVmdCB7IHRleHQtYWxpZ246IGxlZnQ7IH1cXG4ubWluLWgtc2NyZWVuIHsgbWluLWhlaWdodDogMTAwdmg7IH1cXG4udy1mdWxsIHsgd2lkdGg6IDEwMCU7IH1cXG4uaC1mdWxsIHsgaGVpZ2h0OiAxMDAlOyB9XFxuLmxlYWRpbmctcmVsYXhlZCB7IGxpbmUtaGVpZ2h0OiAxLjYyNTsgfVxcbi53aGl0ZXNwYWNlLXByZS13cmFwIHsgd2hpdGUtc3BhY2U6IHByZS13cmFwOyB9XFxuLm1heC13LTR4bCB7IG1heC13aWR0aDogNTZyZW07IH1cXG4ubXgtYXV0byB7IG1hcmdpbi1sZWZ0OiBhdXRvOyBtYXJnaW4tcmlnaHQ6IGF1dG87IH1cXG4ucC00IHsgcGFkZGluZzogMXJlbTsgfVxcbi5wLTYgeyBwYWRkaW5nOiAxLjVyZW07IH1cXG4ubWItMiB7IG1hcmdpbi1ib3R0b206IDAuMjVyZW07IH1cXG4ubWItNCB7IG1hcmdpbi1ib3R0b206IDAuNXJlbTsgfVxcbi5tYi02IHsgbWFyZ2luLWJvdHRvbTogMC43NXJlbTsgfVxcbi5zcGFjZS15LTMgPiAqICsgKiB7IG1hcmdpbi10b3A6IDAuNXJlbTsgfVxcbi5zcGFjZS15LTQgPiAqICsgKiB7IG1hcmdpbi10b3A6IDAuNzVyZW07IH1cXG4uc3BhY2UteC0yID4gKiArICogeyBtYXJnaW4tbGVmdDogMC41cmVtOyB9XFxuLnNwYWNlLXgtMyA+ICogKyAqIHsgbWFyZ2luLWxlZnQ6IDAuNzVyZW07IH1cXG4udGV4dC0zeGwgeyBmb250LXNpemU6IDEuMjVyZW07IGxpbmUtaGVpZ2h0OiAxLjVyZW07IH1cXG4udGV4dC14bCB7IGZvbnQtc2l6ZTogMXJlbTsgbGluZS1oZWlnaHQ6IDEuMjVyZW07IH1cXG4udGV4dC1zbSB7IGZvbnQtc2l6ZTogMC43NXJlbTsgbGluZS1oZWlnaHQ6IDFyZW07IH1cXG4udGV4dC14cyB7IGZvbnQtc2l6ZTogMC42MjVyZW07IGxpbmUtaGVpZ2h0OiAwLjg3NXJlbTsgfVxcbi5mb250LWJvbGQgeyBmb250LXdlaWdodDogNzAwOyB9XFxuLmZvbnQtc2VtaWJvbGQgeyBmb250LXdlaWdodDogNjAwOyB9XFxuLnRleHQtd2hpdGUgeyBjb2xvcjogd2hpdGU7IH1cXG4ub3BhY2l0eS01MCB7IG9wYWNpdHk6IDAuNTsgfVxcbi5vcGFjaXR5LTYwIHsgb3BhY2l0eTogMC42OyB9XFxuLm9wYWNpdHktODAgeyBvcGFjaXR5OiAwLjg7IH1cXG4ucm91bmRlZC0yeGwgeyBib3JkZXItcmFkaXVzOiAxcmVtOyB9XFxuLnJvdW5kZWQteGwgeyBib3JkZXItcmFkaXVzOiAwLjc1cmVtOyB9XFxuLnNoYWRvdy1sZyB7IGJveC1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpOyB9XFxuLnRyYW5zaXRpb24tYWxsIHsgdHJhbnNpdGlvbjogYWxsIDAuMTVzIGVhc2U7IH1cXG4uZHVyYXRpb24tMjAwIHsgdHJhbnNpdGlvbi1kdXJhdGlvbjogMjAwbXM7IH1cXG4uZHVyYXRpb24tMzAwIHsgdHJhbnNpdGlvbi1kdXJhdGlvbjogMzAwbXM7IH1cXG4uZHVyYXRpb24tNTAwIHsgdHJhbnNpdGlvbi1kdXJhdGlvbjogNTAwbXM7IH1cXG4uaG92ZXJcXFxcOnNjYWxlLTEwNTpob3ZlciB7IHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7IH1cXG4uYW5pbWF0ZS1zcGluIHsgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTsgfVxcbi5hbmltYXRlLWJvdW5jZSB7IGFuaW1hdGlvbjogYm91bmNlIDFzIGluZmluaXRlOyB9XFxuLmFuaW1hdGUtcHVsc2UgeyBhbmltYXRpb246IHB1bHNlIDJzIGN1YmljLWJlemllcigwLjQsIDAsIDAuNiwgMSkgaW5maW5pdGU7IH1cXG4uZmxleC0xIHsgZmxleDogMSAxOyB9XFxuLm92ZXJmbG93LXktYXV0byB7IG92ZXJmbG93LXk6IGF1dG87IH1cXG4ucmVsYXRpdmUgeyBwb3NpdGlvbjogcmVsYXRpdmU7IH1cXG4uYWJzb2x1dGUgeyBwb3NpdGlvbjogYWJzb2x1dGU7IH1cXG4uaW5zZXQtMCB7IHRvcDogMDsgcmlnaHQ6IDA7IGJvdHRvbTogMDsgbGVmdDogMDsgfVxcbi5ib3JkZXItdCB7IGJvcmRlci10b3Atd2lkdGg6IDFweDsgfVxcbi5oaWRkZW4geyBkaXNwbGF5OiBub25lOyB9XFxuLmJsb2NrIHsgZGlzcGxheTogYmxvY2s7IH1cXG5cXG4vKiBSZXNwb25zaXZlIHZpc2liaWxpdHkgKi9cXG5AbWVkaWEgKG1pbi13aWR0aDogNjQwcHgpIHtcXG4gIC5zbVxcXFw6YmxvY2sgeyBkaXNwbGF5OiBibG9jazsgfVxcbiAgLnNtXFxcXDpoaWRkZW4geyBkaXNwbGF5OiBub25lOyB9XFxufVxcblxcbi8qIENvbXBvbmVudCBDbGFzc2VzICovXFxuLmdsYXNzLWVmZmVjdCB7XFxuICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gICAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICBib3gtc2hhZG93OlxcbiAgICAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xMiksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpLFxcbiAgICBpbnNldCAwIC0xcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xcbiAgYm9yZGVyLXJhZGl1czogMXJlbTtcXG59XFxuXFxuLmNoYXQtYnViYmxlIHtcXG4gIHBhZGRpbmc6IDFyZW0gMS4yNXJlbTtcXG4gIGJvcmRlci1yYWRpdXM6IDFyZW07XFxuICBtaW4td2lkdGg6IDhyZW07XFxuICBtYXgtd2lkdGg6IDI0cmVtO1xcbiAgd2lkdGg6IC1tb3otZml0LWNvbnRlbnQ7XFxuICB3aWR0aDogZml0LWNvbnRlbnQ7XFxuICBib3gtc2hhZG93OiAwIDRweCA2cHggLTFweCByZ2JhKDAsIDAsIDAsIDAuMSk7XFxuICBhbmltYXRpb246IHNsaWRlSW5Gcm9tQm90dG9tIDAuM3MgZWFzZS1vdXQ7XFxuICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7XFxuICBvdmVyZmxvdy13cmFwOiBicmVhay13b3JkO1xcbiAgZm9udC1zaXplOiAwLjk1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuNHJlbTtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG59XFxuXFxuLmNoYXQtYnViYmxlLXVzZXIge1xcbiAgYmFja2dyb3VuZDogcmdiYSgzNywgOTksIDIzNSwgMC42KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCkgc2F0dXJhdGUoMTgwJSk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDM3LCA5OSwgMjM1LCAwLjMpO1xcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XFxuICBib3gtc2hhZG93OlxcbiAgICAwIDRweCAxNnB4IHJnYmEoMzcsIDk5LCAyMzUsIDAuMiksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbn1cXG5cXG4uY2hhdC1idWJibGUtYWkge1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCkgc2F0dXJhdGUoMTgwJSk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpO1xcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xcbiAgYm94LXNoYWRvdzpcXG4gICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMSksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbn1cXG5cXG4uYXVyYS1hbmltYXRpb24ge1xcbiAgYW5pbWF0aW9uOiBwdWxzZSAzcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjYsIDEpIGluZmluaXRlO1xcbiAgZmlsdGVyOiBibHVyKDFweCk7XFxufVxcblxcbi5pbnB1dC1maWVsZCB7XFxuICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gICAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgcGFkZGluZzogMC41cmVtIDAuNzVyZW07XFxuICBjb2xvcjogd2hpdGU7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xcbiAgd2lkdGg6IDEwMCU7XFxuICByZXNpemU6IG5vbmU7XFxuICBtaW4taGVpZ2h0OiA0MHB4O1xcbiAgbWF4LWhlaWdodDogODBweDtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIGJveC1zaGFkb3c6XFxuICAgIDAgNHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEpLFxcbiAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG59XFxuXFxuLmlucHV0LWZpZWxkOjpwbGFjZWhvbGRlciB7XFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpO1xcbn1cXG5cXG4uaW5wdXQtZmllbGQ6Zm9jdXMge1xcbiAgb3V0bGluZTogbm9uZTtcXG4gIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC41KTtcXG59XFxuXFxuLmJ0biB7XFxuICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gICAgICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XFxuICBwYWRkaW5nOiAwLjVyZW07XFxuICBjb2xvcjogd2hpdGU7XFxuICBjdXJzb3I6IHBvaW50ZXI7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xcbiAgYm9yZGVyOiBub25lO1xcbiAgYm94LXNoYWRvdzpcXG4gICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMSksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbn1cXG5cXG4uYnRuOmhvdmVyIHtcXG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XFxuICBib3gtc2hhZG93OiAwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG59XFxuXFxuLmJ0bjphY3RpdmUge1xcbiAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcXG59XFxuXFxuLmJ0bjpkaXNhYmxlZCB7XFxuICBvcGFjaXR5OiAwLjU7XFxuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xcbn1cXG5cXG4uYnRuOmRpc2FibGVkOmhvdmVyIHtcXG4gIHRyYW5zZm9ybTogbm9uZTtcXG59XFxuXFxuLyogTGF5b3V0IHJlc3BvbnNpdmUgcGVyIGNoYXQgKi9cXG4uY2hhdC1sYXlvdXQge1xcbiAgaGVpZ2h0OiAxMDB2aDtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xcbiAgbWF4LXdpZHRoOiAxMDB2dztcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxufVxcblxcbi5jaGF0LWhlYWRlciB7XFxuICBmbGV4LXNocmluazogMDtcXG4gIHBhZGRpbmc6IDAuNXJlbTtcXG59XFxuXFxuLmNoYXQtbWFpbi1jb250YWluZXIge1xcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTIwcHgpO1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XFxufVxcblxcbi5nbGFzcy1mcmFtZS1jb250YWluZXIge1xcbiAgaGVpZ2h0OiAxMDAlO1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XFxufVxcblxcbi5jaGF0LW1lc3NhZ2VzIHtcXG4gIGZsZXg6IDEgMTtcXG4gIG92ZXJmbG93LXk6IHNjcm9sbCAhaW1wb3J0YW50O1xcbiAgb3ZlcmZsb3cteDogaGlkZGVuO1xcbiAgcGFkZGluZzogMCAwLjc1cmVtO1xcbiAgbWluLWhlaWdodDogMDtcXG4gIG1heC1oZWlnaHQ6IGNhbGMoMTAwdmggLSAyMDBweCk7XFxuICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7XFxuICAtbXMtb3ZlcmZsb3ctc3R5bGU6IG5vbmU7XFxuICBzY3JvbGwtYmVoYXZpb3I6IHNtb290aDtcXG4gIG92ZXJzY3JvbGwtYmVoYXZpb3I6IGNvbnRhaW47XFxufVxcblxcbi5jaGF0LW1lc3NhZ2VzOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICB3aWR0aDogMDtcXG4gIGhlaWdodDogMDtcXG4gIGRpc3BsYXk6IG5vbmU7XFxufVxcblxcbi5jaGF0LWlucHV0LWFyZWEge1xcbiAgZmxleC1zaHJpbms6IDA7XFxuICBwYWRkaW5nOiAwLjc1cmVtO1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCkgc2F0dXJhdGUoMTgwJSk7XFxuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gIGJveC1zaGFkb3c6XFxuICAgIDAgLTRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xKSxcXG4gICAgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxufVxcblxcbi8qIE1vYmlsZSBGaXJzdCBSZXNwb25zaXZlICovXFxuQG1lZGlhIChtYXgtd2lkdGg6IDY0MHB4KSB7XFxuICAuY2hhdC1oZWFkZXIgeyBwYWRkaW5nOiAwLjI1cmVtOyB9XFxuICAuY2hhdC1tZXNzYWdlcyB7IHBhZGRpbmc6IDAgMC41cmVtOyB9XFxuICAuY2hhdC1pbnB1dC1hcmVhIHsgcGFkZGluZzogMC41cmVtOyB9XFxuICAudGV4dC0zeGwgeyBmb250LXNpemU6IDFyZW07IGxpbmUtaGVpZ2h0OiAxLjI1cmVtOyB9XFxuICAudGV4dC14bCB7IGZvbnQtc2l6ZTogMC44NzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuICAucC02IHsgcGFkZGluZzogMC43NXJlbTsgfVxcbiAgLm1iLTYgeyBtYXJnaW4tYm90dG9tOiAwLjVyZW07IH1cXG4gIC5jaGF0LWJ1YmJsZSB7IG1heC13aWR0aDogNzUlOyBwYWRkaW5nOiAwLjVyZW07IGZvbnQtc2l6ZTogMC43NXJlbTsgbGluZS1oZWlnaHQ6IDEuMjsgfVxcbn1cXG5cXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcXG4gIC5jaGF0LWhlYWRlciB7IHBhZGRpbmc6IDAuMTI1cmVtOyB9XFxuICAuY2hhdC1tZXNzYWdlcyB7IHBhZGRpbmc6IDAgMC4yNXJlbTsgfVxcbiAgLmNoYXQtaW5wdXQtYXJlYSB7IHBhZGRpbmc6IDAuMjVyZW07IH1cXG4gIC50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMC44NzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuICAuc3BhY2UteS0zID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjI1cmVtOyB9XFxuICAuc3BhY2UteS00ID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cXG4gIC5jaGF0LWJ1YmJsZSB7IG1heC13aWR0aDogODAlOyBwYWRkaW5nOiAwLjM3NXJlbTsgZm9udC1zaXplOiAwLjYyNXJlbTsgbGluZS1oZWlnaHQ6IDEuMTsgfVxcbiAgLmlucHV0LWZpZWxkIHsgbWluLWhlaWdodDogMzJweDsgZm9udC1zaXplOiAwLjc1cmVtOyBwYWRkaW5nOiAwLjM3NXJlbSAwLjVyZW07IH1cXG59XFxuXFxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XFxuICAuY2hhdC1sYXlvdXQgeyBtYXgtd2lkdGg6IDR4bDsgbWFyZ2luOiAwIGF1dG87IH1cXG59XFxuXFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcbiAgLmNoYXQtbWVzc2FnZXMgeyBwYWRkaW5nOiAwIDJyZW07IH1cXG4gIC5jaGF0LWlucHV0LWFyZWEgeyBwYWRkaW5nOiAxLjVyZW0gMnJlbTsgfVxcbn1cXG5cXG4vKiBLZXlmcmFtZSBBbmltYXRpb25zICovXFxuQGtleWZyYW1lcyBzbGlkZUluRnJvbUJvdHRvbSB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDhweCk7XFxuICB9XFxuICB0byB7XFxuICAgIG9wYWNpdHk6IDE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBzcGluIHtcXG4gIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxcbiAgdG8geyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XFxufVxcblxcbkBrZXlmcmFtZXMgYm91bmNlIHtcXG4gIDAlLCAxMDAlIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0yNSUpO1xcbiAgICBhbmltYXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC44LCAwLCAxLCAxKTtcXG4gIH1cXG4gIDUwJSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXG4gICAgYW5pbWF0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAsIDAsIDAuMiwgMSk7XFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgcHVsc2Uge1xcbiAgMCUsIDEwMCUgeyBvcGFjaXR5OiAxOyB9XFxuICA1MCUgeyBvcGFjaXR5OiAwLjU7IH1cXG59XFxuXFxuQGtleWZyYW1lcyBsaXF1aWRNb3ZlIHtcXG4gIDAlLCAxMDAlIHtcXG4gICAgYmFja2dyb3VuZDpcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA4MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDgwJSAyMCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIH1cXG4gIDI1JSB7XFxuICAgIGJhY2tncm91bmQ6XFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA2MCUgMzAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAzMCUgNzAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA3MCUgNjAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICB9XFxuICA1MCUge1xcbiAgICBiYWNrZ3JvdW5kOlxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDYwJSwgcmdiYSgxMjAsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMjAlIDQwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNjAlIDgwJSwgcmdiYSgxMjAsIDIxOSwgMjU1LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpO1xcbiAgfVxcbiAgNzUlIHtcXG4gICAgYmFja2dyb3VuZDpcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA3MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC40KSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDcwJSAzMCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC40KSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDMwJSAyMCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC40KSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIH1cXG59XFxuXCIsIFwiXCIse1widmVyc2lvblwiOjMsXCJzb3VyY2VzXCI6W1wid2VicGFjazovL3NyYy9zdHlsZXMvZ2xvYmFsLmNzc1wiXSxcIm5hbWVzXCI6W10sXCJtYXBwaW5nc1wiOlwiQUFBQSx3QkFBd0I7QUFDeEI7RUFDRSxTQUFTO0VBQ1QsVUFBVTtFQUNWLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixZQUFZO0VBQ1o7Ozs7a0VBSWdFO0VBQ2hFLDRCQUE0QjtFQUM1QiwyREFBMkQ7RUFDM0QsOENBQThDO0FBQ2hEOztBQUVBLG9CQUFvQjtBQUNwQixRQUFRLGFBQWEsRUFBRTtBQUN2QixZQUFZLHNCQUFzQixFQUFFO0FBQ3BDLGdCQUFnQixtQkFBbUIsRUFBRTtBQUNyQyxhQUFhLHFCQUFxQixFQUFFO0FBQ3BDLGtCQUFrQix1QkFBdUIsRUFBRTtBQUMzQyxtQkFBbUIsOEJBQThCLEVBQUU7QUFDbkQsZUFBZSxrQkFBa0IsRUFBRTtBQUNuQyxhQUFhLGdCQUFnQixFQUFFO0FBQy9CLGdCQUFnQixpQkFBaUIsRUFBRTtBQUNuQyxVQUFVLFdBQVcsRUFBRTtBQUN2QixVQUFVLFlBQVksRUFBRTtBQUN4QixtQkFBbUIsa0JBQWtCLEVBQUU7QUFDdkMsdUJBQXVCLHFCQUFxQixFQUFFO0FBQzlDLGFBQWEsZ0JBQWdCLEVBQUU7QUFDL0IsV0FBVyxpQkFBaUIsRUFBRSxrQkFBa0IsRUFBRTtBQUNsRCxPQUFPLGFBQWEsRUFBRTtBQUN0QixPQUFPLGVBQWUsRUFBRTtBQUN4QixRQUFRLHNCQUFzQixFQUFFO0FBQ2hDLFFBQVEscUJBQXFCLEVBQUU7QUFDL0IsUUFBUSxzQkFBc0IsRUFBRTtBQUNoQyxxQkFBcUIsa0JBQWtCLEVBQUU7QUFDekMscUJBQXFCLG1CQUFtQixFQUFFO0FBQzFDLHFCQUFxQixtQkFBbUIsRUFBRTtBQUMxQyxxQkFBcUIsb0JBQW9CLEVBQUU7QUFDM0MsWUFBWSxrQkFBa0IsRUFBRSxtQkFBbUIsRUFBRTtBQUNyRCxXQUFXLGVBQWUsRUFBRSxvQkFBb0IsRUFBRTtBQUNsRCxXQUFXLGtCQUFrQixFQUFFLGlCQUFpQixFQUFFO0FBQ2xELFdBQVcsbUJBQW1CLEVBQUUscUJBQXFCLEVBQUU7QUFDdkQsYUFBYSxnQkFBZ0IsRUFBRTtBQUMvQixpQkFBaUIsZ0JBQWdCLEVBQUU7QUFDbkMsY0FBYyxZQUFZLEVBQUU7QUFDNUIsY0FBYyxZQUFZLEVBQUU7QUFDNUIsY0FBYyxZQUFZLEVBQUU7QUFDNUIsY0FBYyxZQUFZLEVBQUU7QUFDNUIsZUFBZSxtQkFBbUIsRUFBRTtBQUNwQyxjQUFjLHNCQUFzQixFQUFFO0FBQ3RDLGFBQWEsbUZBQW1GLEVBQUU7QUFDbEcsa0JBQWtCLDBCQUEwQixFQUFFO0FBQzlDLGdCQUFnQiwwQkFBMEIsRUFBRTtBQUM1QyxnQkFBZ0IsMEJBQTBCLEVBQUU7QUFDNUMsZ0JBQWdCLDBCQUEwQixFQUFFO0FBQzVDLDBCQUEwQixzQkFBc0IsRUFBRTtBQUNsRCxnQkFBZ0Isa0NBQWtDLEVBQUU7QUFDcEQsa0JBQWtCLDZCQUE2QixFQUFFO0FBQ2pELGlCQUFpQix5REFBeUQsRUFBRTtBQUM1RSxVQUFVLFNBQVksRUFBRTtBQUN4QixtQkFBbUIsZ0JBQWdCLEVBQUU7QUFDckMsWUFBWSxrQkFBa0IsRUFBRTtBQUNoQyxZQUFZLGtCQUFrQixFQUFFO0FBQ2hDLFdBQVcsTUFBTSxFQUFFLFFBQVEsRUFBRSxTQUFTLEVBQUUsT0FBTyxFQUFFO0FBQ2pELFlBQVkscUJBQXFCLEVBQUU7QUFDbkMsVUFBVSxhQUFhLEVBQUU7QUFDekIsU0FBUyxjQUFjLEVBQUU7O0FBRXpCLDBCQUEwQjtBQUMxQjtFQUNFLGFBQWEsY0FBYyxFQUFFO0VBQzdCLGNBQWMsYUFBYSxFQUFFO0FBQy9COztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFLGtEQUEwQztVQUExQywwQ0FBMEM7RUFDMUMscUNBQXFDO0VBQ3JDLDJDQUEyQztFQUMzQzs7OzRDQUcwQztFQUMxQyxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxxQkFBcUI7RUFDckIsbUJBQW1CO0VBQ25CLGVBQWU7RUFDZixnQkFBZ0I7RUFDaEIsdUJBQWtCO0VBQWxCLGtCQUFrQjtFQUNsQiw2Q0FBNkM7RUFDN0MsMENBQTBDO0VBQzFDLHFCQUFxQjtFQUNyQix5QkFBeUI7RUFDekIsa0JBQWtCO0VBQ2xCLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGtDQUFrQztFQUNsQyxrREFBMEM7VUFBMUMsMENBQTBDO0VBQzFDLHdDQUF3QztFQUN4QyxpQkFBaUI7RUFDakI7OzBDQUV3QztBQUMxQzs7QUFFQTtFQUNFLHFDQUFxQztFQUNyQyxrREFBMEM7VUFBMUMsMENBQTBDO0VBQzFDLDJDQUEyQztFQUMzQyxrQkFBa0I7RUFDbEI7OzBDQUV3QztBQUMxQzs7QUFFQTtFQUNFLHlEQUF5RDtFQUN6RCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxrREFBMEM7VUFBMUMsMENBQTBDO0VBQzFDLHFDQUFxQztFQUNyQywyQ0FBMkM7RUFDM0Msc0JBQXNCO0VBQ3RCLHVCQUF1QjtFQUN2QixZQUFZO0VBQ1oseUJBQXlCO0VBQ3pCLFdBQVc7RUFDWCxZQUFZO0VBQ1osZ0JBQWdCO0VBQ2hCLGdCQUFnQjtFQUNoQixtQkFBbUI7RUFDbkIsb0JBQW9CO0VBQ3BCOzswQ0FFd0M7QUFDMUM7O0FBRUE7RUFDRSwrQkFBK0I7QUFDakM7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsNkNBQTZDO0FBQy9DOztBQUVBO0VBQ0Usa0RBQTBDO1VBQTFDLDBDQUEwQztFQUMxQyxxQ0FBcUM7RUFDckMsMkNBQTJDO0VBQzNDLHFCQUFxQjtFQUNyQixlQUFlO0VBQ2YsWUFBWTtFQUNaLGVBQWU7RUFDZix5QkFBeUI7RUFDekIsWUFBWTtFQUNaOzswQ0FFd0M7QUFDMUM7O0FBRUE7RUFDRSxzQkFBc0I7RUFDdEIsK0NBQStDO0FBQ2pEOztBQUVBO0VBQ0Usc0JBQXNCO0FBQ3hCOztBQUVBO0VBQ0UsWUFBWTtFQUNaLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGVBQWU7QUFDakI7O0FBRUEsK0JBQStCO0FBQy9CO0VBQ0UsYUFBYTtFQUNiLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsZ0JBQWdCO0VBQ2hCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLGFBQWE7RUFDYixzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxZQUFZO0VBQ1osYUFBYTtFQUNiLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLFNBQU87RUFDUCw2QkFBNkI7RUFDN0Isa0JBQWtCO0VBQ2xCLGtCQUFrQjtFQUNsQixhQUFhO0VBQ2IsK0JBQStCO0VBQy9CLHFCQUFxQjtFQUNyQix3QkFBd0I7RUFDeEIsdUJBQXVCO0VBQ3ZCLDRCQUE0QjtBQUM5Qjs7QUFFQTtFQUNFLFFBQVE7RUFDUixTQUFTO0VBQ1QsYUFBYTtBQUNmOztBQUVBO0VBQ0UsY0FBYztFQUNkLGdCQUFnQjtFQUNoQixxQ0FBcUM7RUFDckMsa0RBQTBDO1VBQTFDLDBDQUEwQztFQUMxQywrQ0FBK0M7RUFDL0M7OzBDQUV3QztBQUMxQzs7QUFFQSw0QkFBNEI7QUFDNUI7RUFDRSxlQUFlLGdCQUFnQixFQUFFO0VBQ2pDLGlCQUFpQixpQkFBaUIsRUFBRTtFQUNwQyxtQkFBbUIsZUFBZSxFQUFFO0VBQ3BDLFlBQVksZUFBZSxFQUFFLG9CQUFvQixFQUFFO0VBQ25ELFdBQVcsbUJBQW1CLEVBQUUsaUJBQWlCLEVBQUU7RUFDbkQsT0FBTyxnQkFBZ0IsRUFBRTtFQUN6QixRQUFRLHFCQUFxQixFQUFFO0VBQy9CLGVBQWUsY0FBYyxFQUFFLGVBQWUsRUFBRSxrQkFBa0IsRUFBRSxnQkFBZ0IsRUFBRTtBQUN4Rjs7QUFFQTtFQUNFLGVBQWUsaUJBQWlCLEVBQUU7RUFDbEMsaUJBQWlCLGtCQUFrQixFQUFFO0VBQ3JDLG1CQUFtQixnQkFBZ0IsRUFBRTtFQUNyQyxZQUFZLG1CQUFtQixFQUFFLGlCQUFpQixFQUFFO0VBQ3BELHFCQUFxQixtQkFBbUIsRUFBRTtFQUMxQyxxQkFBcUIsa0JBQWtCLEVBQUU7RUFDekMsZUFBZSxjQUFjLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLEVBQUUsZ0JBQWdCLEVBQUU7RUFDekYsZUFBZSxnQkFBZ0IsRUFBRSxrQkFBa0IsRUFBRSx3QkFBd0IsRUFBRTtBQUNqRjs7QUFFQTtFQUNFLGVBQWUsY0FBYyxFQUFFLGNBQWMsRUFBRTtBQUNqRDs7QUFFQTtFQUNFLGlCQUFpQixlQUFlLEVBQUU7RUFDbEMsbUJBQW1CLG9CQUFvQixFQUFFO0FBQzNDOztBQUVBLHdCQUF3QjtBQUN4QjtFQUNFO0lBQ0UsVUFBVTtJQUNWLDBCQUEwQjtFQUM1QjtFQUNBO0lBQ0UsVUFBVTtJQUNWLHdCQUF3QjtFQUMxQjtBQUNGOztBQUVBO0VBQ0UsT0FBTyx1QkFBdUIsRUFBRTtFQUNoQyxLQUFLLHlCQUF5QixFQUFFO0FBQ2xDOztBQUVBO0VBQ0U7SUFDRSwyQkFBMkI7SUFDM0IscURBQXFEO0VBQ3ZEO0VBQ0E7SUFDRSx3QkFBd0I7SUFDeEIscURBQXFEO0VBQ3ZEO0FBQ0Y7O0FBRUE7RUFDRSxXQUFXLFVBQVUsRUFBRTtFQUN2QixNQUFNLFlBQVksRUFBRTtBQUN0Qjs7QUFFQTtFQUNFO0lBQ0U7Ozs7b0VBSWdFO0VBQ2xFO0VBQ0E7SUFDRTs7OztvRUFJZ0U7RUFDbEU7RUFDQTtJQUNFOzs7O29FQUlnRTtFQUNsRTtFQUNBO0lBQ0U7Ozs7b0VBSWdFO0VBQ2xFO0FBQ0ZcIixcInNvdXJjZXNDb250ZW50XCI6W1wiLyogUmVzZXQgZSBiYXNlIHN0eWxlcyAqL1xcbioge1xcbiAgbWFyZ2luOiAwO1xcbiAgcGFkZGluZzogMDtcXG4gIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XFxufVxcblxcbmh0bWwge1xcbiAgZm9udC1mYW1pbHk6ICdJbnRlcicsIHN5c3RlbS11aSwgc2Fucy1zZXJpZjtcXG59XFxuXFxuYm9keSB7XFxuICBtaW4taGVpZ2h0OiAxMDB2aDtcXG4gIGNvbG9yOiB3aGl0ZTtcXG4gIGJhY2tncm91bmQ6XFxuICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMjAlIDgwJSwgcmdiYSgxMjAsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDgwJSAyMCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNDAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgIGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpO1xcbiAgYmFja2dyb3VuZC1hdHRhY2htZW50OiBmaXhlZDtcXG4gIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlLCAxMDAlIDEwMCUsIDEwMCUgMTAwJSwgMTAwJSAxMDAlO1xcbiAgYW5pbWF0aW9uOiBsaXF1aWRNb3ZlIDIwcyBlYXNlLWluLW91dCBpbmZpbml0ZTtcXG59XFxuXFxuLyogVXRpbGl0eSBDbGFzc2VzICovXFxuLmZsZXggeyBkaXNwbGF5OiBmbGV4OyB9XFxuLmZsZXgtY29sIHsgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsgfVxcbi5pdGVtcy1jZW50ZXIgeyBhbGlnbi1pdGVtczogY2VudGVyOyB9XFxuLml0ZW1zLWVuZCB7IGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsgfVxcbi5qdXN0aWZ5LWNlbnRlciB7IGp1c3RpZnktY29udGVudDogY2VudGVyOyB9XFxuLmp1c3RpZnktYmV0d2VlbiB7IGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsgfVxcbi50ZXh0LWNlbnRlciB7IHRleHQtYWxpZ246IGNlbnRlcjsgfVxcbi50ZXh0LWxlZnQgeyB0ZXh0LWFsaWduOiBsZWZ0OyB9XFxuLm1pbi1oLXNjcmVlbiB7IG1pbi1oZWlnaHQ6IDEwMHZoOyB9XFxuLnctZnVsbCB7IHdpZHRoOiAxMDAlOyB9XFxuLmgtZnVsbCB7IGhlaWdodDogMTAwJTsgfVxcbi5sZWFkaW5nLXJlbGF4ZWQgeyBsaW5lLWhlaWdodDogMS42MjU7IH1cXG4ud2hpdGVzcGFjZS1wcmUtd3JhcCB7IHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsgfVxcbi5tYXgtdy00eGwgeyBtYXgtd2lkdGg6IDU2cmVtOyB9XFxuLm14LWF1dG8geyBtYXJnaW4tbGVmdDogYXV0bzsgbWFyZ2luLXJpZ2h0OiBhdXRvOyB9XFxuLnAtNCB7IHBhZGRpbmc6IDFyZW07IH1cXG4ucC02IHsgcGFkZGluZzogMS41cmVtOyB9XFxuLm1iLTIgeyBtYXJnaW4tYm90dG9tOiAwLjI1cmVtOyB9XFxuLm1iLTQgeyBtYXJnaW4tYm90dG9tOiAwLjVyZW07IH1cXG4ubWItNiB7IG1hcmdpbi1ib3R0b206IDAuNzVyZW07IH1cXG4uc3BhY2UteS0zID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cXG4uc3BhY2UteS00ID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjc1cmVtOyB9XFxuLnNwYWNlLXgtMiA+ICogKyAqIHsgbWFyZ2luLWxlZnQ6IDAuNXJlbTsgfVxcbi5zcGFjZS14LTMgPiAqICsgKiB7IG1hcmdpbi1sZWZ0OiAwLjc1cmVtOyB9XFxuLnRleHQtM3hsIHsgZm9udC1zaXplOiAxLjI1cmVtOyBsaW5lLWhlaWdodDogMS41cmVtOyB9XFxuLnRleHQteGwgeyBmb250LXNpemU6IDFyZW07IGxpbmUtaGVpZ2h0OiAxLjI1cmVtOyB9XFxuLnRleHQtc20geyBmb250LXNpemU6IDAuNzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuLnRleHQteHMgeyBmb250LXNpemU6IDAuNjI1cmVtOyBsaW5lLWhlaWdodDogMC44NzVyZW07IH1cXG4uZm9udC1ib2xkIHsgZm9udC13ZWlnaHQ6IDcwMDsgfVxcbi5mb250LXNlbWlib2xkIHsgZm9udC13ZWlnaHQ6IDYwMDsgfVxcbi50ZXh0LXdoaXRlIHsgY29sb3I6IHdoaXRlOyB9XFxuLm9wYWNpdHktNTAgeyBvcGFjaXR5OiAwLjU7IH1cXG4ub3BhY2l0eS02MCB7IG9wYWNpdHk6IDAuNjsgfVxcbi5vcGFjaXR5LTgwIHsgb3BhY2l0eTogMC44OyB9XFxuLnJvdW5kZWQtMnhsIHsgYm9yZGVyLXJhZGl1czogMXJlbTsgfVxcbi5yb3VuZGVkLXhsIHsgYm9yZGVyLXJhZGl1czogMC43NXJlbTsgfVxcbi5zaGFkb3ctbGcgeyBib3gtc2hhZG93OiAwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCA0cHggNnB4IC0ycHggcmdiYSgwLCAwLCAwLCAwLjA1KTsgfVxcbi50cmFuc2l0aW9uLWFsbCB7IHRyYW5zaXRpb246IGFsbCAwLjE1cyBlYXNlOyB9XFxuLmR1cmF0aW9uLTIwMCB7IHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zOyB9XFxuLmR1cmF0aW9uLTMwMCB7IHRyYW5zaXRpb24tZHVyYXRpb246IDMwMG1zOyB9XFxuLmR1cmF0aW9uLTUwMCB7IHRyYW5zaXRpb24tZHVyYXRpb246IDUwMG1zOyB9XFxuLmhvdmVyXFxcXDpzY2FsZS0xMDU6aG92ZXIgeyB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOyB9XFxuLmFuaW1hdGUtc3BpbiB7IGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7IH1cXG4uYW5pbWF0ZS1ib3VuY2UgeyBhbmltYXRpb246IGJvdW5jZSAxcyBpbmZpbml0ZTsgfVxcbi5hbmltYXRlLXB1bHNlIHsgYW5pbWF0aW9uOiBwdWxzZSAycyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjYsIDEpIGluZmluaXRlOyB9XFxuLmZsZXgtMSB7IGZsZXg6IDEgMSAwJTsgfVxcbi5vdmVyZmxvdy15LWF1dG8geyBvdmVyZmxvdy15OiBhdXRvOyB9XFxuLnJlbGF0aXZlIHsgcG9zaXRpb246IHJlbGF0aXZlOyB9XFxuLmFic29sdXRlIHsgcG9zaXRpb246IGFic29sdXRlOyB9XFxuLmluc2V0LTAgeyB0b3A6IDA7IHJpZ2h0OiAwOyBib3R0b206IDA7IGxlZnQ6IDA7IH1cXG4uYm9yZGVyLXQgeyBib3JkZXItdG9wLXdpZHRoOiAxcHg7IH1cXG4uaGlkZGVuIHsgZGlzcGxheTogbm9uZTsgfVxcbi5ibG9jayB7IGRpc3BsYXk6IGJsb2NrOyB9XFxuXFxuLyogUmVzcG9uc2l2ZSB2aXNpYmlsaXR5ICovXFxuQG1lZGlhIChtaW4td2lkdGg6IDY0MHB4KSB7XFxuICAuc21cXFxcOmJsb2NrIHsgZGlzcGxheTogYmxvY2s7IH1cXG4gIC5zbVxcXFw6aGlkZGVuIHsgZGlzcGxheTogbm9uZTsgfVxcbn1cXG5cXG4vKiBDb21wb25lbnQgQ2xhc3NlcyAqL1xcbi5nbGFzcy1lZmZlY3Qge1xcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICBib3gtc2hhZG93OlxcbiAgICAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xMiksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpLFxcbiAgICBpbnNldCAwIC0xcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xcbiAgYm9yZGVyLXJhZGl1czogMXJlbTtcXG59XFxuXFxuLmNoYXQtYnViYmxlIHtcXG4gIHBhZGRpbmc6IDFyZW0gMS4yNXJlbTtcXG4gIGJvcmRlci1yYWRpdXM6IDFyZW07XFxuICBtaW4td2lkdGg6IDhyZW07XFxuICBtYXgtd2lkdGg6IDI0cmVtO1xcbiAgd2lkdGg6IGZpdC1jb250ZW50O1xcbiAgYm94LXNoYWRvdzogMCA0cHggNnB4IC0xcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xcbiAgYW5pbWF0aW9uOiBzbGlkZUluRnJvbUJvdHRvbSAwLjNzIGVhc2Utb3V0O1xcbiAgd29yZC13cmFwOiBicmVhay13b3JkO1xcbiAgb3ZlcmZsb3ctd3JhcDogYnJlYWstd29yZDtcXG4gIGZvbnQtc2l6ZTogMC45NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjRyZW07XFxuICBkaXNwbGF5OiBmbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIHRleHQtYWxpZ246IGxlZnQ7XFxufVxcblxcbi5jaGF0LWJ1YmJsZS11c2VyIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMzcsIDk5LCAyMzUsIDAuNik7XFxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCkgc2F0dXJhdGUoMTgwJSk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDM3LCA5OSwgMjM1LCAwLjMpO1xcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XFxuICBib3gtc2hhZG93OlxcbiAgICAwIDRweCAxNnB4IHJnYmEoMzcsIDk5LCAyMzUsIDAuMiksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcbn1cXG5cXG4uY2hhdC1idWJibGUtYWkge1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICBtYXJnaW4tcmlnaHQ6IGF1dG87XFxuICBib3gtc2hhZG93OlxcbiAgICAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xKSxcXG4gICAgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XFxufVxcblxcbi5hdXJhLWFuaW1hdGlvbiB7XFxuICBhbmltYXRpb246IHB1bHNlIDNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuNiwgMSkgaW5maW5pdGU7XFxuICBmaWx0ZXI6IGJsdXIoMXB4KTtcXG59XFxuXFxuLmlucHV0LWZpZWxkIHtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpO1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIHBhZGRpbmc6IDAuNXJlbSAwLjc1cmVtO1xcbiAgY29sb3I6IHdoaXRlO1xcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcXG4gIHdpZHRoOiAxMDAlO1xcbiAgcmVzaXplOiBub25lO1xcbiAgbWluLWhlaWdodDogNDBweDtcXG4gIG1heC1oZWlnaHQ6IDgwcHg7XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICBib3gtc2hhZG93OlxcbiAgICAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xKSxcXG4gICAgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxufVxcblxcbi5pbnB1dC1maWVsZDo6cGxhY2Vob2xkZXIge1xcbiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC42KTtcXG59XFxuXFxuLmlucHV0LWZpZWxkOmZvY3VzIHtcXG4gIG91dGxpbmU6IG5vbmU7XFxuICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuNSk7XFxufVxcblxcbi5idG4ge1xcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpIHNhdHVyYXRlKDE4MCUpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA4KTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XFxuICBwYWRkaW5nOiAwLjVyZW07XFxuICBjb2xvcjogd2hpdGU7XFxuICBjdXJzb3I6IHBvaW50ZXI7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xcbiAgYm9yZGVyOiBub25lO1xcbiAgYm94LXNoYWRvdzpcXG4gICAgMCA0cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMSksXFxuICAgIGluc2V0IDAgMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbn1cXG5cXG4uYnRuOmhvdmVyIHtcXG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XFxuICBib3gtc2hhZG93OiAwIDEwcHggMTVweCAtM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG59XFxuXFxuLmJ0bjphY3RpdmUge1xcbiAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcXG59XFxuXFxuLmJ0bjpkaXNhYmxlZCB7XFxuICBvcGFjaXR5OiAwLjU7XFxuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xcbn1cXG5cXG4uYnRuOmRpc2FibGVkOmhvdmVyIHtcXG4gIHRyYW5zZm9ybTogbm9uZTtcXG59XFxuXFxuLyogTGF5b3V0IHJlc3BvbnNpdmUgcGVyIGNoYXQgKi9cXG4uY2hhdC1sYXlvdXQge1xcbiAgaGVpZ2h0OiAxMDB2aDtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xcbiAgbWF4LXdpZHRoOiAxMDB2dztcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxufVxcblxcbi5jaGF0LWhlYWRlciB7XFxuICBmbGV4LXNocmluazogMDtcXG4gIHBhZGRpbmc6IDAuNXJlbTtcXG59XFxuXFxuLmNoYXQtbWFpbi1jb250YWluZXIge1xcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTIwcHgpO1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XFxufVxcblxcbi5nbGFzcy1mcmFtZS1jb250YWluZXIge1xcbiAgaGVpZ2h0OiAxMDAlO1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XFxufVxcblxcbi5jaGF0LW1lc3NhZ2VzIHtcXG4gIGZsZXg6IDE7XFxuICBvdmVyZmxvdy15OiBzY3JvbGwgIWltcG9ydGFudDtcXG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcXG4gIHBhZGRpbmc6IDAgMC43NXJlbTtcXG4gIG1pbi1oZWlnaHQ6IDA7XFxuICBtYXgtaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjAwcHgpO1xcbiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xcbiAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xcbiAgc2Nyb2xsLWJlaGF2aW9yOiBzbW9vdGg7XFxuICBvdmVyc2Nyb2xsLWJlaGF2aW9yOiBjb250YWluO1xcbn1cXG5cXG4uY2hhdC1tZXNzYWdlczo6LXdlYmtpdC1zY3JvbGxiYXIge1xcbiAgd2lkdGg6IDA7XFxuICBoZWlnaHQ6IDA7XFxuICBkaXNwbGF5OiBub25lO1xcbn1cXG5cXG4uY2hhdC1pbnB1dC1hcmVhIHtcXG4gIGZsZXgtc2hyaW5rOiAwO1xcbiAgcGFkZGluZzogMC43NXJlbTtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XFxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCkgc2F0dXJhdGUoMTgwJSk7XFxuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcXG4gIGJveC1zaGFkb3c6XFxuICAgIDAgLTRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xKSxcXG4gICAgaW5zZXQgMCAxcHggMCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxufVxcblxcbi8qIE1vYmlsZSBGaXJzdCBSZXNwb25zaXZlICovXFxuQG1lZGlhIChtYXgtd2lkdGg6IDY0MHB4KSB7XFxuICAuY2hhdC1oZWFkZXIgeyBwYWRkaW5nOiAwLjI1cmVtOyB9XFxuICAuY2hhdC1tZXNzYWdlcyB7IHBhZGRpbmc6IDAgMC41cmVtOyB9XFxuICAuY2hhdC1pbnB1dC1hcmVhIHsgcGFkZGluZzogMC41cmVtOyB9XFxuICAudGV4dC0zeGwgeyBmb250LXNpemU6IDFyZW07IGxpbmUtaGVpZ2h0OiAxLjI1cmVtOyB9XFxuICAudGV4dC14bCB7IGZvbnQtc2l6ZTogMC44NzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuICAucC02IHsgcGFkZGluZzogMC43NXJlbTsgfVxcbiAgLm1iLTYgeyBtYXJnaW4tYm90dG9tOiAwLjVyZW07IH1cXG4gIC5jaGF0LWJ1YmJsZSB7IG1heC13aWR0aDogNzUlOyBwYWRkaW5nOiAwLjVyZW07IGZvbnQtc2l6ZTogMC43NXJlbTsgbGluZS1oZWlnaHQ6IDEuMjsgfVxcbn1cXG5cXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcXG4gIC5jaGF0LWhlYWRlciB7IHBhZGRpbmc6IDAuMTI1cmVtOyB9XFxuICAuY2hhdC1tZXNzYWdlcyB7IHBhZGRpbmc6IDAgMC4yNXJlbTsgfVxcbiAgLmNoYXQtaW5wdXQtYXJlYSB7IHBhZGRpbmc6IDAuMjVyZW07IH1cXG4gIC50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMC44NzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuICAuc3BhY2UteS0zID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjI1cmVtOyB9XFxuICAuc3BhY2UteS00ID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cXG4gIC5jaGF0LWJ1YmJsZSB7IG1heC13aWR0aDogODAlOyBwYWRkaW5nOiAwLjM3NXJlbTsgZm9udC1zaXplOiAwLjYyNXJlbTsgbGluZS1oZWlnaHQ6IDEuMTsgfVxcbiAgLmlucHV0LWZpZWxkIHsgbWluLWhlaWdodDogMzJweDsgZm9udC1zaXplOiAwLjc1cmVtOyBwYWRkaW5nOiAwLjM3NXJlbSAwLjVyZW07IH1cXG59XFxuXFxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XFxuICAuY2hhdC1sYXlvdXQgeyBtYXgtd2lkdGg6IDR4bDsgbWFyZ2luOiAwIGF1dG87IH1cXG59XFxuXFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcbiAgLmNoYXQtbWVzc2FnZXMgeyBwYWRkaW5nOiAwIDJyZW07IH1cXG4gIC5jaGF0LWlucHV0LWFyZWEgeyBwYWRkaW5nOiAxLjVyZW0gMnJlbTsgfVxcbn1cXG5cXG4vKiBLZXlmcmFtZSBBbmltYXRpb25zICovXFxuQGtleWZyYW1lcyBzbGlkZUluRnJvbUJvdHRvbSB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDhweCk7XFxuICB9XFxuICB0byB7XFxuICAgIG9wYWNpdHk6IDE7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBzcGluIHtcXG4gIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxcbiAgdG8geyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9XFxufVxcblxcbkBrZXlmcmFtZXMgYm91bmNlIHtcXG4gIDAlLCAxMDAlIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0yNSUpO1xcbiAgICBhbmltYXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC44LCAwLCAxLCAxKTtcXG4gIH1cXG4gIDUwJSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXG4gICAgYW5pbWF0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAsIDAsIDAuMiwgMSk7XFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgcHVsc2Uge1xcbiAgMCUsIDEwMCUgeyBvcGFjaXR5OiAxOyB9XFxuICA1MCUgeyBvcGFjaXR5OiAwLjU7IH1cXG59XFxuXFxuQGtleWZyYW1lcyBsaXF1aWRNb3ZlIHtcXG4gIDAlLCAxMDAlIHtcXG4gICAgYmFja2dyb3VuZDpcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA4MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDgwJSAyMCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIH1cXG4gIDI1JSB7XFxuICAgIGJhY2tncm91bmQ6XFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA2MCUgMzAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAzMCUgNzAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA3MCUgNjAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICB9XFxuICA1MCUge1xcbiAgICBiYWNrZ3JvdW5kOlxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDYwJSwgcmdiYSgxMjAsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMjAlIDQwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNjAlIDgwJSwgcmdiYSgxMjAsIDIxOSwgMjU1LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpO1xcbiAgfVxcbiAgNzUlIHtcXG4gICAgYmFja2dyb3VuZDpcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA3MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC40KSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDcwJSAzMCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC40KSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDMwJSAyMCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC40KSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIH1cXG59XFxuXCJdLFwic291cmNlUm9vdFwiOlwiXCJ9XSk7XG4vLyBFeHBvcnRzXG5leHBvcnQgZGVmYXVsdCBfX19DU1NfTE9BREVSX0VYUE9SVF9fXztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css\n"));

/***/ })

});