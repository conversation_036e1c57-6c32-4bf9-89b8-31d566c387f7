/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_app"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);\\n  background-attachment: fixed;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 0.75rem;\\n  border-radius: 0.75rem;\\n  max-width: 16rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  text-align: left;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.8);\\n  margin-left: auto;\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.1);\\n  margin-right: auto;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem 0.75rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 40px;\\n  max-height: 80px;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n.input-field::-moz-placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.5rem;\\n  padding: 0.5rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar nascosta ma funzionale */\\n.chat-container {\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-container::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 0.75rem;\\n  min-height: 0; /* Importante per il flex */\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Responsive Design */\\n.h-screen { height: 100vh; }\\n.h-full { height: 100%; }\\n.min-h-0 { min-height: 0; }\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; line-height: 1.2; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; line-height: 1.1; }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n/* Responsive */\\n@media (max-width: 768px) {\\n  .max-w-4xl { max-width: 100%; }\\n  .p-4 { padding: 0.5rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ,0EAA0E;EAC1E,4BAA4B;AAC9B;;AAEA,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,mBAAmB,kBAAkB,EAAE;AACvC,uBAAuB,qBAAqB,EAAE;AAC9C,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,sBAAsB,EAAE;AAChC,qBAAqB,kBAAkB,EAAE;AACzC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,kBAAkB,EAAE,mBAAmB,EAAE;AACrD,WAAW,eAAe,EAAE,oBAAoB,EAAE;AAClD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,WAAW,mBAAmB,EAAE,qBAAqB,EAAE;AACvD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,YAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;;AAEzB,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,iDAAiD;EACjD,mBAAmB;AACrB;;AAEA;EACE,gBAAgB;EAChB,sBAAsB;EACtB,gBAAgB;EAChB,6CAA6C;EAC7C,0CAA0C;EAC1C,qBAAqB;EACrB,yBAAyB;EACzB,mBAAmB;EACnB,oBAAoB;EACpB,aAAa;EACb,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,iBAAiB;AACnB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,sBAAsB;EACtB,uBAAuB;EACvB,YAAY;EACZ,yBAAyB;EACzB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,gBAAgB;EAChB,mBAAmB;EACnB,oBAAoB;AACtB;;AAEA;EACE,+BAA+B;AACjC;;AAFA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,qBAAqB;EACrB,eAAe;EACf,YAAY;EACZ,eAAe;EACf,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,qCAAqC;AACrC;EACE,qBAAqB,EAAE,YAAY;EACnC,wBAAwB,EAAE,0BAA0B;EACpD,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,aAAa,EAAE,0BAA0B;AAC3C;;AAEA,+BAA+B;AAC/B;EACE,aAAa;EACb,aAAa;EACb,sBAAsB;EACtB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,eAAe;AACjB;;AAEA;EACE,OAAO;EACP,gBAAgB;EAChB,kBAAkB;EAClB,aAAa,EAAE,2BAA2B;EAC1C,qBAAqB,EAAE,YAAY;EACnC,wBAAwB,EAAE,0BAA0B;EACpD,uBAAuB;EACvB,4BAA4B;AAC9B;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,aAAa,EAAE,0BAA0B;AAC3C;;AAEA;EACE,cAAc;EACd,gBAAgB;EAChB,8BAA8B;EAC9B,mCAA2B;UAA3B,2BAA2B;EAC3B,8CAA8C;AAChD;;AAEA,sBAAsB;AACtB,YAAY,aAAa,EAAE;AAC3B,UAAU,YAAY,EAAE;AACxB,WAAW,aAAa,EAAE;;AAE1B,4BAA4B;AAC5B;EACE,eAAe,gBAAgB,EAAE;EACjC,iBAAiB,iBAAiB,EAAE;EACpC,mBAAmB,eAAe,EAAE;EACpC,YAAY,eAAe,EAAE,oBAAoB,EAAE;EACnD,WAAW,mBAAmB,EAAE,iBAAiB,EAAE;EACnD,OAAO,gBAAgB,EAAE;EACzB,QAAQ,qBAAqB,EAAE;EAC/B,eAAe,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,EAAE;AACxF;;AAEA;EACE,eAAe,iBAAiB,EAAE;EAClC,iBAAiB,kBAAkB,EAAE;EACrC,mBAAmB,gBAAgB,EAAE;EACrC,YAAY,mBAAmB,EAAE,iBAAiB,EAAE;EACpD,qBAAqB,mBAAmB,EAAE;EAC1C,qBAAqB,kBAAkB,EAAE;EACzC,eAAe,cAAc,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE;EACzF,eAAe,gBAAgB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE;AACjF;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,6BAA6B;AAC7B;EACE,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA,eAAe;AACf;EACE,aAAa,eAAe,EAAE;EAC9B,OAAO,eAAe,EAAE;AAC1B;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA,6BAA6B;AAC7B;EACE,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);\\n  background-attachment: fixed;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 0.75rem;\\n  border-radius: 0.75rem;\\n  max-width: 16rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  display: flex;\\n  align-items: center;\\n  text-align: left;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.8);\\n  margin-left: auto;\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.1);\\n  margin-right: auto;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.5rem 0.75rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 40px;\\n  max-height: 80px;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.5rem;\\n  padding: 0.5rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar nascosta ma funzionale */\\n.chat-container {\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-container::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 0.5rem;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 0.75rem;\\n  min-height: 0; /* Importante per il flex */\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n  scroll-behavior: smooth;\\n  overscroll-behavior: contain;\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 0.75rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Responsive Design */\\n.h-screen { height: 100vh; }\\n.h-full { height: 100%; }\\n.min-h-0 { min-height: 0; }\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble { max-width: 75%; padding: 0.5rem; font-size: 0.75rem; line-height: 1.2; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble { max-width: 80%; padding: 0.375rem; font-size: 0.625rem; line-height: 1.1; }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n/* Responsive */\\n@media (max-width: 768px) {\\n  .max-w-4xl { max-width: 100%; }\\n  .p-4 { padding: 0.5rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/ // css base code, injected by the css-loader\n// eslint-disable-next-line func-names\n\nmodule.exports = function(useSourceMap) {\n    var list = [] // return the list of modules as css string\n    ;\n    list.toString = function toString() {\n        return this.map(function(item) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            var content = cssWithMappingToString(item, useSourceMap);\n            if (item[2]) {\n                return '@media '.concat(item[2], ' {').concat(content, '}');\n            }\n            return content;\n        }).join('');\n    } // import a list of modules into the list\n    ;\n    // eslint-disable-next-line func-names\n    // @ts-expect-error TODO: fix type\n    list.i = function(modules, mediaQuery, dedupe) {\n        if (typeof modules === 'string') {\n            // eslint-disable-next-line no-param-reassign\n            modules = [\n                [\n                    null,\n                    modules,\n                    ''\n                ]\n            ];\n        }\n        var alreadyImportedModules = {};\n        if (dedupe) {\n            for(var i = 0; i < this.length; i++){\n                // eslint-disable-next-line prefer-destructuring\n                var id = this[i][0];\n                if (id != null) {\n                    alreadyImportedModules[id] = true;\n                }\n            }\n        }\n        for(var _i = 0; _i < modules.length; _i++){\n            var item = [].concat(modules[_i]);\n            if (dedupe && alreadyImportedModules[item[0]]) {\n                continue;\n            }\n            if (mediaQuery) {\n                if (!item[2]) {\n                    item[2] = mediaQuery;\n                } else {\n                    item[2] = ''.concat(mediaQuery, ' and ').concat(item[2]);\n                }\n            }\n            list.push(item);\n        }\n    };\n    return list;\n};\nfunction cssWithMappingToString(item, useSourceMap) {\n    var content = item[1] || '' // eslint-disable-next-line prefer-destructuring\n    ;\n    var cssMapping = item[3];\n    if (!cssMapping) {\n        return content;\n    }\n    if (useSourceMap && typeof btoa === 'function') {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        var sourceMapping = toComment(cssMapping);\n        var sourceURLs = cssMapping.sources.map(function(source) {\n            return '/*# sourceURL='.concat(cssMapping.sourceRoot || '').concat(source, ' */');\n        });\n        return [\n            content\n        ].concat(sourceURLs).concat([\n            sourceMapping\n        ]).join('\\n');\n    }\n    return [\n        content\n    ].join('\\n');\n} // Adapted from convert-source-map (MIT)\nfunction toComment(sourceMap) {\n    // eslint-disable-next-line no-undef\n    var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n    var data = 'sourceMappingURL=data:application/json;charset=utf-8;base64,'.concat(base64);\n    return '/*# '.concat(data, ' */');\n}\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app! ***!
  \*******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_app\",\n      function () {\n        return __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-browser)/./src/pages/_app.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_app\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmcGFnZT0lMkZfYXBwISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLHlFQUF5QjtBQUNoRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9fYXBwXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL19hcHBcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js ***!
  \************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/// <reference types=\"webpack/module.d.ts\" />\n\nconst isOldIE = function isOldIE() {\n    let memo;\n    return function memorize() {\n        if (typeof memo === 'undefined') {\n            // Test for IE <= 9 as proposed by Browserhacks\n            // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n            // Tests for existence of standard globals is to allow style-loader\n            // to operate correctly into non-standard environments\n            // @see https://github.com/webpack-contrib/style-loader/issues/177\n            memo = Boolean(window && document && document.all && !window.atob);\n        }\n        return memo;\n    };\n}();\nconst getTargetElement = function() {\n    const memo = {};\n    return function memorize(target) {\n        if (typeof memo[target] === 'undefined') {\n            let styleTarget = document.querySelector(target);\n            // Special case to return head of iframe instead of iframe itself\n            if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n                try {\n                    // This will throw an exception if access to iframe is blocked\n                    // due to cross-origin restrictions\n                    styleTarget = styleTarget.contentDocument.head;\n                } catch (e) {\n                    // istanbul ignore next\n                    styleTarget = null;\n                }\n            }\n            memo[target] = styleTarget;\n        }\n        return memo[target];\n    };\n}();\nconst stylesInDom = [];\nfunction getIndexByIdentifier(identifier) {\n    let result = -1;\n    for(let i = 0; i < stylesInDom.length; i++){\n        if (stylesInDom[i].identifier === identifier) {\n            result = i;\n            break;\n        }\n    }\n    return result;\n}\nfunction modulesToDom(list, options) {\n    const idCountMap = {};\n    const identifiers = [];\n    for(let i = 0; i < list.length; i++){\n        const item = list[i];\n        const id = options.base ? item[0] + options.base : item[0];\n        const count = idCountMap[id] || 0;\n        const identifier = id + ' ' + count.toString();\n        idCountMap[id] = count + 1;\n        const index = getIndexByIdentifier(identifier);\n        const obj = {\n            css: item[1],\n            media: item[2],\n            sourceMap: item[3]\n        };\n        if (index !== -1) {\n            stylesInDom[index].references++;\n            stylesInDom[index].updater(obj);\n        } else {\n            stylesInDom.push({\n                identifier: identifier,\n                // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                updater: addStyle(obj, options),\n                references: 1\n            });\n        }\n        identifiers.push(identifier);\n    }\n    return identifiers;\n}\nfunction insertStyleElement(options) {\n    const style = document.createElement('style');\n    const attributes = options.attributes || {};\n    if (typeof attributes.nonce === 'undefined') {\n        const nonce = // eslint-disable-next-line no-undef\n         true ? __webpack_require__.nc : 0;\n        if (nonce) {\n            attributes.nonce = nonce;\n        }\n    }\n    Object.keys(attributes).forEach(function(key) {\n        style.setAttribute(key, attributes[key]);\n    });\n    if (typeof options.insert === 'function') {\n        options.insert(style);\n    } else {\n        const target = getTargetElement(options.insert || 'head');\n        if (!target) {\n            throw Object.defineProperty(new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\"), \"__NEXT_ERROR_CODE\", {\n                value: \"E245\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        target.appendChild(style);\n    }\n    return style;\n}\nfunction removeStyleElement(style) {\n    // istanbul ignore if\n    if (style.parentNode === null) {\n        return false;\n    }\n    style.parentNode.removeChild(style);\n}\n/* istanbul ignore next  */ const replaceText = function replaceText() {\n    const textStore = [];\n    return function replace(index, replacement) {\n        textStore[index] = replacement;\n        return textStore.filter(Boolean).join('\\n');\n    };\n}();\nfunction applyToSingletonTag(style, index, remove, obj) {\n    const css = remove ? '' : obj.media ? '@media ' + obj.media + ' {' + obj.css + '}' : obj.css;\n    // For old IE\n    /* istanbul ignore if  */ if (style.styleSheet) {\n        style.styleSheet.cssText = replaceText(index, css);\n    } else {\n        const cssNode = document.createTextNode(css);\n        const childNodes = style.childNodes;\n        if (childNodes[index]) {\n            style.removeChild(childNodes[index]);\n        }\n        if (childNodes.length) {\n            style.insertBefore(cssNode, childNodes[index]);\n        } else {\n            style.appendChild(cssNode);\n        }\n    }\n}\nfunction applyToTag(style, _options, obj) {\n    let css = obj.css;\n    const media = obj.media;\n    const sourceMap = obj.sourceMap;\n    if (media) {\n        style.setAttribute('media', media);\n    } else {\n        style.removeAttribute('media');\n    }\n    if (sourceMap && typeof btoa !== 'undefined') {\n        css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */';\n    }\n    // For old IE\n    /* istanbul ignore if  */ if (style.styleSheet) {\n        style.styleSheet.cssText = css;\n    } else {\n        while(style.firstChild){\n            style.removeChild(style.firstChild);\n        }\n        style.appendChild(document.createTextNode(css));\n    }\n}\nlet singleton = null;\nlet singletonCounter = 0;\nfunction addStyle(obj, options) {\n    let style;\n    let update;\n    let remove;\n    if (options.singleton) {\n        const styleIndex = singletonCounter++;\n        style = singleton || (singleton = insertStyleElement(options));\n        update = applyToSingletonTag.bind(null, style, styleIndex, false);\n        remove = applyToSingletonTag.bind(null, style, styleIndex, true);\n    } else {\n        style = insertStyleElement(options);\n        update = applyToTag.bind(null, style, options);\n        remove = function() {\n            removeStyleElement(style);\n        };\n    }\n    update(obj);\n    return function updateStyle(newObj) {\n        if (newObj) {\n            if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {\n                return;\n            }\n            update(obj = newObj);\n        } else {\n            remove();\n        }\n    };\n}\nmodule.exports = function(list, options) {\n    options = options || {};\n    // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n    // tags it will allow on a page\n    if (!options.singleton && typeof options.singleton !== 'boolean') {\n        options.singleton = isOldIE();\n    }\n    list = list || [];\n    let lastIdentifiers = modulesToDom(list, options);\n    return function update(newList) {\n        newList = newList || [];\n        if (Object.prototype.toString.call(newList) !== '[object Array]') {\n            return;\n        }\n        for(let i = 0; i < lastIdentifiers.length; i++){\n            const identifier = lastIdentifiers[i];\n            const index = getIndexByIdentifier(identifier);\n            stylesInDom[index].references--;\n        }\n        const newLastIdentifiers = modulesToDom(newList, options);\n        for(let i = 0; i < lastIdentifiers.length; i++){\n            const identifier = lastIdentifiers[i];\n            const index = getIndexByIdentifier(identifier);\n            if (stylesInDom[index].references === 0) {\n                stylesInDom[index].updater();\n                stylesInDom.splice(index, 1);\n            }\n        }\n        lastIdentifiers = newLastIdentifiers;\n    };\n};\n\n//# sourceMappingURL=injectStylesIntoStyleTag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/react/cjs/react-jsx-dev-runtime.development.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js":
/*!***********************************************!*\
  !*** ./node_modules/react/jsx-dev-runtime.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(pages-dir-browser)/./node_modules/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsSUFBSSxLQUFxQyxFQUFFLEVBRTFDLENBQUM7QUFDRixFQUFFLDJLQUFzRTtBQUN4RSIsInNvdXJjZXMiOlsiRzpcXEZyaWVuZHNcXFNvdWxUYWxrXFxub2RlX21vZHVsZXNcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-browser)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App(param) {\n    let { Component, pageProps } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDK0I7QUFFaEIsU0FBU0EsSUFBSSxLQUFrQztRQUFsQyxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWSxHQUFsQztJQUMxQixxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakM7S0FGd0JGIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xccGFnZXNcXF9hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+O1xufVxuIl0sIm5hbWVzIjpbIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/_app.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./globals.css */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector('#__next_css__DO_NOT_USE__');\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === 'default') {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === 'default') {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./globals.css */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./globals.css */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/styles/globals.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!"), __webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/client/router.js")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);