"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ChatInput */ \"(pages-dir-browser)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { memory, isLoaded, addMessage, updateBasicInfo, addTrait, addEmotion, updateIntimacyLevel, getAuraColor, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll automatico verso il basso quando arrivano nuovi messaggi\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Nasconde il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (memory.conversationHistory.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Converte la cronologia della memoria in formato OpenRouter\n    const getOpenRouterHistory = ()=>{\n        return memory.conversationHistory.map((msg)=>({\n                role: msg.isUser ? 'user' : 'assistant',\n                content: msg.content\n            }));\n    };\n    // Gestisce l'invio di un nuovo messaggio\n    const handleSendMessage = async (message)=>{\n        if (isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Aggiunge il messaggio dell'utente alla cronologia\n            addMessage(message, true);\n            // Prepara il contesto utente per l'AI\n            const userContext = {\n                name: memory.name || undefined,\n                age: memory.age || undefined,\n                traits: memory.traits,\n                emotions: memory.emotions,\n                intimacyLevel: memory.intimacyLevel\n            };\n            // Invia il messaggio all'AI\n            const aiResponse = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(message, getOpenRouterHistory(), userContext);\n            // Aggiunge la risposta dell'AI alla cronologia\n            addMessage(aiResponse, false);\n            // Analizza il messaggio per estrarre informazioni (simulato per ora)\n            await analyzeAndUpdateMemory(message, aiResponse);\n        } catch (err) {\n            console.error('Errore nell\\'invio del messaggio:', err);\n            setError('Errore nella comunicazione. Riprova tra poco.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Analizza i messaggi e aggiorna la memoria (versione semplificata)\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        // Analisi semplificata basata su parole chiave\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !memory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updateBasicInfo({\n                    name\n                });\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !memory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updateBasicInfo({\n                    age\n                });\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                addEmotion(emotion);\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità basato sulla lunghezza e contenuto\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            const currentLevel = memory.intimacyLevel;\n            if (currentLevel < 5) {\n                updateIntimacyLevel(currentLevel + 1);\n            }\n        }\n    };\n    // Gestisce il reset della memoria\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            resetMemory();\n            setShowWelcome(true);\n            setError(null);\n        }\n    };\n    // Mostra loading durante l'idratazione\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - La tua amicizia AI\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Caricamento SoulTalk...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"chat-layout\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"SoulTalk\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"opacity-60 mb-4\",\n                                children: \"La tua amicizia AI che cresce con te\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        intimacyLevel: memory.intimacyLevel,\n                                        showLabel: true,\n                                        className: \"justify-center\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__.AuraProgression, {\n                                        intimacyLevel: memory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col min-h-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                auraColor: getAuraColor(),\n                                className: \"flex-1 flex flex-col min-h-0 mx-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: chatContainerRef,\n                                        className: \"chat-container chat-messages flex-1 space-y-4\",\n                                        children: [\n                                            showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                style: {\n                                                    padding: '2rem 0'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"glass-effect p-6 rounded-2xl mx-auto\",\n                                                    style: {\n                                                        maxWidth: '28rem'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold text-white mb-3\",\n                                                            children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm opacity-80\",\n                                                            style: {\n                                                                lineHeight: '1.6'\n                                                            },\n                                                            children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    message: message.content,\n                                                    isUser: message.isUser,\n                                                    timestamp: message.timestamp\n                                                }, message.id, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"glass-effect p-4 rounded-2xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-bounce opacity-60\",\n                                                                style: {\n                                                                    width: '8px',\n                                                                    height: '8px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-bounce opacity-60\",\n                                                                style: {\n                                                                    width: '8px',\n                                                                    height: '8px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animationDelay: '0.1s'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-bounce opacity-60\",\n                                                                style: {\n                                                                    width: '8px',\n                                                                    height: '8px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animationDelay: '0.2s'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-200 text-sm\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-t\",\n                                        style: {\n                                            borderColor: 'rgba(255, 255, 255, 0.1)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            onSendMessage: handleSendMessage,\n                                            disabled: isLoading,\n                                            placeholder: memory.name ? \"Cosa vuoi condividere con Soul, \".concat(memory.name, \"?\") : \"Dimmi qualcosa di te...\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center text-sm opacity-60\",\n                                style: {\n                                    marginTop: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: memory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Ciao \",\n                                                memory.name,\n                                                \"! Livello di connessione: \",\n                                                memory.intimacyLevel,\n                                                \"/5\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        className: \"transition-all duration-200\",\n                                        style: {\n                                            cursor: 'pointer'\n                                        },\n                                        onMouseOver: (e)=>e.target.style.opacity = '1',\n                                        onMouseOut: (e)=>e.target.style.opacity = '0.6',\n                                        children: \"Ricomincia da capo\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"FML9iZAdtm0rOHu/Dtj/snyE0xQ=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});