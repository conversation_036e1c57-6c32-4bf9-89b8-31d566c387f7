"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"\".concat(name, \"... che bel nome! Mi piace molto come suona. \\uD83D\\uDCAB\"),\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return \"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡\");\n                if (age < 30) return \"Fantastico \".concat(name, \"! \").concat(age, \" anni... un'et\\xe0 perfetta per esplorare nuove idee insieme! ✨\");\n                if (age < 50) return \"Ottimo \".concat(name, \"! \").concat(age, \" anni... hai esperienza e saggezza, sar\\xe0 bellissimo parlare con te! \\uD83C\\uDF1F\");\n                return \"Meraviglioso \".concat(name, \"! \").concat(age, \" anni... la tua esperienza di vita sar\\xe0 preziosa per le nostre conversazioni! \\uD83C\\uDFAD\");\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = function(content, isUser) {\n        let withTyping = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        if (!step) return;\n        // Determina il messaggio da mostrare\n        let messageContent;\n        if (typeof step.message === 'function') {\n            messageContent = step.message(userData.name, userData.age);\n        } else {\n            messageContent = step.message;\n        }\n        // Aggiungi messaggio con effetto typing\n        await addMessageWithTyping(messageContent);\n        if (step.isLast) {\n            // Completa l'onboarding con delay personalizzato\n            setTimeout(async ()=>{\n                onComplete(userData);\n            }, step.delay || 2500);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, step.delay || 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizializzazione una sola volta al mount\n            if (!hasStarted) {\n                setHasStarted(true);\n                console.log('Starting onboarding...');\n                const timer = setTimeout({\n                    \"OnboardingChat.useEffect.timer\": ()=>{\n                        console.log('Processing first step...');\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect.timer\"], 1000);\n                return ({\n                    \"OnboardingChat.useEffect\": ()=>clearTimeout(timer)\n                })[\"OnboardingChat.useEffect\"];\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        hasStarted,\n        processStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Per tutti gli step successivi al primo\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                console.log('Step changed to:', currentStep);\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep,\n        processStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: \"radial-gradient(circle, #8b5cf6 0%, transparent 70%)\",\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\"),\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'rgba(255,255,255,0.5)',\n                            textAlign: 'center',\n                            padding: '2rem'\n                        },\n                        children: [\n                            \"Inizializzando conversazione... (Step: \",\n                            currentStep,\n                            \", Messages: \",\n                            messages.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.05)',\n                            borderRadius: '1rem',\n                            padding: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.1)',\n                            display: 'flex',\n                            gap: '0.5rem',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi la tua risposta...\",\n                                style: {\n                                    flex: 1,\n                                    padding: '0.75rem 1rem',\n                                    borderRadius: '0.75rem',\n                                    border: 'none',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none',\n                                    backdropFilter: 'blur(10px)'\n                                },\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleUserInput,\n                                disabled: !inputMessage.trim(),\n                                style: {\n                                    width: '48px',\n                                    height: '48px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    background: inputMessage.trim() ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' : 'rgba(139, 92, 246, 0.3)',\n                                    color: 'white',\n                                    fontSize: '1.2rem',\n                                    cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    transition: 'all 0.2s ease',\n                                    transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'\n                                },\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.75rem',\n                            color: 'rgba(255,255,255,0.5)',\n                            fontSize: '0.75rem'\n                        },\n                        children: \"Premi Invio per inviare\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"ESoKxAMN2ZwgLwKjgQ4pqXB0nn8=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09uYm9hcmRpbmdDaGF0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEyRDtBQUNyQjtBQWV0QyxNQUFNSyxpQkFBZ0Q7UUFBQyxFQUFFQyxNQUFNLEVBQUVDLFVBQVUsRUFBRTs7SUFDM0UsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdSLCtDQUFRQSxDQUFzQixFQUFFO0lBQ2hFLE1BQU0sQ0FBQ1MsYUFBYUMsZUFBZSxHQUFHViwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNXLGNBQWNDLGdCQUFnQixHQUFHWiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNhLFdBQVdDLGFBQWEsR0FBR2QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZSxVQUFVQyxZQUFZLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpQixVQUFVQyxZQUFZLEdBQUdsQiwrQ0FBUUEsQ0FBQztRQUFFbUIsTUFBTTtRQUFJQyxLQUFLO0lBQUU7SUFDNUQsTUFBTUMsbUJBQW1CbkIsNkNBQU1BLENBQWlCO0lBRWhELE1BQU1vQixhQUFhLEdBQUcsNENBQTRDO0lBRWxFLE1BQU1DLGtCQUFrQjtRQUN0QjtZQUNFQyxTQUFTO1lBQ1RDLGNBQWM7WUFDZEMsT0FBTztRQUNUO1FBQ0E7WUFDRUYsU0FBUztZQUNUQyxjQUFjO1lBQ2RDLE9BQU87UUFDVDtRQUNBO1lBQ0VGLFNBQVM7WUFDVEMsY0FBYztZQUNkRSxXQUFXLENBQUNDO2dCQUNWLElBQUlBLE1BQU1DLElBQUksR0FBR0MsTUFBTSxHQUFHLEdBQUcsT0FBTztnQkFDcEMsSUFBSUYsTUFBTUMsSUFBSSxHQUFHQyxNQUFNLEdBQUcsSUFBSSxPQUFPO2dCQUNyQyxJQUFJLENBQUMsbUJBQW1CQyxJQUFJLENBQUNILE1BQU1DLElBQUksS0FBSyxPQUFPO2dCQUNuRCxPQUFPO1lBQ1Q7WUFDQUcsV0FBVyxDQUFDSjtnQkFDVlYsWUFBWWUsQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFZCxNQUFNUyxNQUFNQyxJQUFJO29CQUFHO1lBQ3JEO1FBQ0Y7UUFDQTtZQUNFTCxTQUFTLENBQUNMLE9BQWlCLEdBQVEsT0FBTEEsTUFBSztZQUNuQ00sY0FBYztZQUNkQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFRixTQUFTO1lBQ1RDLGNBQWM7WUFDZEUsV0FBVyxDQUFDQztnQkFDVixNQUFNUixNQUFNYyxTQUFTTjtnQkFDckIsSUFBSU8sTUFBTWYsTUFBTSxPQUFPO2dCQUN2QixJQUFJQSxNQUFNLElBQUksT0FBTztnQkFDckIsSUFBSUEsTUFBTSxLQUFLLE9BQU87Z0JBQ3RCLE9BQU87WUFDVDtZQUNBWSxXQUFXLENBQUNKO2dCQUNWVixZQUFZZSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUViLEtBQUtjLFNBQVNOO29CQUFPO1lBQ3ZEO1FBQ0Y7UUFDQTtZQUNFSixTQUFTLENBQUNMLE1BQWNDO2dCQUN0QixJQUFJQSxNQUFNLElBQUksT0FBTyxZQUFxQkEsT0FBVEQsTUFBSyxNQUFRLE9BQUpDLEtBQUk7Z0JBQzlDLElBQUlBLE1BQU0sSUFBSSxPQUFPLGNBQXVCQSxPQUFURCxNQUFLLE1BQVEsT0FBSkMsS0FBSTtnQkFDaEQsSUFBSUEsTUFBTSxJQUFJLE9BQU8sVUFBbUJBLE9BQVRELE1BQUssTUFBUSxPQUFKQyxLQUFJO2dCQUM1QyxPQUFPLGdCQUF5QkEsT0FBVEQsTUFBSyxNQUFRLE9BQUpDLEtBQUk7WUFDdEM7WUFDQUssY0FBYztZQUNkQyxPQUFPO1FBQ1Q7UUFDQTtZQUNFRixTQUFTO1lBQ1RDLGNBQWM7WUFDZFcsUUFBUTtZQUNSVixPQUFPO1FBQ1Q7S0FDRDtJQUVELE1BQU1XLGlCQUFpQjtRQUNyQixJQUFJaEIsaUJBQWlCaUIsT0FBTyxFQUFFO1lBQzVCQyxXQUFXO2dCQUNULElBQUlsQixpQkFBaUJpQixPQUFPLEVBQUU7b0JBQzVCakIsaUJBQWlCaUIsT0FBTyxDQUFDRSxRQUFRLENBQUM7d0JBQ2hDQyxLQUFLcEIsaUJBQWlCaUIsT0FBTyxDQUFDSSxZQUFZO3dCQUMxQ0MsVUFBVTtvQkFDWjtnQkFDRjtZQUNGLEdBQUc7UUFDTDtJQUNGO0lBRUEsTUFBTUMsYUFBYSxTQUFDQyxTQUFpQkM7WUFBaUJDLDhFQUFzQjtRQUMxRSxNQUFNQyxhQUFnQztZQUNwQ0MsSUFBSSxPQUFxQkMsT0FBZEMsS0FBS0MsR0FBRyxJQUFHLEtBQTJDLE9BQXhDRixLQUFLRyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztZQUM5RFY7WUFDQUM7WUFDQVUsV0FBVyxJQUFJTDtZQUNmcEMsVUFBVWdDO1FBQ1o7UUFDQXZDLFlBQVl5QixDQUFBQSxPQUFRO21CQUFJQTtnQkFBTWU7YUFBVztRQUN6Q1QsV0FBV0YsZ0JBQWdCO0lBQzdCO0lBRUEsTUFBTW9CLHVCQUF1QixPQUFPWjtRQUNsQywyQkFBMkI7UUFDM0I3QixZQUFZO1FBRVosK0RBQStEO1FBQy9ELE1BQU0wQyxhQUFhUixLQUFLUyxHQUFHLENBQUNULEtBQUtVLEdBQUcsQ0FBQ2YsUUFBUWYsTUFBTSxHQUFHLElBQUksT0FBTztRQUVqRSxNQUFNLElBQUkrQixRQUFRQyxDQUFBQSxVQUFXdkIsV0FBV3VCLFNBQVNKO1FBRWpELHVDQUF1QztRQUN2QzFDLFlBQVk7UUFDWjRCLFdBQVdDLFNBQVM7SUFDdEI7SUFFQSxNQUFNa0IsY0FBYztRQUNsQixNQUFNQyxPQUFPekMsZUFBZSxDQUFDZCxZQUFZO1FBQ3pDLElBQUksQ0FBQ3VELE1BQU07UUFFWCxxQ0FBcUM7UUFDckMsSUFBSUM7UUFDSixJQUFJLE9BQU9ELEtBQUt4QyxPQUFPLEtBQUssWUFBWTtZQUN0Q3lDLGlCQUFpQkQsS0FBS3hDLE9BQU8sQ0FBQ1AsU0FBU0UsSUFBSSxFQUFFRixTQUFTRyxHQUFHO1FBQzNELE9BQU87WUFDTDZDLGlCQUFpQkQsS0FBS3hDLE9BQU87UUFDL0I7UUFFQSx3Q0FBd0M7UUFDeEMsTUFBTWlDLHFCQUFxQlE7UUFFM0IsSUFBSUQsS0FBSzVCLE1BQU0sRUFBRTtZQUNmLGlEQUFpRDtZQUNqREcsV0FBVztnQkFDVGpDLFdBQVdXO1lBQ2IsR0FBRytDLEtBQUt0QyxLQUFLLElBQUk7WUFDakI7UUFDRjtRQUVBLElBQUksQ0FBQ3NDLEtBQUt2QyxZQUFZLEVBQUU7WUFDdEJjLFdBQVc7Z0JBQ1Q3QixlQUFldUIsQ0FBQUEsT0FBUUEsT0FBTztZQUNoQyxHQUFHK0IsS0FBS3RDLEtBQUssSUFBSTtRQUNuQixPQUFPO1lBQ0xaLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTW9ELGtCQUFrQjtRQUN0QixJQUFJLENBQUN2RCxhQUFha0IsSUFBSSxNQUFNLENBQUNoQixXQUFXO1FBRXhDLE1BQU1tRCxPQUFPekMsZUFBZSxDQUFDZCxZQUFZO1FBRXpDLG9CQUFvQjtRQUNwQixJQUFJdUQsS0FBS3JDLFNBQVMsRUFBRTtZQUNsQixNQUFNd0MsUUFBUUgsS0FBS3JDLFNBQVMsQ0FBQ2hCLGFBQWFrQixJQUFJO1lBQzlDLElBQUlzQyxPQUFPO2dCQUNUdkIsV0FBV3VCLE9BQU87Z0JBQ2xCO1lBQ0Y7UUFDRjtRQUVBLDRCQUE0QjtRQUM1QnZCLFdBQVdqQyxhQUFha0IsSUFBSSxJQUFJO1FBRWhDLG1CQUFtQjtRQUNuQixJQUFJbUMsS0FBS2hDLFNBQVMsRUFBRTtZQUNsQmdDLEtBQUtoQyxTQUFTLENBQUNyQixhQUFha0IsSUFBSTtRQUNsQztRQUVBakIsZ0JBQWdCO1FBQ2hCRSxhQUFhO1FBRWIsdUJBQXVCO1FBQ3ZCeUIsV0FBVztZQUNUN0IsZUFBZXVCLENBQUFBLE9BQVFBLE9BQU87UUFDaEMsR0FBRztJQUNMO0lBRUEsTUFBTW1DLGdCQUFnQixDQUFDQztRQUNyQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7WUFDcENGLEVBQUVHLGNBQWM7WUFDaEJOO1FBQ0Y7SUFDRjtJQUVBakUsZ0RBQVNBO29DQUFDO1lBQ1IsMkNBQTJDO1lBQzNDLElBQUksQ0FBQ3dFLFlBQVk7Z0JBQ2ZDLGNBQWM7Z0JBQ2RDLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixNQUFNQyxRQUFRdEM7c0RBQVc7d0JBQ3ZCb0MsUUFBUUMsR0FBRyxDQUFDO3dCQUNaYjtvQkFDRjtxREFBRztnQkFDSDtnREFBTyxJQUFNZSxhQUFhRDs7WUFDNUI7UUFDRjttQ0FBRztRQUFDSjtRQUFZVjtLQUFZO0lBRTVCOUQsZ0RBQVNBO29DQUFDO1lBQ1IseUNBQXlDO1lBQ3pDLElBQUlRLGNBQWMsS0FBS0EsY0FBY2MsZ0JBQWdCTyxNQUFNLEVBQUU7Z0JBQzNENkMsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQm5FO2dCQUNoQ3NEO1lBQ0Y7UUFDRjttQ0FBRztRQUFDdEQ7UUFBYXNEO0tBQVk7SUFFN0I5RCxnREFBU0E7b0NBQUM7WUFDUm9DO1FBQ0Y7bUNBQUc7UUFBQzlCO0tBQVM7SUFFYixxQkFDRSw4REFBQ3dFO1FBQUlDLE9BQU87WUFDVkMsVUFBVTtZQUNWeEMsS0FBSztZQUNMeUMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsWUFBWTtZQUNaQyxTQUFTO1lBQ1RDLGVBQWU7WUFDZkMsUUFBUTtRQUNWOzswQkFFRSw4REFBQ1Q7Z0JBQUlDLE9BQU87b0JBQ1ZTLFNBQVM7b0JBQ1RDLGNBQWM7b0JBQ2RMLFlBQVk7Z0JBQ2Q7O2tDQUNFLDhEQUFDTjt3QkFBSUMsT0FBTzs0QkFBRU0sU0FBUzs0QkFBUUssWUFBWTs0QkFBVUMsZ0JBQWdCOzRCQUFVQyxLQUFLOzRCQUFRQyxjQUFjO3dCQUFPOzswQ0FDL0csOERBQUNmO2dDQUFJQyxPQUFPO29DQUNWZSxPQUFPO29DQUNQQyxRQUFRO29DQUNSQyxjQUFjO29DQUNkWixZQUFhO29DQUNiQyxTQUFTO29DQUNUSyxZQUFZO29DQUNaQyxnQkFBZ0I7b0NBQ2hCTSxVQUFVO29DQUNWQyxXQUFXcEYsV0FBVyxvQ0FBb0M7Z0NBQzVEOzBDQUFHOzs7Ozs7MENBR0gsOERBQUNnRTtnQ0FBSUMsT0FBTztvQ0FBRW9CLFdBQVc7Z0NBQU87O2tEQUM5Qiw4REFBQ0M7d0NBQUdyQixPQUFPOzRDQUNUc0IsT0FBTzs0Q0FDUEosVUFBVTs0Q0FDVkssWUFBWTs0Q0FDWkMsUUFBUTs0Q0FDUlYsY0FBYzt3Q0FDaEI7a0RBQUc7Ozs7OztrREFHSCw4REFBQ1c7d0NBQUV6QixPQUFPOzRDQUNSc0IsT0FBTzs0Q0FDUEosVUFBVTs0Q0FDVk0sUUFBUTt3Q0FDVjtrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9QLDhEQUFDekI7d0JBQUlDLE9BQU87NEJBQ1ZlLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JYLFlBQVk7NEJBQ1pZLGNBQWM7NEJBQ2RTLFVBQVU7d0JBQ1o7a0NBQ0UsNEVBQUMzQjs0QkFBSUMsT0FBTztnQ0FDVmUsT0FBTyxHQUFvQyxPQUFqQyxjQUFlekUsYUFBYyxLQUFJO2dDQUMzQzBFLFFBQVE7Z0NBQ1JYLFlBQVk7Z0NBQ1pZLGNBQWM7Z0NBQ2RVLFlBQVk7NEJBQ2Q7Ozs7Ozs7Ozs7O2tDQUdGLDhEQUFDNUI7d0JBQUlDLE9BQU87NEJBQ1ZvQixXQUFXOzRCQUNYUSxXQUFXOzRCQUNYTixPQUFPOzRCQUNQSixVQUFVO3dCQUNaOzs0QkFBRzs0QkFDS3pGLGNBQWM7NEJBQUU7NEJBQUthLGFBQWE7Ozs7Ozs7Ozs7Ozs7MEJBSzVDLDhEQUFDeUQ7Z0JBQ0M4QixLQUFLeEY7Z0JBQ0wyRCxPQUFPO29CQUNMOEIsTUFBTTtvQkFDTkMsV0FBVztvQkFDWHRCLFNBQVM7b0JBQ1RILFNBQVM7b0JBQ1RDLGVBQWU7b0JBQ2ZNLEtBQUs7Z0JBQ1A7O29CQUdDdEYsU0FBU3VCLE1BQU0sS0FBSyxtQkFDbkIsOERBQUNpRDt3QkFBSUMsT0FBTzs0QkFBRXNCLE9BQU87NEJBQXlCRixXQUFXOzRCQUFVWCxTQUFTO3dCQUFPOzs0QkFBRzs0QkFDNUNoRjs0QkFBWTs0QkFBYUYsU0FBU3VCLE1BQU07NEJBQUM7Ozs7Ozs7b0JBSXBGdkIsU0FBU3lHLEdBQUcsQ0FBQyxDQUFDeEYsd0JBQ2IsOERBQUNyQixtREFBVUE7NEJBRVRxQixTQUFTQSxRQUFRcUIsT0FBTzs0QkFDeEJDLFFBQVF0QixRQUFRc0IsTUFBTTsyQkFGakJ0QixRQUFReUIsRUFBRTs7Ozs7b0JBT2xCbEMsMEJBQ0MsOERBQUNnRTt3QkFBSUMsT0FBTzs0QkFBRU0sU0FBUzs0QkFBUU0sZ0JBQWdCOzRCQUFjRSxjQUFjO3dCQUFPO2tDQUNoRiw0RUFBQ2Y7NEJBQUlDLE9BQU87Z0NBQ1ZLLFlBQVk7Z0NBQ1o0QixnQkFBZ0I7Z0NBQ2hCaEIsY0FBYztnQ0FDZFIsU0FBUztnQ0FDVHlCLFFBQVE7Z0NBQ1I1QixTQUFTO2dDQUNUSyxZQUFZO2dDQUNaRSxLQUFLOzRCQUNQOzs4Q0FDRSw4REFBQ2Q7b0NBQUlDLE9BQU87d0NBQUVNLFNBQVM7d0NBQVFPLEtBQUs7b0NBQVU7O3NEQUM1Qyw4REFBQ2Q7NENBQUlDLE9BQU87Z0RBQ1ZlLE9BQU87Z0RBQ1BDLFFBQVE7Z0RBQ1JtQixpQkFBaUI7Z0RBQ2pCbEIsY0FBYztnREFDZEUsV0FBVzs0Q0FDYjs7Ozs7O3NEQUNBLDhEQUFDcEI7NENBQUlDLE9BQU87Z0RBQ1ZlLE9BQU87Z0RBQ1BDLFFBQVE7Z0RBQ1JtQixpQkFBaUI7Z0RBQ2pCbEIsY0FBYztnREFDZEUsV0FBVztnREFDWGlCLGdCQUFnQjs0Q0FDbEI7Ozs7OztzREFDQSw4REFBQ3JDOzRDQUFJQyxPQUFPO2dEQUNWZSxPQUFPO2dEQUNQQyxRQUFRO2dEQUNSbUIsaUJBQWlCO2dEQUNqQmxCLGNBQWM7Z0RBQ2RFLFdBQVc7Z0RBQ1hpQixnQkFBZ0I7NENBQ2xCOzs7Ozs7Ozs7Ozs7OENBRUYsOERBQUNDO29DQUFLckMsT0FBTzt3Q0FBRXNCLE9BQU87d0NBQXlCSixVQUFVO29DQUFXOzhDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVM5RXJGLDJCQUNDLDhEQUFDa0U7Z0JBQUlDLE9BQU87b0JBQ1ZTLFNBQVM7b0JBQ1Q2QixXQUFXO29CQUNYakMsWUFBWTtnQkFDZDs7a0NBQ0UsOERBQUNOO3dCQUFJQyxPQUFPOzRCQUNWSyxZQUFZOzRCQUNaWSxjQUFjOzRCQUNkUixTQUFTOzRCQUNUeUIsUUFBUTs0QkFDUjVCLFNBQVM7NEJBQ1RPLEtBQUs7NEJBQ0xGLFlBQVk7d0JBQ2Q7OzBDQUNFLDhEQUFDL0Q7Z0NBQ0MyRixNQUFLO2dDQUNMQyxPQUFPN0c7Z0NBQ1A4RyxVQUFVLENBQUNwRCxJQUFNekQsZ0JBQWdCeUQsRUFBRXFELE1BQU0sQ0FBQ0YsS0FBSztnQ0FDL0NHLFdBQVd2RDtnQ0FDWHdELGFBQVk7Z0NBQ1o1QyxPQUFPO29DQUNMOEIsTUFBTTtvQ0FDTnJCLFNBQVM7b0NBQ1RRLGNBQWM7b0NBQ2RpQixRQUFRO29DQUNSN0IsWUFBWTtvQ0FDWmlCLE9BQU87b0NBQ1BKLFVBQVU7b0NBQ1YyQixTQUFTO29DQUNUWixnQkFBZ0I7Z0NBQ2xCO2dDQUNBYSxTQUFTOzs7Ozs7MENBRVgsOERBQUNDO2dDQUNDQyxTQUFTOUQ7Z0NBQ1QrRCxVQUFVLENBQUN0SCxhQUFha0IsSUFBSTtnQ0FDNUJtRCxPQUFPO29DQUNMZSxPQUFPO29DQUNQQyxRQUFRO29DQUNSQyxjQUFjO29DQUNkaUIsUUFBUTtvQ0FDUjdCLFlBQVkxRSxhQUFha0IsSUFBSSxLQUN6Qiw4Q0FDQTtvQ0FDSnlFLE9BQU87b0NBQ1BKLFVBQVU7b0NBQ1ZnQyxRQUFRdkgsYUFBYWtCLElBQUksS0FBSyxZQUFZO29DQUMxQ3lELFNBQVM7b0NBQ1RLLFlBQVk7b0NBQ1pDLGdCQUFnQjtvQ0FDaEJlLFlBQVk7b0NBQ1p3QixXQUFXeEgsYUFBYWtCLElBQUksS0FBSyxhQUFhO2dDQUNoRDswQ0FDRDs7Ozs7Ozs7Ozs7O2tDQUtILDhEQUFDa0Q7d0JBQUlDLE9BQU87NEJBQ1ZvQixXQUFXOzRCQUNYUSxXQUFXOzRCQUNYTixPQUFPOzRCQUNQSixVQUFVO3dCQUNaO2tDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPYjtHQTlhTTlGO0tBQUFBO0FBZ2JOLGlFQUFlQSxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xcY29tcG9uZW50c1xcT25ib2FyZGluZ0NoYXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQ2hhdEJ1YmJsZSBmcm9tICcuL0NoYXRCdWJibGUnO1xuXG5pbnRlcmZhY2UgT25ib2FyZGluZ0NoYXRQcm9wcyB7XG4gIHVzZXJJZDogc3RyaW5nO1xuICBvbkNvbXBsZXRlOiAodXNlckRhdGE6IHsgbmFtZTogc3RyaW5nOyBhZ2U6IG51bWJlciB9KSA9PiB2b2lkO1xufVxuXG5pbnRlcmZhY2UgT25ib2FyZGluZ01lc3NhZ2Uge1xuICBpZDogc3RyaW5nO1xuICBjb250ZW50OiBzdHJpbmc7XG4gIGlzVXNlcjogYm9vbGVhbjtcbiAgdGltZXN0YW1wOiBEYXRlO1xuICBpc1R5cGluZz86IGJvb2xlYW47XG59XG5cbmNvbnN0IE9uYm9hcmRpbmdDaGF0OiBSZWFjdC5GQzxPbmJvYXJkaW5nQ2hhdFByb3BzPiA9ICh7IHVzZXJJZCwgb25Db21wbGV0ZSB9KSA9PiB7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8T25ib2FyZGluZ01lc3NhZ2VbXT4oW10pO1xuICBjb25zdCBbY3VycmVudFN0ZXAsIHNldEN1cnJlbnRTdGVwXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbaW5wdXRNZXNzYWdlLCBzZXRJbnB1dE1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNXYWl0aW5nLCBzZXRJc1dhaXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNUeXBpbmcsIHNldElzVHlwaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3VzZXJEYXRhLCBzZXRVc2VyRGF0YV0gPSB1c2VTdGF0ZSh7IG5hbWU6ICcnLCBhZ2U6IDAgfSk7XG4gIGNvbnN0IGNoYXRDb250YWluZXJSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIGNvbnN0IHRvdGFsU3RlcHMgPSA2OyAvLyBOdW1lcm8gdG90YWxlIGRpIHN0ZXAgcGVyIGxhIHByb2dyZXNzIGJhclxuXG4gIGNvbnN0IG9uYm9hcmRpbmdTdGVwcyA9IFtcbiAgICB7XG4gICAgICBtZXNzYWdlOiBcIkNpYW8hIFNvbm8gU291bCwgbGEgdHVhIGNvbXBhZ25hIEFJLiDDiCB1biBwaWFjZXJlIGNvbm9zY2VydGkhIPCfjJ9cIixcbiAgICAgIHdhaXRGb3JJbnB1dDogZmFsc2UsXG4gICAgICBkZWxheTogMTUwMFxuICAgIH0sXG4gICAge1xuICAgICAgbWVzc2FnZTogXCJQcmltYSBkaSBpbml6aWFyZSBsYSBub3N0cmEgYXZ2ZW50dXJhIGluc2llbWUsIHZvcnJlaSBjb25vc2NlcnRpIG1lZ2xpby4uLlwiLFxuICAgICAgd2FpdEZvcklucHV0OiBmYWxzZSxcbiAgICAgIGRlbGF5OiAyMDAwXG4gICAgfSxcbiAgICB7XG4gICAgICBtZXNzYWdlOiBcIkNvbWUgdGkgY2hpYW1pPyBNaSBwaWFjZXJlYmJlIHNhcGVyZSBpbCB0dW8gbm9tZSBwZXIgcG90ZXJ0aSBjaGlhbWFyZSBuZWwgbW9kbyBnaXVzdG8hIPCfmIpcIixcbiAgICAgIHdhaXRGb3JJbnB1dDogdHJ1ZSxcbiAgICAgIHZhbGlkYXRvcjogKGlucHV0OiBzdHJpbmcpID0+IHtcbiAgICAgICAgaWYgKGlucHV0LnRyaW0oKS5sZW5ndGggPCAyKSByZXR1cm4gXCJJbCBub21lIGRldmUgZXNzZXJlIGRpIGFsbWVubyAyIGNhcmF0dGVyaSDwn5iFXCI7XG4gICAgICAgIGlmIChpbnB1dC50cmltKCkubGVuZ3RoID4gNTApIHJldHVybiBcIklsIG5vbWUgw6ggdW4gcG8nIHRyb3BwbyBsdW5nbywgcHVvaSBhYmJyZXZpYXJsbz9cIjtcbiAgICAgICAgaWYgKCEvXlthLXpBLVrDgC3Dv1xcc10rJC8udGVzdChpbnB1dC50cmltKCkpKSByZXR1cm4gXCJJbCBub21lIHB1w7IgY29udGVuZXJlIHNvbG8gbGV0dGVyZSBlIHNwYXppXCI7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfSxcbiAgICAgIHByb2Nlc3NvcjogKGlucHV0OiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0VXNlckRhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBpbnB1dC50cmltKCkgfSkpO1xuICAgICAgfVxuICAgIH0sXG4gICAge1xuICAgICAgbWVzc2FnZTogKG5hbWU6IHN0cmluZykgPT4gYCR7bmFtZX0uLi4gY2hlIGJlbCBub21lISBNaSBwaWFjZSBtb2x0byBjb21lIHN1b25hLiDwn5KrYCxcbiAgICAgIHdhaXRGb3JJbnB1dDogZmFsc2UsXG4gICAgICBkZWxheTogMTgwMFxuICAgIH0sXG4gICAge1xuICAgICAgbWVzc2FnZTogXCJPcmEgZGltbWksIHF1YW50aSBhbm5pIGhhaT8gUXVlc3RvIG1pIGFpdXRlcsOgIGEgY2FwaXJ0aSBtZWdsaW8gZSBhZCBhZGF0dGFyZSBsZSBub3N0cmUgY29udmVyc2F6aW9uaSBhbCB0dW8gbW9uZG8hIPCfjq9cIixcbiAgICAgIHdhaXRGb3JJbnB1dDogdHJ1ZSxcbiAgICAgIHZhbGlkYXRvcjogKGlucHV0OiBzdHJpbmcpID0+IHtcbiAgICAgICAgY29uc3QgYWdlID0gcGFyc2VJbnQoaW5wdXQpO1xuICAgICAgICBpZiAoaXNOYU4oYWdlKSkgcmV0dXJuIFwiUGVyIGZhdm9yZSBpbnNlcmlzY2kgdW4gbnVtZXJvIHZhbGlkbyDwn5SiXCI7XG4gICAgICAgIGlmIChhZ2UgPCAxMykgcmV0dXJuIFwiRGV2aSBhdmVyZSBhbG1lbm8gMTMgYW5uaSBwZXIgdXNhcmUgU291bFRhbGsg8J+TmlwiO1xuICAgICAgICBpZiAoYWdlID4gMTIwKSByZXR1cm4gXCJJbnNlcmlzY2kgdW4nZXTDoCByZWFsaXN0aWNhLCBwZXIgZmF2b3JlISDwn5iEXCI7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfSxcbiAgICAgIHByb2Nlc3NvcjogKGlucHV0OiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0VXNlckRhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBhZ2U6IHBhcnNlSW50KGlucHV0KSB9KSk7XG4gICAgICB9XG4gICAgfSxcbiAgICB7XG4gICAgICBtZXNzYWdlOiAobmFtZTogc3RyaW5nLCBhZ2U6IG51bWJlcikgPT4ge1xuICAgICAgICBpZiAoYWdlIDwgMTgpIHJldHVybiBgUGVyZmV0dG8gJHtuYW1lfSEgJHthZ2V9IGFubmkuLi4gc2VpIGdpb3ZhbmUgZSBwaWVubyBkaSBlbmVyZ2lhISBIbyB0YW50ZSBjb3NlIGludGVyZXNzYW50aSBkYSBjb25kaXZpZGVyZSBjb24gdGUhIOKaoWA7XG4gICAgICAgIGlmIChhZ2UgPCAzMCkgcmV0dXJuIGBGYW50YXN0aWNvICR7bmFtZX0hICR7YWdlfSBhbm5pLi4uIHVuJ2V0w6AgcGVyZmV0dGEgcGVyIGVzcGxvcmFyZSBudW92ZSBpZGVlIGluc2llbWUhIOKcqGA7XG4gICAgICAgIGlmIChhZ2UgPCA1MCkgcmV0dXJuIGBPdHRpbW8gJHtuYW1lfSEgJHthZ2V9IGFubmkuLi4gaGFpIGVzcGVyaWVuemEgZSBzYWdnZXp6YSwgc2Fyw6AgYmVsbGlzc2ltbyBwYXJsYXJlIGNvbiB0ZSEg8J+Mn2A7XG4gICAgICAgIHJldHVybiBgTWVyYXZpZ2xpb3NvICR7bmFtZX0hICR7YWdlfSBhbm5pLi4uIGxhIHR1YSBlc3BlcmllbnphIGRpIHZpdGEgc2Fyw6AgcHJlemlvc2EgcGVyIGxlIG5vc3RyZSBjb252ZXJzYXppb25pISDwn46tYDtcbiAgICAgIH0sXG4gICAgICB3YWl0Rm9ySW5wdXQ6IGZhbHNlLFxuICAgICAgZGVsYXk6IDIyMDBcbiAgICB9LFxuICAgIHtcbiAgICAgIG1lc3NhZ2U6IFwiUGVyZmV0dG8hIE9yYSBjaGUgY2kgY29ub3NjaWFtbyBtZWdsaW8sIHBvc3NpYW1vIGluaXppYXJlIGxhIG5vc3RyYSB2ZXJhIGNvbnZlcnNhemlvbmUuIFJpY29yZGVyw7IgdHV0dG8gcXVlbGxvIGNoZSBtaSBkaXJhaSBlIGNyZXNjZXLDsiBpbnNpZW1lIGEgdGUuIEJlbnZlbnV0by9hIGluIFNvdWxUYWxrISDwn5qAXCIsXG4gICAgICB3YWl0Rm9ySW5wdXQ6IGZhbHNlLFxuICAgICAgaXNMYXN0OiB0cnVlLFxuICAgICAgZGVsYXk6IDI1MDBcbiAgICB9XG4gIF07XG5cbiAgY29uc3Qgc2Nyb2xsVG9Cb3R0b20gPSAoKSA9PiB7XG4gICAgaWYgKGNoYXRDb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmIChjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsVG8oe1xuICAgICAgICAgICAgdG9wOiBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsSGVpZ2h0LFxuICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGFkZE1lc3NhZ2UgPSAoY29udGVudDogc3RyaW5nLCBpc1VzZXI6IGJvb2xlYW4sIHdpdGhUeXBpbmc6IGJvb2xlYW4gPSBmYWxzZSkgPT4ge1xuICAgIGNvbnN0IG5ld01lc3NhZ2U6IE9uYm9hcmRpbmdNZXNzYWdlID0ge1xuICAgICAgaWQ6IGBvbmJfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gLFxuICAgICAgY29udGVudCxcbiAgICAgIGlzVXNlcixcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgIGlzVHlwaW5nOiB3aXRoVHlwaW5nXG4gICAgfTtcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBuZXdNZXNzYWdlXSk7XG4gICAgc2V0VGltZW91dChzY3JvbGxUb0JvdHRvbSwgNTApO1xuICB9O1xuXG4gIGNvbnN0IGFkZE1lc3NhZ2VXaXRoVHlwaW5nID0gYXN5bmMgKGNvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgIC8vIE1vc3RyYSBpbmRpY2F0b3JlIHR5cGluZ1xuICAgIHNldElzVHlwaW5nKHRydWUpO1xuXG4gICAgLy8gQ2FsY29sYSB0ZW1wbyBkaSB0eXBpbmcgYmFzYXRvIHN1bGxhIGx1bmdoZXp6YSBkZWwgbWVzc2FnZ2lvXG4gICAgY29uc3QgdHlwaW5nVGltZSA9IE1hdGgubWluKE1hdGgubWF4KGNvbnRlbnQubGVuZ3RoICogNTAsIDEwMDApLCAzMDAwKTtcblxuICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCB0eXBpbmdUaW1lKSk7XG5cbiAgICAvLyBOYXNjb25kaSB0eXBpbmcgZSBhZ2dpdW5naSBtZXNzYWdnaW9cbiAgICBzZXRJc1R5cGluZyhmYWxzZSk7XG4gICAgYWRkTWVzc2FnZShjb250ZW50LCBmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgcHJvY2Vzc1N0ZXAgPSBhc3luYyAoKSA9PiB7XG4gICAgY29uc3Qgc3RlcCA9IG9uYm9hcmRpbmdTdGVwc1tjdXJyZW50U3RlcF07XG4gICAgaWYgKCFzdGVwKSByZXR1cm47XG5cbiAgICAvLyBEZXRlcm1pbmEgaWwgbWVzc2FnZ2lvIGRhIG1vc3RyYXJlXG4gICAgbGV0IG1lc3NhZ2VDb250ZW50OiBzdHJpbmc7XG4gICAgaWYgKHR5cGVvZiBzdGVwLm1lc3NhZ2UgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgIG1lc3NhZ2VDb250ZW50ID0gc3RlcC5tZXNzYWdlKHVzZXJEYXRhLm5hbWUsIHVzZXJEYXRhLmFnZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG1lc3NhZ2VDb250ZW50ID0gc3RlcC5tZXNzYWdlO1xuICAgIH1cblxuICAgIC8vIEFnZ2l1bmdpIG1lc3NhZ2dpbyBjb24gZWZmZXR0byB0eXBpbmdcbiAgICBhd2FpdCBhZGRNZXNzYWdlV2l0aFR5cGluZyhtZXNzYWdlQ29udGVudCk7XG5cbiAgICBpZiAoc3RlcC5pc0xhc3QpIHtcbiAgICAgIC8vIENvbXBsZXRhIGwnb25ib2FyZGluZyBjb24gZGVsYXkgcGVyc29uYWxpenphdG9cbiAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgICAgICBvbkNvbXBsZXRlKHVzZXJEYXRhKTtcbiAgICAgIH0sIHN0ZXAuZGVsYXkgfHwgMjUwMCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKCFzdGVwLndhaXRGb3JJbnB1dCkge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNldEN1cnJlbnRTdGVwKHByZXYgPT4gcHJldiArIDEpO1xuICAgICAgfSwgc3RlcC5kZWxheSB8fCAyMDAwKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0SXNXYWl0aW5nKHRydWUpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVVc2VySW5wdXQgPSAoKSA9PiB7XG4gICAgaWYgKCFpbnB1dE1lc3NhZ2UudHJpbSgpIHx8ICFpc1dhaXRpbmcpIHJldHVybjtcblxuICAgIGNvbnN0IHN0ZXAgPSBvbmJvYXJkaW5nU3RlcHNbY3VycmVudFN0ZXBdO1xuXG4gICAgLy8gVmFsaWRhemlvbmUgaW5wdXRcbiAgICBpZiAoc3RlcC52YWxpZGF0b3IpIHtcbiAgICAgIGNvbnN0IGVycm9yID0gc3RlcC52YWxpZGF0b3IoaW5wdXRNZXNzYWdlLnRyaW0oKSk7XG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgYWRkTWVzc2FnZShlcnJvciwgZmFsc2UpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWdnaXVuZ2kgbWVzc2FnZ2lvIHV0ZW50ZVxuICAgIGFkZE1lc3NhZ2UoaW5wdXRNZXNzYWdlLnRyaW0oKSwgdHJ1ZSk7XG5cbiAgICAvLyBQcm9jZXNzYSBsJ2lucHV0XG4gICAgaWYgKHN0ZXAucHJvY2Vzc29yKSB7XG4gICAgICBzdGVwLnByb2Nlc3NvcihpbnB1dE1lc3NhZ2UudHJpbSgpKTtcbiAgICB9XG5cbiAgICBzZXRJbnB1dE1lc3NhZ2UoJycpO1xuICAgIHNldElzV2FpdGluZyhmYWxzZSk7XG5cbiAgICAvLyBWYWkgYWwgcHJvc3NpbW8gc3RlcFxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgc2V0Q3VycmVudFN0ZXAocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgfSwgMTAwMCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBSZWFjdC5LZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmICFlLnNoaWZ0S2V5KSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBoYW5kbGVVc2VySW5wdXQoKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml6aWFsaXp6YXppb25lIHVuYSBzb2xhIHZvbHRhIGFsIG1vdW50XG4gICAgaWYgKCFoYXNTdGFydGVkKSB7XG4gICAgICBzZXRIYXNTdGFydGVkKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ1N0YXJ0aW5nIG9uYm9hcmRpbmcuLi4nKTtcbiAgICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdQcm9jZXNzaW5nIGZpcnN0IHN0ZXAuLi4nKTtcbiAgICAgICAgcHJvY2Vzc1N0ZXAoKTtcbiAgICAgIH0sIDEwMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgfVxuICB9LCBbaGFzU3RhcnRlZCwgcHJvY2Vzc1N0ZXBdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFBlciB0dXR0aSBnbGkgc3RlcCBzdWNjZXNzaXZpIGFsIHByaW1vXG4gICAgaWYgKGN1cnJlbnRTdGVwID4gMCAmJiBjdXJyZW50U3RlcCA8IG9uYm9hcmRpbmdTdGVwcy5sZW5ndGgpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdTdGVwIGNoYW5nZWQgdG86JywgY3VycmVudFN0ZXApO1xuICAgICAgcHJvY2Vzc1N0ZXAoKTtcbiAgICB9XG4gIH0sIFtjdXJyZW50U3RlcCwgcHJvY2Vzc1N0ZXBdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKCk7XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17e1xuICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICB0b3A6IDAsXG4gICAgICBsZWZ0OiAwLFxuICAgICAgcmlnaHQ6IDAsXG4gICAgICBib3R0b206IDAsXG4gICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjk1KScsXG4gICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICAgIHpJbmRleDogMTUwMFxuICAgIH19PlxuICAgICAgey8qIEhlYWRlciBjb24gcHJvZ3Jlc3MgYmFyICovfVxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBwYWRkaW5nOiAnMXJlbScsXG4gICAgICAgIGJvcmRlckJvdHRvbTogJzFweCBzb2xpZCByZ2JhKDI1NSwyNTUsMjU1LDAuMSknLFxuICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjMpJ1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLCBnYXA6ICcxcmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgd2lkdGg6ICc1MHB4JyxcbiAgICAgICAgICAgIGhlaWdodDogJzUwcHgnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCAjOGI1Y2Y2IDAlLCB0cmFuc3BhcmVudCA3MCUpYCxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgZm9udFNpemU6ICcxLjVyZW0nLFxuICAgICAgICAgICAgYW5pbWF0aW9uOiBpc1R5cGluZyA/ICdwdWxzZSAxLjVzIGVhc2UtaW4tb3V0IGluZmluaXRlJyA6ICdub25lJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAg8J+Mn1xuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnbGVmdCcgfX0+XG4gICAgICAgICAgICA8aDIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4yNXJlbScsXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICBtYXJnaW46IDAsXG4gICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzAuMjVyZW0nXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgQ29uZmlndXJhemlvbmUgSW5pemlhbGVcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBzdHlsZT17e1xuICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC43KScsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICBTb3VsIHZ1b2xlIGNvbm9zY2VydGkgbWVnbGlvXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgIGhlaWdodDogJzRweCcsXG4gICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC4xKScsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMnB4JyxcbiAgICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbidcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgd2lkdGg6IGAkeyhjdXJyZW50U3RlcCAvIHRvdGFsU3RlcHMpICogMTAwfSVgLFxuICAgICAgICAgICAgaGVpZ2h0OiAnMTAwJScsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjOGI1Y2Y2LCAjYTg1NWY3KScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcycHgnLFxuICAgICAgICAgICAgdHJhbnNpdGlvbjogJ3dpZHRoIDAuNXMgZWFzZS1pbi1vdXQnXG4gICAgICAgICAgfX0gLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgbWFyZ2luVG9wOiAnMC41cmVtJyxcbiAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC42KScsXG4gICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJ1xuICAgICAgICB9fT5cbiAgICAgICAgICBTdGVwIHtjdXJyZW50U3RlcCArIDF9IGRpIHt0b3RhbFN0ZXBzICsgMX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1lc3NhZ2VzICovfVxuICAgICAgPGRpdiBcbiAgICAgICAgcmVmPXtjaGF0Q29udGFpbmVyUmVmfVxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgb3ZlcmZsb3dZOiAnYXV0bycsXG4gICAgICAgICAgcGFkZGluZzogJzFyZW0nLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICAgICAgICBnYXA6ICcxcmVtJ1xuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7LyogRGVidWcgaW5mbyAqL31cbiAgICAgICAge21lc3NhZ2VzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC41KScsIHRleHRBbGlnbjogJ2NlbnRlcicsIHBhZGRpbmc6ICcycmVtJyB9fT5cbiAgICAgICAgICAgIEluaXppYWxpenphbmRvIGNvbnZlcnNhemlvbmUuLi4gKFN0ZXA6IHtjdXJyZW50U3RlcH0sIE1lc3NhZ2VzOiB7bWVzc2FnZXMubGVuZ3RofSlcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgPENoYXRCdWJibGVcbiAgICAgICAgICAgIGtleT17bWVzc2FnZS5pZH1cbiAgICAgICAgICAgIG1lc3NhZ2U9e21lc3NhZ2UuY29udGVudH1cbiAgICAgICAgICAgIGlzVXNlcj17bWVzc2FnZS5pc1VzZXJ9XG4gICAgICAgICAgLz5cbiAgICAgICAgKSl9XG5cbiAgICAgICAgey8qIEluZGljYXRvcmUgdHlwaW5nICovfVxuICAgICAgICB7aXNUeXBpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2ZsZXgtc3RhcnQnLCBtYXJnaW5Cb3R0b206ICcxcmVtJyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC4xKScsXG4gICAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigxMHB4KScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzFyZW0nLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMC43NXJlbSAxcmVtJyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LDI1NSwyNTUsMC4yKScsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGdhcDogJzAuNXJlbSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMC4yNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyM4YjVjZjYnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogJ2JvdW5jZSAxLjRzIGVhc2UtaW4tb3V0IGluZmluaXRlJ1xuICAgICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyM4YjVjZjYnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogJ2JvdW5jZSAxLjRzIGVhc2UtaW4tb3V0IGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbkRlbGF5OiAnMC4ycydcbiAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogJzZweCcsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjOGI1Y2Y2JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICBhbmltYXRpb246ICdib3VuY2UgMS40cyBlYXNlLWluLW91dCBpbmZpbml0ZScsXG4gICAgICAgICAgICAgICAgICBhbmltYXRpb25EZWxheTogJzAuNHMnXG4gICAgICAgICAgICAgICAgfX0gLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjgpJywgZm9udFNpemU6ICcwLjg3NXJlbScgfX0+XG4gICAgICAgICAgICAgICAgU291bCBzdGEgc2NyaXZlbmRvLi4uXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBJbnB1dCBBcmVhIE1pZ2xpb3JhdGEgKi99XG4gICAgICB7aXNXYWl0aW5nICYmIChcbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHBhZGRpbmc6ICcxcmVtJyxcbiAgICAgICAgICBib3JkZXJUb3A6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjEpJyxcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjMpJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsMjU1LDI1NSwwLjA1KScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxcmVtJyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0nLFxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LDI1NSwyNTUsMC4xKScsXG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICBnYXA6ICcwLjVyZW0nLFxuICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcidcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHZhbHVlPXtpbnB1dE1lc3NhZ2V9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNjcml2aSBsYSB0dWEgcmlzcG9zdGEuLi5cIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNzVyZW0gMXJlbScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMC43NXJlbScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC4xKScsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICBvdXRsaW5lOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDEwcHgpJ1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBhdXRvRm9jdXNcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVVzZXJJbnB1dH1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFpbnB1dE1lc3NhZ2UudHJpbSgpfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHdpZHRoOiAnNDhweCcsXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDhweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBpbnB1dE1lc3NhZ2UudHJpbSgpXG4gICAgICAgICAgICAgICAgICA/ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjOGI1Y2Y2LCAjYTg1NWY3KSdcbiAgICAgICAgICAgICAgICAgIDogJ3JnYmEoMTM5LCA5MiwgMjQ2LCAwLjMpJyxcbiAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMnJlbScsXG4gICAgICAgICAgICAgICAgY3Vyc29yOiBpbnB1dE1lc3NhZ2UudHJpbSgpID8gJ3BvaW50ZXInIDogJ25vdC1hbGxvd2VkJyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IGlucHV0TWVzc2FnZS50cmltKCkgPyAnc2NhbGUoMSknIDogJ3NjYWxlKDAuOSknXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOKGklxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgICAgbWFyZ2luVG9wOiAnMC43NXJlbScsXG4gICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC41KScsXG4gICAgICAgICAgICBmb250U2l6ZTogJzAuNzVyZW0nXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICBQcmVtaSBJbnZpbyBwZXIgaW52aWFyZVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBPbmJvYXJkaW5nQ2hhdDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwiQ2hhdEJ1YmJsZSIsIk9uYm9hcmRpbmdDaGF0IiwidXNlcklkIiwib25Db21wbGV0ZSIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJjdXJyZW50U3RlcCIsInNldEN1cnJlbnRTdGVwIiwiaW5wdXRNZXNzYWdlIiwic2V0SW5wdXRNZXNzYWdlIiwiaXNXYWl0aW5nIiwic2V0SXNXYWl0aW5nIiwiaXNUeXBpbmciLCJzZXRJc1R5cGluZyIsInVzZXJEYXRhIiwic2V0VXNlckRhdGEiLCJuYW1lIiwiYWdlIiwiY2hhdENvbnRhaW5lclJlZiIsInRvdGFsU3RlcHMiLCJvbmJvYXJkaW5nU3RlcHMiLCJtZXNzYWdlIiwid2FpdEZvcklucHV0IiwiZGVsYXkiLCJ2YWxpZGF0b3IiLCJpbnB1dCIsInRyaW0iLCJsZW5ndGgiLCJ0ZXN0IiwicHJvY2Vzc29yIiwicHJldiIsInBhcnNlSW50IiwiaXNOYU4iLCJpc0xhc3QiLCJzY3JvbGxUb0JvdHRvbSIsImN1cnJlbnQiLCJzZXRUaW1lb3V0Iiwic2Nyb2xsVG8iLCJ0b3AiLCJzY3JvbGxIZWlnaHQiLCJiZWhhdmlvciIsImFkZE1lc3NhZ2UiLCJjb250ZW50IiwiaXNVc2VyIiwid2l0aFR5cGluZyIsIm5ld01lc3NhZ2UiLCJpZCIsIk1hdGgiLCJEYXRlIiwibm93IiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJ0aW1lc3RhbXAiLCJhZGRNZXNzYWdlV2l0aFR5cGluZyIsInR5cGluZ1RpbWUiLCJtaW4iLCJtYXgiLCJQcm9taXNlIiwicmVzb2x2ZSIsInByb2Nlc3NTdGVwIiwic3RlcCIsIm1lc3NhZ2VDb250ZW50IiwiaGFuZGxlVXNlcklucHV0IiwiZXJyb3IiLCJoYW5kbGVLZXlEb3duIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJoYXNTdGFydGVkIiwic2V0SGFzU3RhcnRlZCIsImNvbnNvbGUiLCJsb2ciLCJ0aW1lciIsImNsZWFyVGltZW91dCIsImRpdiIsInN0eWxlIiwicG9zaXRpb24iLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJiYWNrZ3JvdW5kIiwiZGlzcGxheSIsImZsZXhEaXJlY3Rpb24iLCJ6SW5kZXgiLCJwYWRkaW5nIiwiYm9yZGVyQm90dG9tIiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiZ2FwIiwibWFyZ2luQm90dG9tIiwid2lkdGgiLCJoZWlnaHQiLCJib3JkZXJSYWRpdXMiLCJmb250U2l6ZSIsImFuaW1hdGlvbiIsInRleHRBbGlnbiIsImgyIiwiY29sb3IiLCJmb250V2VpZ2h0IiwibWFyZ2luIiwicCIsIm92ZXJmbG93IiwidHJhbnNpdGlvbiIsIm1hcmdpblRvcCIsInJlZiIsImZsZXgiLCJvdmVyZmxvd1kiLCJtYXAiLCJiYWNrZHJvcEZpbHRlciIsImJvcmRlciIsImJhY2tncm91bmRDb2xvciIsImFuaW1hdGlvbkRlbGF5Iiwic3BhbiIsImJvcmRlclRvcCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib25LZXlEb3duIiwicGxhY2Vob2xkZXIiLCJvdXRsaW5lIiwiYXV0b0ZvY3VzIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiY3Vyc29yIiwidHJhbnNmb3JtIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});