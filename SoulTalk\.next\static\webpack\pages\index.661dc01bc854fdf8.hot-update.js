"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-browser)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-browser)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _components_ProfileCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ProfileCard */ \"(pages-dir-browser)/./src/components/ProfileCard.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_10__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_9__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se non è autenticato, mostra sempre il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Caricamento...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.9)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Caricando i tuoi dati...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowProfile(true),\n                        style: {\n                            position: 'absolute',\n                            top: '1rem',\n                            right: '1rem',\n                            zIndex: 10,\n                            width: '45px',\n                            height: '45px',\n                            borderRadius: '50%',\n                            background: 'rgba(255,255,255,0.1)',\n                            backdropFilter: 'blur(10px)',\n                            border: '1px solid rgba(255,255,255,0.2)',\n                            color: 'white',\n                            fontSize: '1.1rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.background = 'rgba(255,255,255,0.2)';\n                            e.currentTarget.style.transform = 'scale(1.05)';\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.background = 'rgba(255,255,255,0.1)';\n                            e.currentTarget.style.transform = 'scale(1)';\n                        },\n                        children: userMemory.name ? userMemory.name.charAt(0).toUpperCase() : '👤'\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showProfile,\n                onClose: ()=>setShowProfile(false),\n                userName: userMemory.name || 'Utente',\n                intimacyLevel: userMemory.intimacyLevel,\n                messageCount: messages.length,\n                userId: userId,\n                onLogout: logout,\n                onReset: handleReset\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 565,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"v3AuwDN++fufj3+yt7Ec5dDksYo=\", false, function() {\n    return [\n        _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_10__.useSupabaseMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9wYWdlcy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkQ7QUFDOUI7QUFDcUI7QUFDQTtBQUMyQjtBQUMvQjtBQUNZO0FBQ047QUFDRTtBQUNTO0FBRWhELFNBQVNhOztJQUN0QixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHZCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNlLGFBQWFDLGVBQWUsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2lCLGFBQWFDLGVBQWUsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ21CLGFBQWFDLGVBQWUsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1xQixXQUFXcEIsNkNBQU1BLENBQXNCO0lBQzdDLE1BQU1xQixtQkFBbUJyQiw2Q0FBTUEsQ0FBaUI7SUFFaEQsMENBQTBDO0lBQzFDLE1BQU0sRUFDSnNCLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxVQUFVLEVBQ1ZDLFdBQVdDLGFBQWEsRUFDeEJDLFFBQVEsRUFDUkMsZUFBZSxFQUNmQyxlQUFlLEVBQ2ZDLFVBQVUsRUFDVkMsZ0JBQWdCLEVBQ2hCQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQkMsTUFBTSxFQUNOQywwQkFBMEIsRUFDM0IsR0FBR3pCLDRFQUFpQkE7SUFFckIsTUFBTTBCLGlCQUFpQjtRQUNyQixJQUFJZixpQkFBaUJnQixPQUFPLEVBQUU7WUFDNUJDLFdBQVc7Z0JBQ1QsSUFBSWpCLGlCQUFpQmdCLE9BQU8sRUFBRTtvQkFDNUJoQixpQkFBaUJnQixPQUFPLENBQUNFLFFBQVEsQ0FBQzt3QkFDaENDLEtBQUtuQixpQkFBaUJnQixPQUFPLENBQUNJLFlBQVk7d0JBQzFDQyxVQUFVO29CQUNaO2dCQUNGO1lBQ0YsR0FBRztRQUNMO0lBQ0Y7SUFFQSw4RUFBOEU7SUFDOUUsTUFBTUMsZUFBZTtRQUNuQixNQUFNQyxTQUFTO1lBQ2I7WUFDQTtZQUNBO1lBQ0E7WUFDQSxVQUFXLGdCQUFnQjtTQUM1QjtRQUNELE9BQU9BLE1BQU0sQ0FBQ3BCLFdBQVdxQixhQUFhLENBQUMsSUFBSUQsTUFBTSxDQUFDLEVBQUU7SUFDdEQ7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTUUsdUJBQXVCO1FBQzNCLE1BQU1ELGdCQUFnQnJCLFdBQVdxQixhQUFhO1FBRTlDLE1BQU1FLG9CQUFvQjtZQUN4QiwrQ0FBK0M7WUFDL0M7Z0JBQ0VILFFBQVE7b0JBQUM7b0JBQTRCO29CQUEyQjtpQkFBMkI7Z0JBQzNGSSxjQUFjO1lBQ2hCO1lBQ0EsMENBQTBDO1lBQzFDO2dCQUNFSixRQUFRO29CQUFDO29CQUEyQjtvQkFBNEI7aUJBQTBCO2dCQUMxRkksY0FBYztZQUNoQjtZQUNBLG9DQUFvQztZQUNwQztnQkFDRUosUUFBUTtvQkFBQztvQkFBNEI7b0JBQTJCO2lCQUEwQjtnQkFDMUZJLGNBQWM7WUFDaEI7WUFDQSwwQ0FBMEM7WUFDMUM7Z0JBQ0VKLFFBQVE7b0JBQUM7b0JBQTJCO29CQUEyQjtpQkFBMkI7Z0JBQzFGSSxjQUFjO1lBQ2hCO1lBQ0EsMENBQTBDO1lBQzFDO2dCQUNFSixRQUFRO29CQUFDO29CQUEyQjtvQkFBNEI7aUJBQTRCO2dCQUM1RkksY0FBYztZQUNoQjtTQUNEO1FBRUQsTUFBTUMsU0FBU0YsaUJBQWlCLENBQUNGLGNBQWMsSUFBSUUsaUJBQWlCLENBQUMsRUFBRTtRQUV2RSxPQUFPO1lBQ0xHLFlBQVksZ0RBRTJCRCxPQURBQSxPQUFPTCxNQUFNLENBQUMsRUFBRSxFQUFDLHVFQUVqQkssT0FEQUEsT0FBT0wsTUFBTSxDQUFDLEVBQUUsRUFBQyx1RUFFcERLLE9BRG1DQSxPQUFPTCxNQUFNLENBQUMsRUFBRSxFQUFDLG9DQUNoQyxPQUFwQkssT0FBT0QsWUFBWSxFQUFDO1lBRXhCRyxnQkFBZ0I7WUFDaEJDLHNCQUFzQjtZQUN0QkMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7SUFDRjtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNQyx5QkFBeUIsT0FBT0MsYUFBcUJDO1FBQ3pELE1BQU1DLG1CQUFtQkYsWUFBWUcsV0FBVztRQUNoRCxNQUFNQyxVQUFlLENBQUM7UUFFdEIsMkJBQTJCO1FBQzNCLElBQUlGLGlCQUFpQkcsUUFBUSxDQUFDLGdCQUFnQkgsaUJBQWlCRyxRQUFRLENBQUMsVUFBVTtZQUNoRixNQUFNQyxZQUFZTixZQUFZTyxLQUFLLENBQUM7WUFDcEMsSUFBSUQsYUFBYSxDQUFDdEMsV0FBV3dDLElBQUksRUFBRTtnQkFDakMsTUFBTUEsT0FBT0YsU0FBUyxDQUFDLEVBQUUsSUFBSUEsU0FBUyxDQUFDLEVBQUU7Z0JBQ3pDRixRQUFRSSxJQUFJLEdBQUdBO1lBQ2pCO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsTUFBTUMsV0FBV1QsWUFBWU8sS0FBSyxDQUFDO1FBQ25DLElBQUlFLFlBQVksQ0FBQ3pDLFdBQVcwQyxHQUFHLEVBQUU7WUFDL0IsTUFBTUEsTUFBTUMsU0FBU0YsUUFBUSxDQUFDLEVBQUUsSUFBSUEsUUFBUSxDQUFDLEVBQUU7WUFDL0MsSUFBSUMsTUFBTSxLQUFLQSxNQUFNLEtBQUs7Z0JBQ3hCTixRQUFRTSxHQUFHLEdBQUdBO1lBQ2hCO1FBQ0Y7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTUUsV0FBVztZQUNmLFVBQVU7Z0JBQUM7Z0JBQVU7Z0JBQVk7Z0JBQVc7YUFBVTtZQUN0RCxVQUFVO2dCQUFDO2dCQUFVO2dCQUFZO2dCQUFPO2FBQU87WUFDL0MsY0FBYztnQkFBQztnQkFBYztnQkFBVzthQUFZO1lBQ3BELGVBQWU7Z0JBQUM7Z0JBQWU7Z0JBQVc7YUFBVTtZQUNwRCxZQUFZO2dCQUFDO2dCQUFZO2dCQUFjO2FBQVM7WUFDaEQsU0FBUztnQkFBQztnQkFBUztnQkFBYzthQUFTO1FBQzVDO1FBRUEsS0FBSyxNQUFNLENBQUNDLFNBQVNDLFNBQVMsSUFBSUMsT0FBT0MsT0FBTyxDQUFDSixVQUFXO1lBQzFELElBQUlFLFNBQVNHLElBQUksQ0FBQ0MsQ0FBQUEsVUFBV2hCLGlCQUFpQkcsUUFBUSxDQUFDYSxXQUFXO2dCQUNoRSxJQUFJLENBQUNsRCxXQUFXNEMsUUFBUSxDQUFDUCxRQUFRLENBQUNRLFVBQVU7b0JBQzFDVCxRQUFRUSxRQUFRLEdBQUc7MkJBQUk1QyxXQUFXNEMsUUFBUTt3QkFBRUM7cUJBQVE7Z0JBQ3REO2dCQUNBO1lBQ0Y7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJYixZQUFZbUIsTUFBTSxHQUFHLE9BQ3JCakIsaUJBQWlCRyxRQUFRLENBQUMsZ0JBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxjQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsZUFDMUJILGlCQUFpQkcsUUFBUSxDQUFDLFlBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxlQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsaUJBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxZQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsVUFBVTtZQUV0QyxNQUFNZSxXQUFXQyxLQUFLQyxHQUFHLENBQUN0RCxXQUFXcUIsYUFBYSxHQUFHLEdBQUc7WUFFeEQsc0RBQXNEO1lBQ3RELElBQUkrQixXQUFXcEQsV0FBV3FCLGFBQWEsRUFBRTtnQkFDdkNrQyxRQUFRQyxHQUFHLENBQUMsa0RBQWlELE9BQVRKLFVBQVM7Z0JBQzdEaEIsUUFBUWYsYUFBYSxHQUFHK0I7WUFDMUI7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJTCxPQUFPVSxJQUFJLENBQUNyQixTQUFTZSxNQUFNLEdBQUcsR0FBRztZQUNuQzVDLGlCQUFpQjZCO1FBQ25CO0lBQ0Y7SUFFQTNELGdEQUFTQTswQkFBQztZQUNSbUM7UUFDRjt5QkFBRztRQUFDYjtRQUFVVDtLQUFZO0lBRTFCLDZEQUE2RDtJQUM3RGIsZ0RBQVNBOzBCQUFDO1lBQ1IsSUFBSXNCLFNBQVNvRCxNQUFNLEdBQUcsR0FBRztnQkFDdkIxRCxlQUFlO1lBQ2pCO1FBQ0Y7eUJBQUc7UUFBQ007S0FBUztJQUViLGtFQUFrRTtJQUNsRXRCLGdEQUFTQTswQkFBQztZQUNSLE1BQU1pRixrQkFBa0JwQztZQUN4QixNQUFNcUMsT0FBT0MsU0FBU0QsSUFBSTtZQUUxQix3REFBd0Q7WUFDeERaLE9BQU9jLE1BQU0sQ0FBQ0YsS0FBS0csS0FBSyxFQUFFSjtZQUUxQix1REFBdUQ7WUFDdkRDLEtBQUtJLFNBQVMsR0FBRyxrQkFBMkMsT0FBekIvRCxXQUFXcUIsYUFBYTtZQUUzRCxzRUFBc0U7WUFDdEU7a0NBQU87Z0JBQ0wsK0NBQStDO2dCQUNqRDs7UUFDRjt5QkFBRztRQUFDckIsV0FBV3FCLGFBQWE7S0FBQztJQUU3QixNQUFNMkMsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQzVFLGFBQWE2RSxJQUFJLE1BQU0zRSxhQUFhO1FBRXpDLE1BQU0wQyxjQUFjNUMsYUFBYTZFLElBQUk7UUFDckM1RSxnQkFBZ0I7UUFDaEJFLGVBQWU7UUFFZixtREFBbUQ7UUFDbkQsTUFBTTJFLFVBQVUsTUFBTTVELFdBQVcwQixhQUFhO1FBQzlDdUIsUUFBUUMsR0FBRyxDQUFDLCtDQUErQ3pELFNBQVNvRCxNQUFNLEdBQUc7UUFFN0UsZ0RBQWdEO1FBQ2hEckMsV0FBV0YsZ0JBQWdCO1FBRTNCLElBQUk7WUFDRixzRUFBc0U7WUFDdEUsTUFBTXVELHNCQUFzQjttQkFBSXBFO2dCQUFVbUU7YUFBUSxDQUFDRSxHQUFHLENBQUNDLENBQUFBLE1BQVE7b0JBQzdEQyxNQUFNRCxJQUFJRSxNQUFNLEdBQUcsU0FBa0I7b0JBQ3JDQyxTQUFTSCxJQUFJRyxPQUFPO2dCQUN0QjtZQUVBLE1BQU1DLFdBQVcsTUFBTXhGLGtFQUFlQSxDQUNwQytDLGFBQ0FtQyxxQkFDQTtnQkFDRTNCLE1BQU14QyxXQUFXd0MsSUFBSSxJQUFJa0M7Z0JBQ3pCaEMsS0FBSzFDLFdBQVcwQyxHQUFHLElBQUlnQztnQkFDdkJDLFFBQVEzRSxXQUFXMkUsTUFBTTtnQkFDekIvQixVQUFVNUMsV0FBVzRDLFFBQVE7Z0JBQzdCdkIsZUFBZXJCLFdBQVdxQixhQUFhO1lBQ3pDO1lBR0YsK0JBQStCO1lBQy9CLE1BQU1mLFdBQVdtRSxVQUFVO1lBQzNCbEIsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ3pELFNBQVNvRCxNQUFNLEdBQUc7WUFFeEUsa0RBQWtEO1lBQ2xELE1BQU1wQix1QkFBdUJDLGFBQWF5QztRQUU1QyxFQUFFLE9BQU9HLE9BQU87WUFDZHJCLFFBQVFxQixLQUFLLENBQUMsVUFBVUE7WUFDeEIsTUFBTXRFLFdBQVcsaURBQWlEO1FBQ3BFLFNBQVU7WUFDUmYsZUFBZTtRQUNqQjtJQUNGO0lBRUEsTUFBTXNGLGdCQUFnQixDQUFDQztRQUNyQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7WUFDcENGLEVBQUVHLGNBQWM7WUFDaEJqQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNa0IsY0FBYztRQUNsQixJQUFJQyxRQUFRLHNHQUFzRztZQUNoSCxNQUFNM0U7WUFDTmYsZUFBZTtRQUNqQjtJQUNGO0lBRUEsdURBQXVEO0lBQ3ZELElBQUksQ0FBQ1csaUJBQWlCO1FBQ3BCLHFCQUNFOzs4QkFDRSw4REFBQzFCLGtEQUFJQTs7c0NBQ0gsOERBQUMwRztzQ0FBTTs7Ozs7O3NDQUNQLDhEQUFDQzs0QkFBSzdDLE1BQUs7NEJBQWNnQyxTQUFROzs7Ozs7c0NBQ2pDLDhEQUFDYTs0QkFBSzdDLE1BQUs7NEJBQVdnQyxTQUFROzs7Ozs7c0NBQzlCLDhEQUFDYzs0QkFBS0MsS0FBSTs0QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzhCQUV4Qiw4REFBQzFHLDREQUFRQTtvQkFBQzJHLGVBQWVoRjs7Ozs7Ozs7SUFHL0I7SUFFQSwyRUFBMkU7SUFDM0UsSUFBSVAsZUFBZTtRQUNqQixxQkFDRTs7OEJBQ0UsOERBQUN4QixrREFBSUE7O3NDQUNILDhEQUFDMEc7c0NBQU07Ozs7OztzQ0FDUCw4REFBQ0M7NEJBQUs3QyxNQUFLOzRCQUFXZ0MsU0FBUTs7Ozs7O3NDQUM5Qiw4REFBQ2M7NEJBQUtDLEtBQUk7NEJBQU9DLE1BQUs7Ozs7Ozs7Ozs7Ozs4QkFFeEIsOERBQUNFO29CQUFJNUIsT0FBTzt3QkFDVjZCLFVBQVU7d0JBQ1YzRSxLQUFLO3dCQUNMNEUsTUFBTTt3QkFDTkMsT0FBTzt3QkFDUEMsUUFBUTt3QkFDUnBFLFlBQVk7d0JBQ1pxRSxTQUFTO3dCQUNUQyxZQUFZO3dCQUNaQyxnQkFBZ0I7d0JBQ2hCQyxRQUFRO29CQUNWOzhCQUNFLDRFQUFDUjt3QkFBSTVCLE9BQU87NEJBQUVxQyxPQUFPOzRCQUFTQyxXQUFXO3dCQUFTOzswQ0FDaEQsOERBQUNWO2dDQUFJNUIsT0FBTztvQ0FBRXVDLFVBQVU7b0NBQVFDLGNBQWM7Z0NBQU87MENBQUc7Ozs7OzswQ0FDeEQsOERBQUNaOzBDQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS2Y7SUFFQSxnRUFBZ0U7SUFDaEUsSUFBSXJGLGlCQUFpQjtRQUNuQixxQkFDRTs7OEJBQ0UsOERBQUMzQixrREFBSUE7O3NDQUNILDhEQUFDMEc7c0NBQU07Ozs7OztzQ0FDUCw4REFBQ0M7NEJBQUs3QyxNQUFLOzRCQUFjZ0MsU0FBUTs7Ozs7O3NDQUNqQyw4REFBQ2E7NEJBQUs3QyxNQUFLOzRCQUFXZ0MsU0FBUTs7Ozs7O3NDQUM5Qiw4REFBQ2M7NEJBQUtDLEtBQUk7NEJBQU9DLE1BQUs7Ozs7Ozs7Ozs7Ozs4QkFFeEIsOERBQUN6RyxrRUFBY0E7b0JBQ2JlLFFBQVFBO29CQUNSeUcsWUFBWTVGOzs7Ozs7OztJQUlwQjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ2pDLGtEQUFJQTs7a0NBQ0gsOERBQUMwRztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBSzdDLE1BQUs7d0JBQWNnQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDYTt3QkFBSzdDLE1BQUs7d0JBQVdnQyxTQUFROzs7Ozs7a0NBQzlCLDhEQUFDYzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUd4Qiw4REFBQ0U7Z0JBQUkzQixXQUFVOztvQkFFWDdELENBQUFBLGlCQUFpQixDQUFDSixNQUFLLG1CQUN2Qiw4REFBQzRGO3dCQUFJNUIsT0FBTzs0QkFDVjZCLFVBQVU7NEJBQ1YzRSxLQUFLOzRCQUNMNEUsTUFBTTs0QkFDTkMsT0FBTzs0QkFDUEMsUUFBUTs0QkFDUnBFLFlBQVk7NEJBQ1pxRSxTQUFTOzRCQUNUQyxZQUFZOzRCQUNaQyxnQkFBZ0I7NEJBQ2hCQyxRQUFRO3dCQUNWO2tDQUNFLDRFQUFDUjs0QkFBSTVCLE9BQU87Z0NBQUVxQyxPQUFPO2dDQUFTQyxXQUFXOzRCQUFTOzs4Q0FDaEQsOERBQUNWO29DQUFJNUIsT0FBTzt3Q0FBRXVDLFVBQVU7d0NBQVFDLGNBQWM7b0NBQU87OENBQUc7Ozs7Ozs4Q0FDeEQsOERBQUNaOzhDQUFLLENBQUM1RixTQUFTLHNCQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTVDLDhEQUFDMEc7d0JBQ0NDLFNBQVMsSUFBTTlHLGVBQWU7d0JBQzlCbUUsT0FBTzs0QkFDTDZCLFVBQVU7NEJBQ1YzRSxLQUFLOzRCQUNMNkUsT0FBTzs0QkFDUEssUUFBUTs0QkFDUlEsT0FBTzs0QkFDUEMsUUFBUTs0QkFDUkMsY0FBYzs0QkFDZGxGLFlBQVk7NEJBQ1ptRixnQkFBZ0I7NEJBQ2hCQyxRQUFROzRCQUNSWCxPQUFPOzRCQUNQRSxVQUFVOzRCQUNWVSxZQUFZOzRCQUNaQyxRQUFROzRCQUNSbEYsWUFBWTs0QkFDWmlFLFNBQVM7NEJBQ1RDLFlBQVk7NEJBQ1pDLGdCQUFnQjt3QkFDbEI7d0JBQ0FnQixjQUFjLENBQUNuQzs0QkFDYkEsRUFBRW9DLGFBQWEsQ0FBQ3BELEtBQUssQ0FBQ3BDLFVBQVUsR0FBRzs0QkFDbkNvRCxFQUFFb0MsYUFBYSxDQUFDcEQsS0FBSyxDQUFDcUQsU0FBUyxHQUFHO3dCQUNwQzt3QkFDQUMsY0FBYyxDQUFDdEM7NEJBQ2JBLEVBQUVvQyxhQUFhLENBQUNwRCxLQUFLLENBQUNwQyxVQUFVLEdBQUc7NEJBQ25Db0QsRUFBRW9DLGFBQWEsQ0FBQ3BELEtBQUssQ0FBQ3FELFNBQVMsR0FBRzt3QkFDcEM7a0NBRUNuSCxXQUFXd0MsSUFBSSxHQUFHeEMsV0FBV3dDLElBQUksQ0FBQzZFLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUs7Ozs7OztrQ0FJL0QsOERBQUM1Qjt3QkFBSTNCLFdBQVU7OzBDQUNiLDhEQUFDMkI7Z0NBQUk1QixPQUFPO29DQUFFaUMsU0FBUztvQ0FBUUMsWUFBWTtvQ0FBVXVCLEtBQUs7Z0NBQU87O2tEQUMvRCw4REFBQzdCO3dDQUFJNUIsT0FBTzs0Q0FBRWlDLFNBQVM7NENBQVFDLFlBQVk7NENBQVV1QixLQUFLO3dDQUFVOzswREFDbEUsOERBQUM3QjtnREFDQzVCLE9BQU87b0RBQ0w0QyxPQUFPO29EQUNQQyxRQUFRO29EQUNSQyxjQUFjO29EQUNkbEYsWUFBWSwyQkFBMEMsT0FBZlAsZ0JBQWU7b0RBQ3RENEUsU0FBUztvREFDVEMsWUFBWTtvREFDWkMsZ0JBQWdCO29EQUNoQkksVUFBVTtvREFDVnhFLFdBQVc7Z0RBQ2I7MERBQ0Q7Ozs7OzswREFHRCw4REFBQzZEOztrRUFDQyw4REFBQzhCO3dEQUFHMUQsT0FBTzs0REFBRXFDLE9BQU87NERBQVNFLFVBQVU7NERBQVVVLFlBQVk7NERBQVFVLFFBQVE7d0RBQUU7a0VBQUc7Ozs7OztrRUFHbEYsOERBQUNDO3dEQUFFNUQsT0FBTzs0REFBRXFDLE9BQU87NERBQXlCRSxVQUFVOzREQUFXb0IsUUFBUTt3REFBRTtrRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtsRiw4REFBQzVJLHNFQUFlQTt3Q0FBQ3dDLGVBQWVyQixXQUFXcUIsYUFBYTs7Ozs7Ozs7Ozs7OzBDQUUxRCw4REFBQ3FFO2dDQUFJNUIsT0FBTztvQ0FBRWlDLFNBQVM7b0NBQVF3QixLQUFLO2dDQUFPOztrREFDekMsOERBQUNmO3dDQUFPQyxTQUFTL0Y7d0NBQVFvRCxPQUFPOzRDQUFFcUMsT0FBTzs0Q0FBeUJ6RSxZQUFZOzRDQUFRb0YsUUFBUTs0Q0FBUUUsUUFBUTs0Q0FBV1gsVUFBVTt3Q0FBVztrREFBRzs7Ozs7O2tEQUdqSiw4REFBQ0c7d0NBQU9DLFNBQVN2Qjt3Q0FBYXBCLE9BQU87NENBQUVxQyxPQUFPOzRDQUFTekUsWUFBWTs0Q0FBUW9GLFFBQVE7NENBQVFFLFFBQVE7NENBQVdYLFVBQVU7d0NBQVc7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPMUksOERBQUNYO3dCQUFJNUIsT0FBTzs0QkFBRTZELE1BQU07NEJBQUdDLFNBQVM7d0JBQVM7a0NBQ3ZDLDRFQUFDaEosOERBQVVBOzRCQUFDaUosV0FBVzFHOzRCQUFnQjRDLFdBQVU7c0NBQy9DLDRFQUFDMkI7Z0NBQ0NvQyxLQUFLakk7Z0NBQ0xrRSxXQUFVO2dDQUNWRCxPQUFPO29DQUFFNkQsTUFBTTtvQ0FBR0ksV0FBVztvQ0FBUUgsU0FBUztnQ0FBTzs7b0NBR3BEcEksNkJBQ0MsOERBQUNrRzt3Q0FBSTVCLE9BQU87NENBQUVzQyxXQUFXOzRDQUFVd0IsU0FBUzt3Q0FBVztrREFDckQsNEVBQUNsQzs0Q0FBSTNCLFdBQVU7NENBQWVELE9BQU87Z0RBQ25Da0UsVUFBVTtnREFDVlAsUUFBUTtnREFDUkcsU0FBUztnREFDVGhCLGNBQWM7Z0RBQ2RsRixZQUFZO2dEQUNabUYsZ0JBQWdCO2dEQUNoQkMsUUFBUTs0Q0FDVjs7OERBQ0UsOERBQUNtQjtvREFBR25FLE9BQU87d0RBQ1RxQyxPQUFPO3dEQUNQRSxVQUFVO3dEQUNWVSxZQUFZO3dEQUNaVCxjQUFjO3dEQUNkbUIsUUFBUTtvREFDVjs4REFBRzs7Ozs7OzhEQUdILDhEQUFDQztvREFBRTVELE9BQU87d0RBQ1JxQyxPQUFPO3dEQUNQRSxVQUFVO3dEQUNWNkIsWUFBWTt3REFDWlQsUUFBUTtvREFDVjs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU1IxSCxTQUFTcUUsR0FBRyxDQUFDLENBQUMrRCx3QkFDYiw4REFBQ3hKLDhEQUFVQTs0Q0FFVHdKLFNBQVNBLFFBQVEzRCxPQUFPOzRDQUN4QkQsUUFBUTRELFFBQVE1RCxNQUFNOzJDQUZqQjRELFFBQVFDLEVBQUU7Ozs7O29DQU9sQjlJLDZCQUNDLDhEQUFDb0c7d0NBQUk1QixPQUFPOzRDQUFFaUMsU0FBUzs0Q0FBUUUsZ0JBQWdCO3dDQUFhO2tEQUMxRCw0RUFBQ1A7NENBQUkzQixXQUFVOzRDQUFlRCxPQUFPO2dEQUNuQzhELFNBQVM7Z0RBQ1RoQixjQUFjO2dEQUNkbEYsWUFBWTtnREFDWm1GLGdCQUFnQjs0Q0FDbEI7c0RBQ0UsNEVBQUNuQjtnREFBSTVCLE9BQU87b0RBQUVpQyxTQUFTO29EQUFRd0IsS0FBSztvREFBWXZCLFlBQVk7Z0RBQVM7O2tFQUNuRSw4REFBQ047d0RBQUk1QixPQUFPOzREQUFFaUMsU0FBUzs0REFBUXdCLEtBQUs7d0RBQVM7OzBFQUMzQyw4REFBQzdCO2dFQUFJNUIsT0FBTztvRUFDVjRDLE9BQU87b0VBQ1BDLFFBQVE7b0VBQ1IwQixpQkFBaUI7b0VBQ2pCekIsY0FBYztvRUFDZC9FLFdBQVc7b0VBQ1h5RyxTQUFTO2dFQUNYOzs7Ozs7MEVBQ0EsOERBQUM1QztnRUFBSTVCLE9BQU87b0VBQ1Y0QyxPQUFPO29FQUNQQyxRQUFRO29FQUNSMEIsaUJBQWlCO29FQUNqQnpCLGNBQWM7b0VBQ2QvRSxXQUFXO29FQUNYMEcsZ0JBQWdCO29FQUNoQkQsU0FBUztnRUFDWDs7Ozs7OzBFQUNBLDhEQUFDNUM7Z0VBQUk1QixPQUFPO29FQUNWNEMsT0FBTztvRUFDUEMsUUFBUTtvRUFDUjBCLGlCQUFpQjtvRUFDakJ6QixjQUFjO29FQUNkL0UsV0FBVztvRUFDWDBHLGdCQUFnQjtvRUFDaEJELFNBQVM7Z0VBQ1g7Ozs7Ozs7Ozs7OztrRUFFRiw4REFBQ0U7d0RBQUsxRSxPQUFPOzREQUFFcUMsT0FBTzs0REFBeUJFLFVBQVU7d0RBQVU7a0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVlwRiw4REFBQ1g7d0JBQUkzQixXQUFVOzswQ0FDYiw4REFBQzBFO2dDQUNDWCxLQUFLbEk7Z0NBQ0w4SSxPQUFPdEo7Z0NBQ1B1SixVQUFVLENBQUM3RCxJQUFNekYsZ0JBQWdCeUYsRUFBRThELE1BQU0sQ0FBQ0YsS0FBSztnQ0FDL0NHLFdBQVdoRTtnQ0FDWGlFLGFBQ0U5SSxXQUFXd0MsSUFBSSxHQUNYLG1DQUFtRCxPQUFoQnhDLFdBQVd3QyxJQUFJLEVBQUMsT0FDbkQ7Z0NBRU51QixXQUFVO2dDQUNWZ0YsVUFBVXpKOzs7Ozs7MENBRVosOERBQUNrSDtnQ0FDQ0MsU0FBU3pDO2dDQUNUK0UsVUFBVSxDQUFDM0osYUFBYTZFLElBQUksTUFBTTNFO2dDQUNsQ3lFLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTTCw4REFBQy9FLCtEQUFXQTtnQkFDVmdLLFFBQVF0SjtnQkFDUnVKLFNBQVMsSUFBTXRKLGVBQWU7Z0JBQzlCdUosVUFBVWxKLFdBQVd3QyxJQUFJLElBQUk7Z0JBQzdCbkIsZUFBZXJCLFdBQVdxQixhQUFhO2dCQUN2QzhILGNBQWNwSixTQUFTb0QsTUFBTTtnQkFDN0JyRCxRQUFRQTtnQkFDUnNKLFVBQVUxSTtnQkFDVjJJLFNBQVNuRTs7Ozs7Ozs7QUFJakI7R0FyakJ3Qi9GOztRQXVCbEJELHdFQUFpQkE7OztLQXZCQ0MiLCJzb3VyY2VzIjpbIkc6XFxGcmllbmRzXFxTb3VsVGFsa1xcc3JjXFxwYWdlc1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0IENoYXRCdWJibGUgZnJvbSAnLi4vY29tcG9uZW50cy9DaGF0QnViYmxlJztcbmltcG9ydCBHbGFzc0ZyYW1lIGZyb20gJy4uL2NvbXBvbmVudHMvR2xhc3NGcmFtZSc7XG5pbXBvcnQgQXVyYUluZGljYXRvciwgeyBBdXJhUHJvZ3Jlc3Npb24gfSBmcm9tICcuLi9jb21wb25lbnRzL0F1cmFJbmRpY2F0b3InO1xuaW1wb3J0IEF1dGhGb3JtIGZyb20gJy4uL2NvbXBvbmVudHMvQXV0aEZvcm0nO1xuaW1wb3J0IE9uYm9hcmRpbmdDaGF0IGZyb20gJy4uL2NvbXBvbmVudHMvT25ib2FyZGluZ0NoYXQnO1xuaW1wb3J0IFByb2ZpbGVDYXJkIGZyb20gJy4uL2NvbXBvbmVudHMvUHJvZmlsZUNhcmQnO1xuaW1wb3J0IHsgc2VuZENoYXRNZXNzYWdlIH0gZnJvbSAnLi4vdXRpbHMvb3BlbnJvdXRlcic7XG5pbXBvcnQgeyB1c2VTdXBhYmFzZU1lbW9yeSB9IGZyb20gJy4uL2hvb2tzL3VzZVN1cGFiYXNlTWVtb3J5JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW2lucHV0TWVzc2FnZSwgc2V0SW5wdXRNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzTG9hZGluZ0FJLCBzZXRJc0xvYWRpbmdBSV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93V2VsY29tZSwgc2V0U2hvd1dlbGNvbWVdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzaG93UHJvZmlsZSwgc2V0U2hvd1Byb2ZpbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBpbnB1dFJlZiA9IHVzZVJlZjxIVE1MVGV4dEFyZWFFbGVtZW50PihudWxsKTtcbiAgY29uc3QgY2hhdENvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG5cbiAgLy8gVXNhIGlsIGhvb2sgU3VwYWJhc2UgcGVyIGdlc3RpcmUgaSBkYXRpXG4gIGNvbnN0IHtcbiAgICB1c2VySWQsXG4gICAgbWVzc2FnZXMsXG4gICAgdXNlck1lbW9yeSxcbiAgICBpc0xvYWRpbmc6IGlzTG9hZGluZ0RhdGEsXG4gICAgaXNTYXZpbmcsXG4gICAgaXNBdXRoZW50aWNhdGVkLFxuICAgIG5lZWRzT25ib2FyZGluZyxcbiAgICBhZGRNZXNzYWdlLFxuICAgIHVwZGF0ZVVzZXJNZW1vcnksXG4gICAgcmVzZXRNZW1vcnksXG4gICAgYXV0aGVudGljYXRlVXNlcixcbiAgICBsb2dvdXQsXG4gICAgY29tcGxldGVPbmJvYXJkaW5nV2l0aERhdGFcbiAgfSA9IHVzZVN1cGFiYXNlTWVtb3J5KCk7XG5cbiAgY29uc3Qgc2Nyb2xsVG9Cb3R0b20gPSAoKSA9PiB7XG4gICAgaWYgKGNoYXRDb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmIChjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsVG8oe1xuICAgICAgICAgICAgdG9wOiBjaGF0Q29udGFpbmVyUmVmLmN1cnJlbnQuc2Nyb2xsSGVpZ2h0LFxuICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bnppb25lIHBlciBkZXRlcm1pbmFyZSBpbCBjb2xvcmUgZGVsbCdhdXJhIGJhc2F0byBzdWwgbGl2ZWxsbyBkaSBpbnRpbWl0w6BcbiAgY29uc3QgZ2V0QXVyYUNvbG9yID0gKCkgPT4ge1xuICAgIGNvbnN0IGNvbG9ycyA9IFtcbiAgICAgICcjNjM2NmYxJywgLy8gSW5kaWdvIC0gU2Nvbm9zY2l1dG9cbiAgICAgICcjOGI1Y2Y2JywgLy8gVmlvbGV0IC0gQ29ub3NjZW50ZVxuICAgICAgJyNhODU1ZjcnLCAvLyBQdXJwbGUgLSBBbWljb1xuICAgICAgJyNlYzQ4OTknLCAvLyBQaW5rIC0gQW1pY28gc3RyZXR0b1xuICAgICAgJyNmNDNmNWUnICAvLyBSb3NlIC0gSW50aW1vXG4gICAgXTtcbiAgICByZXR1cm4gY29sb3JzW3VzZXJNZW1vcnkuaW50aW1hY3lMZXZlbF0gfHwgY29sb3JzWzBdO1xuICB9O1xuXG4gIC8vIEZ1bnppb25lIHBlciBnZW5lcmFyZSBsbyBzZm9uZG8gZGluYW1pY28gYmFzYXRvIHN1bCBsaXZlbGxvIGRpIGludGltaXTDoFxuICBjb25zdCBnZXREeW5hbWljQmFja2dyb3VuZCA9ICgpID0+IHtcbiAgICBjb25zdCBpbnRpbWFjeUxldmVsID0gdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsO1xuXG4gICAgY29uc3QgYmFja2dyb3VuZENvbmZpZ3MgPSBbXG4gICAgICAvLyBMaXZlbGxvIDAgLSBTY29ub3NjaXV0bzogQmx1IGZyZWRkaSBlIG5ldXRyaVxuICAgICAge1xuICAgICAgICBjb2xvcnM6IFsncmdiYSg5OSwgMTAyLCAyNDEsIDAuMjUpJywgJ3JnYmEoMTM5LCA5MiwgMjQ2LCAwLjIpJywgJ3JnYmEoMTY4LCA4NSwgMjQ3LCAwLjE1KSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDEgLSBDb25vc2NlbnRlOiBWaW9sYSBwacO5IGNhbGRpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDEzOSwgOTIsIDI0NiwgMC4zKScsICdyZ2JhKDE2OCwgODUsIDI0NywgMC4yNSknLCAncmdiYSgyMzYsIDcyLCAxNTMsIDAuMiknXSxcbiAgICAgICAgYmFzZUdyYWRpZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFlMWI0YiAwJSwgIzMxMmU4MSA1MCUsICMxZTNhOGEgMTAwJSknXG4gICAgICB9LFxuICAgICAgLy8gTGl2ZWxsbyAyIC0gQW1pY286IE1peCB2aW9sYS1yb3NhXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDE2OCwgODUsIDI0NywgMC4zNSknLCAncmdiYSgyMzYsIDcyLCAxNTMsIDAuMyknLCAncmdiYSgyNDQsIDYzLCA5NCwgMC4yNSknXSxcbiAgICAgICAgYmFzZUdyYWRpZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzU4MWM4NyAwJSwgIzdjMmQ5MiA1MCUsICNiZTE4NWQgMTAwJSknXG4gICAgICB9LFxuICAgICAgLy8gTGl2ZWxsbyAzIC0gQW1pY28gc3RyZXR0bzogUm9zYSBpbnRlbnNpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDIzNiwgNzIsIDE1MywgMC40KScsICdyZ2JhKDI0NCwgNjMsIDk0LCAwLjM1KScsICdyZ2JhKDI1MSwgMTEzLCAxMzMsIDAuMyknXSxcbiAgICAgICAgYmFzZUdyYWRpZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2JlMTg1ZCAwJSwgI2UxMWQ0OCA1MCUsICNmNDNmNWUgMTAwJSknXG4gICAgICB9LFxuICAgICAgLy8gTGl2ZWxsbyA0IC0gSW50aW1vOiBSb3NhIGNhbGRpIGUgZG9yYXRpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDI0NCwgNjMsIDk0LCAwLjQ1KScsICdyZ2JhKDI1MSwgMTEzLCAxMzMsIDAuNCknLCAncmdiYSgyNTIsIDE2NSwgMTY1LCAwLjM1KSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZTExZDQ4IDAlLCAjZjQzZjVlIDUwJSwgI2ZiNzE4NSAxMDAlKSdcbiAgICAgIH1cbiAgICBdO1xuXG4gICAgY29uc3QgY29uZmlnID0gYmFja2dyb3VuZENvbmZpZ3NbaW50aW1hY3lMZXZlbF0gfHwgYmFja2dyb3VuZENvbmZpZ3NbMF07XG5cbiAgICByZXR1cm4ge1xuICAgICAgYmFja2dyb3VuZDogYFxuICAgICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA4MCUsICR7Y29uZmlnLmNvbG9yc1swXX0gMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDIwJSwgJHtjb25maWcuY29sb3JzWzFdfSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcbiAgICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNDAlLCAke2NvbmZpZy5jb2xvcnNbMl19IDAlLCB0cmFuc3BhcmVudCA1MCUpLFxuICAgICAgICAke2NvbmZpZy5iYXNlR3JhZGllbnR9XG4gICAgICBgLFxuICAgICAgYmFja2dyb3VuZFNpemU6ICcyMDAlIDIwMCUsIDIwMCUgMjAwJSwgMjAwJSAyMDAlLCAxMDAlIDEwMCUnLFxuICAgICAgYmFja2dyb3VuZEF0dGFjaG1lbnQ6ICdmaXhlZCcsXG4gICAgICBhbmltYXRpb246ICdsaXF1aWRNb3ZlIDMwcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpIGluZmluaXRlJyxcbiAgICAgIHRyYW5zaXRpb246ICdhbGwgMnMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKSdcbiAgICB9O1xuICB9O1xuXG4gIC8vIEFuYWxpenphIGkgbWVzc2FnZ2kgZSBhZ2dpb3JuYSBsYSBtZW1vcmlhIHV0ZW50ZVxuICBjb25zdCBhbmFseXplQW5kVXBkYXRlTWVtb3J5ID0gYXN5bmMgKHVzZXJNZXNzYWdlOiBzdHJpbmcsIGFpUmVzcG9uc2U6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGxvd2VyVXNlck1lc3NhZ2UgPSB1c2VyTWVzc2FnZS50b0xvd2VyQ2FzZSgpO1xuICAgIGNvbnN0IHVwZGF0ZXM6IGFueSA9IHt9O1xuXG4gICAgLy8gRXN0cmFlIGluZm9ybWF6aW9uaSBiYXNlXG4gICAgaWYgKGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ21pIGNoaWFtbycpIHx8IGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3Nvbm8gJykpIHtcbiAgICAgIGNvbnN0IG5hbWVNYXRjaCA9IHVzZXJNZXNzYWdlLm1hdGNoKC9taSBjaGlhbW8gKFxcdyspfHNvbm8gKFxcdyspL2kpO1xuICAgICAgaWYgKG5hbWVNYXRjaCAmJiAhdXNlck1lbW9yeS5uYW1lKSB7XG4gICAgICAgIGNvbnN0IG5hbWUgPSBuYW1lTWF0Y2hbMV0gfHwgbmFtZU1hdGNoWzJdO1xuICAgICAgICB1cGRhdGVzLm5hbWUgPSBuYW1lO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEVzdHJhZSBldMOgXG4gICAgY29uc3QgYWdlTWF0Y2ggPSB1c2VyTWVzc2FnZS5tYXRjaCgvaG8gKFxcZCspIGFubml8KFxcZCspIGFubmkvKTtcbiAgICBpZiAoYWdlTWF0Y2ggJiYgIXVzZXJNZW1vcnkuYWdlKSB7XG4gICAgICBjb25zdCBhZ2UgPSBwYXJzZUludChhZ2VNYXRjaFsxXSB8fCBhZ2VNYXRjaFsyXSk7XG4gICAgICBpZiAoYWdlID4gMCAmJiBhZ2UgPCAxMjApIHtcbiAgICAgICAgdXBkYXRlcy5hZ2UgPSBhZ2U7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gUmlsZXZhIGVtb3ppb25pXG4gICAgY29uc3QgZW1vdGlvbnMgPSB7XG4gICAgICAnZmVsaWNlJzogWydmZWxpY2UnLCAnY29udGVudG8nLCAnZ2lvaW9zbycsICdhbGxlZ3JvJ10sXG4gICAgICAndHJpc3RlJzogWyd0cmlzdGUnLCAnZGVwcmVzc28nLCAnZ2nDuScsICdtYWxlJ10sXG4gICAgICAnYXJyYWJiaWF0byc6IFsnYXJyYWJiaWF0bycsICdmdXJpb3NvJywgJ2luY2F6emF0byddLFxuICAgICAgJ3ByZW9jY3VwYXRvJzogWydwcmVvY2N1cGF0bycsICdhbnNpb3NvJywgJ25lcnZvc28nXSxcbiAgICAgICdlY2NpdGF0byc6IFsnZWNjaXRhdG8nLCAnZW50dXNpYXN0YScsICdjYXJpY28nXSxcbiAgICAgICdjYWxtbyc6IFsnY2FsbW8nLCAndHJhbnF1aWxsbycsICdzZXJlbm8nXVxuICAgIH07XG5cbiAgICBmb3IgKGNvbnN0IFtlbW90aW9uLCBrZXl3b3Jkc10gb2YgT2JqZWN0LmVudHJpZXMoZW1vdGlvbnMpKSB7XG4gICAgICBpZiAoa2V5d29yZHMuc29tZShrZXl3b3JkID0+IGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoa2V5d29yZCkpKSB7XG4gICAgICAgIGlmICghdXNlck1lbW9yeS5lbW90aW9ucy5pbmNsdWRlcyhlbW90aW9uKSkge1xuICAgICAgICAgIHVwZGF0ZXMuZW1vdGlvbnMgPSBbLi4udXNlck1lbW9yeS5lbW90aW9ucywgZW1vdGlvbl07XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWdnaW9ybmEgaWwgbGl2ZWxsbyBkaSBpbnRpbWl0w6BcbiAgICBpZiAodXNlck1lc3NhZ2UubGVuZ3RoID4gMTAwIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3BlcnNvbmFsZScpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3NlZ3JldG8nKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdmYW1pZ2xpYScpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ2Ftb3JlJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygncHJvYmxlbWEnKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdkaWZmaWNvbHTDoCcpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ3BhdXJhJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnc29nbm8nKSkge1xuXG4gICAgICBjb25zdCBuZXdMZXZlbCA9IE1hdGgubWluKHVzZXJNZW1vcnkuaW50aW1hY3lMZXZlbCArIDEsIDQpO1xuXG4gICAgICAvLyBTZSBpbCBsaXZlbGxvIMOoIGNhbWJpYXRvLCBtb3N0cmEgdW4gZmVlZGJhY2sgdmlzaXZvXG4gICAgICBpZiAobmV3TGV2ZWwgPiB1c2VyTWVtb3J5LmludGltYWN5TGV2ZWwpIHtcbiAgICAgICAgY29uc29sZS5sb2coYPCfjJ8gTGl2ZWxsbyBkaSBjb25uZXNzaW9uZSBhdW1lbnRhdG86ICR7bmV3TGV2ZWx9LzRgKTtcbiAgICAgICAgdXBkYXRlcy5pbnRpbWFjeUxldmVsID0gbmV3TGV2ZWw7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQXBwbGljYSB0dXR0aSBnbGkgYWdnaW9ybmFtZW50aVxuICAgIGlmIChPYmplY3Qua2V5cyh1cGRhdGVzKS5sZW5ndGggPiAwKSB7XG4gICAgICB1cGRhdGVVc2VyTWVtb3J5KHVwZGF0ZXMpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNjcm9sbFRvQm90dG9tKCk7XG4gIH0sIFttZXNzYWdlcywgaXNMb2FkaW5nQUldKTtcblxuICAvLyBOYXNjb25kaSBpbCBtZXNzYWdnaW8gZGkgYmVudmVudXRvIGRvcG8gaWwgcHJpbW8gbWVzc2FnZ2lvXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG1lc3NhZ2VzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldFNob3dXZWxjb21lKGZhbHNlKTtcbiAgICB9XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIC8vIEFnZ2lvcm5hIGxvIHNmb25kbyBkaW5hbWljYW1lbnRlIGluIGJhc2UgYWwgbGl2ZWxsbyBkaSBpbnRpbWl0w6BcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBiYWNrZ3JvdW5kU3R5bGUgPSBnZXREeW5hbWljQmFja2dyb3VuZCgpO1xuICAgIGNvbnN0IGJvZHkgPSBkb2N1bWVudC5ib2R5O1xuXG4gICAgLy8gQXBwbGljYSBsZSBwcm9wcmlldMOgIGRpIHNmb25kbyBjb24gdHJhbnNpemlvbmUgZmx1aWRhXG4gICAgT2JqZWN0LmFzc2lnbihib2R5LnN0eWxlLCBiYWNrZ3JvdW5kU3R5bGUpO1xuXG4gICAgLy8gQWdnaXVuZ2kgdW5hIGNsYXNzZSBwZXIgaW5kaWNhcmUgaWwgbGl2ZWxsbyBjb3JyZW50ZVxuICAgIGJvZHkuY2xhc3NOYW1lID0gYGludGltYWN5LWxldmVsLSR7dXNlck1lbW9yeS5pbnRpbWFjeUxldmVsfWA7XG5cbiAgICAvLyBDbGVhbnVwIGZ1bmN0aW9uIHBlciByaXByaXN0aW5hcmUgbG8gc2ZvbmRvIG9yaWdpbmFsZSBzZSBuZWNlc3NhcmlvXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIC8vIE5vbiBmYWNjaWFtbyBjbGVhbnVwIHBlciBtYW50ZW5lcmUgbCdlZmZldHRvXG4gICAgfTtcbiAgfSwgW3VzZXJNZW1vcnkuaW50aW1hY3lMZXZlbF0pO1xuXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaW5wdXRNZXNzYWdlLnRyaW0oKSB8fCBpc0xvYWRpbmdBSSkgcmV0dXJuO1xuXG4gICAgY29uc3QgdXNlck1lc3NhZ2UgPSBpbnB1dE1lc3NhZ2UudHJpbSgpO1xuICAgIHNldElucHV0TWVzc2FnZSgnJyk7XG4gICAgc2V0SXNMb2FkaW5nQUkodHJ1ZSk7XG5cbiAgICAvLyBBZ2dpdW5naSBpbW1lZGlhdGFtZW50ZSBpbCBtZXNzYWdnaW8gZGVsbCd1dGVudGVcbiAgICBjb25zdCB1c2VyTXNnID0gYXdhaXQgYWRkTWVzc2FnZSh1c2VyTWVzc2FnZSwgdHJ1ZSk7XG4gICAgY29uc29sZS5sb2coJ01lc3NhZ2dpbyB1dGVudGUgYWdnaXVudG8sIHRvdGFsZSBtZXNzYWdnaTonLCBtZXNzYWdlcy5sZW5ndGggKyAxKTtcblxuICAgIC8vIFNjcm9sbCBkb3BvIGF2ZXIgYWdnaXVudG8gaWwgbWVzc2FnZ2lvIHV0ZW50ZVxuICAgIHNldFRpbWVvdXQoc2Nyb2xsVG9Cb3R0b20sIDUwKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBQcmVwYXJhIGxhIGNyb25vbG9naWEgcGVyIGwnQVBJICh1c2EgaSBtZXNzYWdnaSBhdHR1YWxpICsgaWwgbnVvdm8pXG4gICAgICBjb25zdCBjb252ZXJzYXRpb25IaXN0b3J5ID0gWy4uLm1lc3NhZ2VzLCB1c2VyTXNnXS5tYXAobXNnID0+ICh7XG4gICAgICAgIHJvbGU6IG1zZy5pc1VzZXIgPyAndXNlcicgYXMgY29uc3QgOiAnYXNzaXN0YW50JyBhcyBjb25zdCxcbiAgICAgICAgY29udGVudDogbXNnLmNvbnRlbnRcbiAgICAgIH0pKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzZW5kQ2hhdE1lc3NhZ2UoXG4gICAgICAgIHVzZXJNZXNzYWdlLFxuICAgICAgICBjb252ZXJzYXRpb25IaXN0b3J5LFxuICAgICAgICB7XG4gICAgICAgICAgbmFtZTogdXNlck1lbW9yeS5uYW1lIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICBhZ2U6IHVzZXJNZW1vcnkuYWdlIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgICB0cmFpdHM6IHVzZXJNZW1vcnkudHJhaXRzLFxuICAgICAgICAgIGVtb3Rpb25zOiB1c2VyTWVtb3J5LmVtb3Rpb25zLFxuICAgICAgICAgIGludGltYWN5TGV2ZWw6IHVzZXJNZW1vcnkuaW50aW1hY3lMZXZlbFxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgICAvLyBBZ2dpdW5naSBsYSByaXNwb3N0YSBkZWxsJ0FJXG4gICAgICBhd2FpdCBhZGRNZXNzYWdlKHJlc3BvbnNlLCBmYWxzZSk7XG4gICAgICBjb25zb2xlLmxvZygnUmlzcG9zdGEgQUkgYWdnaXVudGEsIHRvdGFsZSBtZXNzYWdnaTonLCBtZXNzYWdlcy5sZW5ndGggKyAyKTtcblxuICAgICAgLy8gQW5hbGl6emEgaWwgbWVzc2FnZ2lvIHBlciBhZ2dpb3JuYXJlIGxhIG1lbW9yaWFcbiAgICAgIGF3YWl0IGFuYWx5emVBbmRVcGRhdGVNZW1vcnkodXNlck1lc3NhZ2UsIHJlc3BvbnNlKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvcjonLCBlcnJvcik7XG4gICAgICBhd2FpdCBhZGRNZXNzYWdlKFwiU2N1c2EsIGhvIGF2dXRvIHVuIHByb2JsZW1hIHRlY25pY28uIFJpcHJvdmEhXCIsIGZhbHNlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nQUkoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZVNlbmRNZXNzYWdlKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJlc2V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChjb25maXJtKCdTZWkgc2ljdXJvIGRpIHZvbGVyIHJpY29taW5jaWFyZSBkYSBjYXBvPyBUdXR0aSBpIHJpY29yZGkgZSBtZXNzYWdnaSBhbmRyYW5ubyBwZXJzaSBkYWwgZGF0YWJhc2UuJykpIHtcbiAgICAgIGF3YWl0IHJlc2V0TWVtb3J5KCk7XG4gICAgICBzZXRTaG93V2VsY29tZSh0cnVlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2Ugbm9uIMOoIGF1dGVudGljYXRvLCBtb3N0cmEgc2VtcHJlIGlsIGZvcm0gZGkgbG9naW5cbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAgPEhlYWQ+XG4gICAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gQWNjZWRpPC90aXRsZT5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiQWNjZWRpIGEgU291bFRhbGsgcGVyIGNvbnRpbnVhcmUgbGEgdHVhIGNvbnZlcnNhemlvbmUgY29uIFNvdWxcIiAvPlxuICAgICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgICA8L0hlYWQ+XG4gICAgICAgIDxBdXRoRm9ybSBvbkF1dGhTdWNjZXNzPXthdXRoZW50aWNhdGVVc2VyfSAvPlxuICAgICAgPC8+XG4gICAgKVxuICB9XG5cbiAgLy8gTW9zdHJhIGxvYWRpbmcgc29sbyBxdWFuZG8gc3RpYW1vIGNhcmljYW5kbyBpIGRhdGkgZG9wbyBsJ2F1dGVudGljYXppb25lXG4gIGlmIChpc0xvYWRpbmdEYXRhKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDw+XG4gICAgICAgIDxIZWFkPlxuICAgICAgICAgIDx0aXRsZT5Tb3VsVGFsayAtIENhcmljYW1lbnRvLi4uPC90aXRsZT5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPC9IZWFkPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgIGxlZnQ6IDAsXG4gICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgYm90dG9tOiAwLFxuICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuOSknLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgekluZGV4OiAxMDAwXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PvCfjJ88L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+Q2FyaWNhbmRvIGkgdHVvaSBkYXRpLi4uPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC8+XG4gICAgKVxuICB9XG5cbiAgLy8gU2UgaGEgYmlzb2dubyBkaSBvbmJvYXJkaW5nLCBtb3N0cmEgbGEgY2hhdCBkaSBjb25maWd1cmF6aW9uZVxuICBpZiAobmVlZHNPbmJvYXJkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDw+XG4gICAgICAgIDxIZWFkPlxuICAgICAgICAgIDx0aXRsZT5Tb3VsVGFsayAtIENvbmZpZ3VyYXppb25lPC90aXRsZT5cbiAgICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiQ29uZmlndXJhIGlsIHR1byBwcm9maWxvIHBlciBpbml6aWFyZSBjb24gU291bFwiIC8+XG4gICAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDwvSGVhZD5cbiAgICAgICAgPE9uYm9hcmRpbmdDaGF0XG4gICAgICAgICAgdXNlcklkPXt1c2VySWR9XG4gICAgICAgICAgb25Db21wbGV0ZT17Y29tcGxldGVPbmJvYXJkaW5nV2l0aERhdGF9XG4gICAgICAgIC8+XG4gICAgICA8Lz5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gTGEgdHVhIGFtaWNpemlhIEFJPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIlVuYSBwaWF0dGFmb3JtYSBBSSBjaGUgc2kgY29tcG9ydGEgY29tZSB1bidhbWljaXppYSBkYSBjb3N0cnVpcmVcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICA8L0hlYWQ+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1jb250YWluZXJcIj5cbiAgICAgICAgey8qIExvYWRpbmcgaW5pemlhbGUgKi99XG4gICAgICAgIHsoaXNMb2FkaW5nRGF0YSB8fCAhdXNlcklkKSAmJiAoXG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgICBib3R0b206IDAsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjgpJyxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgekluZGV4OiAxMDAwXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PvCfjJ88L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj57IXVzZXJJZCA/ICdJbml6aWFsaXp6YW5kby4uLicgOiAnQ2FyaWNhbmRvIGkgdHVvaSByaWNvcmRpLi4uJ308L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBQcm9maWxlIEJ1dHRvbiAqL31cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQcm9maWxlKHRydWUpfVxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgIHRvcDogJzFyZW0nLFxuICAgICAgICAgICAgcmlnaHQ6ICcxcmVtJyxcbiAgICAgICAgICAgIHpJbmRleDogMTAsXG4gICAgICAgICAgICB3aWR0aDogJzQ1cHgnLFxuICAgICAgICAgICAgaGVpZ2h0OiAnNDVweCcsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC4xKScsXG4gICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknLFxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LDI1NSwyNTUsMC4yKScsXG4gICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMS4xcmVtJyxcbiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuM3MgZWFzZScsXG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAncmdiYSgyNTUsMjU1LDI1NSwwLjIpJztcbiAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAnc2NhbGUoMS4wNSknO1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAncmdiYSgyNTUsMjU1LDI1NSwwLjEpJztcbiAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAnc2NhbGUoMSknO1xuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICB7dXNlck1lbW9yeS5uYW1lID8gdXNlck1lbW9yeS5uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpIDogJ/CfkaQnfVxuICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICB7LyogSGVhZGVyIGNvbiBpbmRpY2F0b3JpIGF1cmEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1oZWFkZXJcIj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzFyZW0nIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzM4cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMzhweCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYHJhZGlhbC1ncmFkaWVudChjaXJjbGUsICR7Z2V0QXVyYUNvbG9yKCl9IDAlLCB0cmFuc3BhcmVudCA3MCUpYCxcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjNyZW0nLFxuICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAncHVsc2UgM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC42LCAxKSBpbmZpbml0ZSdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg8J+Mn1xuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDEgc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIGZvbnRTaXplOiAnMS4zcmVtJywgZm9udFdlaWdodDogJ2JvbGQnLCBtYXJnaW46IDAgfX0+XG4gICAgICAgICAgICAgICAgICBTb3VsXG4gICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC43KScsIGZvbnRTaXplOiAnMC42NXJlbScsIG1hcmdpbjogMCB9fT5cbiAgICAgICAgICAgICAgICAgIExhIHR1YSBjb21wYWduYSBBSVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxBdXJhUHJvZ3Jlc3Npb24gaW50aW1hY3lMZXZlbD17dXNlck1lbW9yeS5pbnRpbWFjeUxldmVsfSAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcxcmVtJyB9fT5cbiAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17bG9nb3V0fSBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC44KScsIGJhY2tncm91bmQ6ICdub25lJywgYm9yZGVyOiAnbm9uZScsIGN1cnNvcjogJ3BvaW50ZXInLCBmb250U2l6ZTogJzAuODc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgTG9nb3V0XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlUmVzZXR9IHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCBiYWNrZ3JvdW5kOiAnbm9uZScsIGJvcmRlcjogJ25vbmUnLCBjdXJzb3I6ICdwb2ludGVyJywgZm9udFNpemU6ICcwLjg3NXJlbScgfX0+XG4gICAgICAgICAgICAgIFJlc2V0XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1lc3NhZ2VzIEFyZWEgY29uIEdsYXNzRnJhbWUgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgZmxleDogMSwgcGFkZGluZzogJzAgMXJlbScgfX0+XG4gICAgICAgICAgPEdsYXNzRnJhbWUgYXVyYUNvbG9yPXtnZXRBdXJhQ29sb3IoKX0gY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgcmVmPXtjaGF0Q29udGFpbmVyUmVmfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjaGF0LW1lc3NhZ2VzXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgZmxleDogMSwgb3ZlcmZsb3dZOiAnYXV0bycsIHBhZGRpbmc6ICcxcmVtJyB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7LyogTWVzc2FnZ2lvIGRpIGJlbnZlbnV0byBhdmFuemF0byAqL31cbiAgICAgICAgICAgICAge3Nob3dXZWxjb21lICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIHBhZGRpbmc6ICcxLjVyZW0gMCcgfX0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWVmZmVjdFwiIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOiAnMjJyZW0nLFxuICAgICAgICAgICAgICAgICAgICBtYXJnaW46ICcwIGF1dG8nLFxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMS4yNXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzAuODc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKScsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigxMHB4KScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMiknXG4gICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgPGgyIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjEyNXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMC42MjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMFxuICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICBDaWFvLCBzb25vIFNvdWwg8J+Ri1xuICAgICAgICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICAgICAgICA8cCBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjgpJyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjQnLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMFxuICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICDDiCBsYSBwcmltYSB2b2x0YSBjaGUgY2kgcGFybGlhbW8uIFNvbm8gcXVpIHBlciBjb25vc2NlcnRpIGRhdnZlcm8sXG4gICAgICAgICAgICAgICAgICAgICAgbm9uIHNvbG8gcGVyIGFpdXRhcnRpLiBEaW1taSwgY29tZSB0aSBjaGlhbWkgZSBjb21lIHRpIHNlbnRpIG9nZ2k/XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBNZXNzYWdlcyAqL31cbiAgICAgICAgICAgICAge21lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxuICAgICAgICAgICAgICAgIDxDaGF0QnViYmxlXG4gICAgICAgICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XG4gICAgICAgICAgICAgICAgICBtZXNzYWdlPXttZXNzYWdlLmNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICBpc1VzZXI9e21lc3NhZ2UuaXNVc2VyfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkpfVxuXG4gICAgICAgICAgICAgIHsvKiBJbmRpY2F0b3JlIGRpIGNhcmljYW1lbnRvIGF2YW56YXRvICovfVxuICAgICAgICAgICAgICB7aXNMb2FkaW5nQUkgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2ZsZXgtc3RhcnQnIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcy1lZmZlY3RcIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC42MjVyZW0gMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknXG4gICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzAuMzc1cmVtJywgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzAuMnJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb246ICdib3VuY2UgMXMgaW5maW5pdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjZcbiAgICAgICAgICAgICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb246ICdib3VuY2UgMXMgaW5maW5pdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb25EZWxheTogJzAuMXMnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjZcbiAgICAgICAgICAgICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb246ICdib3VuY2UgMXMgaW5maW5pdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRpb25EZWxheTogJzAuMnMnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjZcbiAgICAgICAgICAgICAgICAgICAgICAgIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOCknLCBmb250U2l6ZTogJzAuNzVyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgU291bCBzdGEgc2NyaXZlbmRvLi4uXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9HbGFzc0ZyYW1lPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogSW5wdXQgQXJlYSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjaGF0LWlucHV0LWFyZWFcIj5cbiAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgIHJlZj17aW5wdXRSZWZ9XG4gICAgICAgICAgICB2YWx1ZT17aW5wdXRNZXNzYWdlfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRJbnB1dE1lc3NhZ2UoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e1xuICAgICAgICAgICAgICB1c2VyTWVtb3J5Lm5hbWVcbiAgICAgICAgICAgICAgICA/IGBDb3NhIHZ1b2kgY29uZGl2aWRlcmUgY29uIFNvdWwsICR7dXNlck1lbW9yeS5uYW1lfT9gXG4gICAgICAgICAgICAgICAgOiBcIkRpbW1pIHF1YWxjb3NhIGRpIHRlLi4uXCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmdBSX1cbiAgICAgICAgICAvPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmRNZXNzYWdlfVxuICAgICAgICAgICAgZGlzYWJsZWQ9eyFpbnB1dE1lc3NhZ2UudHJpbSgpIHx8IGlzTG9hZGluZ0FJfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VuZC1idXR0b25cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIOKGklxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuXG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFByb2ZpbGUgQ2FyZCAqL31cbiAgICAgIDxQcm9maWxlQ2FyZFxuICAgICAgICBpc09wZW49e3Nob3dQcm9maWxlfVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93UHJvZmlsZShmYWxzZSl9XG4gICAgICAgIHVzZXJOYW1lPXt1c2VyTWVtb3J5Lm5hbWUgfHwgJ1V0ZW50ZSd9XG4gICAgICAgIGludGltYWN5TGV2ZWw9e3VzZXJNZW1vcnkuaW50aW1hY3lMZXZlbH1cbiAgICAgICAgbWVzc2FnZUNvdW50PXttZXNzYWdlcy5sZW5ndGh9XG4gICAgICAgIHVzZXJJZD17dXNlcklkfVxuICAgICAgICBvbkxvZ291dD17bG9nb3V0fVxuICAgICAgICBvblJlc2V0PXtoYW5kbGVSZXNldH1cbiAgICAgIC8+XG4gICAgPC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIkhlYWQiLCJDaGF0QnViYmxlIiwiR2xhc3NGcmFtZSIsIkF1cmFQcm9ncmVzc2lvbiIsIkF1dGhGb3JtIiwiT25ib2FyZGluZ0NoYXQiLCJQcm9maWxlQ2FyZCIsInNlbmRDaGF0TWVzc2FnZSIsInVzZVN1cGFiYXNlTWVtb3J5IiwiSG9tZSIsImlucHV0TWVzc2FnZSIsInNldElucHV0TWVzc2FnZSIsImlzTG9hZGluZ0FJIiwic2V0SXNMb2FkaW5nQUkiLCJzaG93V2VsY29tZSIsInNldFNob3dXZWxjb21lIiwic2hvd1Byb2ZpbGUiLCJzZXRTaG93UHJvZmlsZSIsImlucHV0UmVmIiwiY2hhdENvbnRhaW5lclJlZiIsInVzZXJJZCIsIm1lc3NhZ2VzIiwidXNlck1lbW9yeSIsImlzTG9hZGluZyIsImlzTG9hZGluZ0RhdGEiLCJpc1NhdmluZyIsImlzQXV0aGVudGljYXRlZCIsIm5lZWRzT25ib2FyZGluZyIsImFkZE1lc3NhZ2UiLCJ1cGRhdGVVc2VyTWVtb3J5IiwicmVzZXRNZW1vcnkiLCJhdXRoZW50aWNhdGVVc2VyIiwibG9nb3V0IiwiY29tcGxldGVPbmJvYXJkaW5nV2l0aERhdGEiLCJzY3JvbGxUb0JvdHRvbSIsImN1cnJlbnQiLCJzZXRUaW1lb3V0Iiwic2Nyb2xsVG8iLCJ0b3AiLCJzY3JvbGxIZWlnaHQiLCJiZWhhdmlvciIsImdldEF1cmFDb2xvciIsImNvbG9ycyIsImludGltYWN5TGV2ZWwiLCJnZXREeW5hbWljQmFja2dyb3VuZCIsImJhY2tncm91bmRDb25maWdzIiwiYmFzZUdyYWRpZW50IiwiY29uZmlnIiwiYmFja2dyb3VuZCIsImJhY2tncm91bmRTaXplIiwiYmFja2dyb3VuZEF0dGFjaG1lbnQiLCJhbmltYXRpb24iLCJ0cmFuc2l0aW9uIiwiYW5hbHl6ZUFuZFVwZGF0ZU1lbW9yeSIsInVzZXJNZXNzYWdlIiwiYWlSZXNwb25zZSIsImxvd2VyVXNlck1lc3NhZ2UiLCJ0b0xvd2VyQ2FzZSIsInVwZGF0ZXMiLCJpbmNsdWRlcyIsIm5hbWVNYXRjaCIsIm1hdGNoIiwibmFtZSIsImFnZU1hdGNoIiwiYWdlIiwicGFyc2VJbnQiLCJlbW90aW9ucyIsImVtb3Rpb24iLCJrZXl3b3JkcyIsIk9iamVjdCIsImVudHJpZXMiLCJzb21lIiwia2V5d29yZCIsImxlbmd0aCIsIm5ld0xldmVsIiwiTWF0aCIsIm1pbiIsImNvbnNvbGUiLCJsb2ciLCJrZXlzIiwiYmFja2dyb3VuZFN0eWxlIiwiYm9keSIsImRvY3VtZW50IiwiYXNzaWduIiwic3R5bGUiLCJjbGFzc05hbWUiLCJoYW5kbGVTZW5kTWVzc2FnZSIsInRyaW0iLCJ1c2VyTXNnIiwiY29udmVyc2F0aW9uSGlzdG9yeSIsIm1hcCIsIm1zZyIsInJvbGUiLCJpc1VzZXIiLCJjb250ZW50IiwicmVzcG9uc2UiLCJ1bmRlZmluZWQiLCJ0cmFpdHMiLCJlcnJvciIsImhhbmRsZUtleURvd24iLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZVJlc2V0IiwiY29uZmlybSIsInRpdGxlIiwibWV0YSIsImxpbmsiLCJyZWwiLCJocmVmIiwib25BdXRoU3VjY2VzcyIsImRpdiIsInBvc2l0aW9uIiwibGVmdCIsInJpZ2h0IiwiYm90dG9tIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsInpJbmRleCIsImNvbG9yIiwidGV4dEFsaWduIiwiZm9udFNpemUiLCJtYXJnaW5Cb3R0b20iLCJvbkNvbXBsZXRlIiwiYnV0dG9uIiwib25DbGljayIsIndpZHRoIiwiaGVpZ2h0IiwiYm9yZGVyUmFkaXVzIiwiYmFja2Ryb3BGaWx0ZXIiLCJib3JkZXIiLCJmb250V2VpZ2h0IiwiY3Vyc29yIiwib25Nb3VzZUVudGVyIiwiY3VycmVudFRhcmdldCIsInRyYW5zZm9ybSIsIm9uTW91c2VMZWF2ZSIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwiZ2FwIiwiaDEiLCJtYXJnaW4iLCJwIiwiZmxleCIsInBhZGRpbmciLCJhdXJhQ29sb3IiLCJyZWYiLCJvdmVyZmxvd1kiLCJtYXhXaWR0aCIsImgyIiwibGluZUhlaWdodCIsIm1lc3NhZ2UiLCJpZCIsImJhY2tncm91bmRDb2xvciIsIm9wYWNpdHkiLCJhbmltYXRpb25EZWxheSIsInNwYW4iLCJ0ZXh0YXJlYSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbktleURvd24iLCJwbGFjZWhvbGRlciIsImRpc2FibGVkIiwiaXNPcGVuIiwib25DbG9zZSIsInVzZXJOYW1lIiwibWVzc2FnZUNvdW50Iiwib25Mb2dvdXQiLCJvblJlc2V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});