import React, { useState, useEffect, useRef } from 'react';
import ChatBubble from './ChatBubble';
import { completeOnboarding } from '../lib/auth';

interface OnboardingChatProps {
  userId: string;
  onComplete: (userData: { name: string; age: number }) => void;
}

interface OnboardingMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

const OnboardingChat: React.FC<OnboardingChatProps> = ({ userId, onComplete }) => {
  const [messages, setMessages] = useState<OnboardingMessage[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [inputMessage, setInputMessage] = useState('');
  const [isWaiting, setIsWaiting] = useState(false);
  const [userData, setUserData] = useState({ name: '', age: 0 });
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const onboardingSteps = [
    {
      message: "Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟",
      waitForInput: false
    },
    {
      message: "Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio. Come ti chiami?",
      waitForInput: true,
      validator: (input: string) => {
        if (input.trim().length < 2) return "Il nome deve essere di almeno 2 caratteri";
        if (input.trim().length > 50) return "Il nome è troppo lungo";
        return null;
      },
      processor: (input: string) => {
        setUserData(prev => ({ ...prev, name: input.trim() }));
      }
    },
    {
      message: (name: string) => `Che bel nome, ${name}! Mi piace molto. 😊`,
      waitForInput: false
    },
    {
      message: "Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni.",
      waitForInput: true,
      validator: (input: string) => {
        const age = parseInt(input);
        if (isNaN(age)) return "Per favore inserisci un numero valido";
        if (age < 13) return "Devi avere almeno 13 anni per usare SoulTalk";
        if (age > 120) return "Inserisci un'età realistica";
        return null;
      },
      processor: (input: string) => {
        setUserData(prev => ({ ...prev, age: parseInt(input) }));
      }
    },
    {
      message: (name: string, age: number) => 
        `Perfetto ${name}! ${age} anni... sei in un'età interessante. Ho tante cose da condividere con te! ✨`,
      waitForInput: false
    },
    {
      message: "Ora che ci conosciamo meglio, possiamo iniziare la nostra conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Sei pronto/a?",
      waitForInput: false,
      isLast: true
    }
  ];

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTo({
            top: chatContainerRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  };

  const addMessage = (content: string, isUser: boolean) => {
    const newMessage: OnboardingMessage = {
      id: `onb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      isUser,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMessage]);
    setTimeout(scrollToBottom, 50);
  };

  const processStep = async () => {
    const step = onboardingSteps[currentStep];
    
    if (typeof step.message === 'function') {
      const message = step.message(userData.name, userData.age);
      addMessage(message, false);
    } else {
      addMessage(step.message, false);
    }

    if (step.isLast) {
      // Completa l'onboarding
      setTimeout(async () => {
        await completeOnboarding(userId);
        onComplete(userData);
      }, 2000);
      return;
    }

    if (!step.waitForInput) {
      setTimeout(() => {
        setCurrentStep(prev => prev + 1);
      }, 2000);
    } else {
      setIsWaiting(true);
    }
  };

  const handleUserInput = () => {
    if (!inputMessage.trim() || !isWaiting) return;

    const step = onboardingSteps[currentStep];
    
    // Validazione input
    if (step.validator) {
      const error = step.validator(inputMessage.trim());
      if (error) {
        addMessage(error, false);
        return;
      }
    }

    // Aggiungi messaggio utente
    addMessage(inputMessage.trim(), true);
    
    // Processa l'input
    if (step.processor) {
      step.processor(inputMessage.trim());
    }

    setInputMessage('');
    setIsWaiting(false);
    
    // Vai al prossimo step
    setTimeout(() => {
      setCurrentStep(prev => prev + 1);
    }, 1000);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleUserInput();
    }
  };

  useEffect(() => {
    // Inizia l'onboarding
    setTimeout(() => {
      processStep();
    }, 1000);
  }, []);

  useEffect(() => {
    if (currentStep > 0 && currentStep < onboardingSteps.length) {
      processStep();
    }
  }, [currentStep]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.95)',
      display: 'flex',
      flexDirection: 'column',
      zIndex: 1500
    }}>
      {/* Header */}
      <div style={{
        padding: '1rem',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
        background: 'rgba(0,0,0,0.3)',
        textAlign: 'center'
      }}>
        <h2 style={{ 
          color: 'white', 
          fontSize: '1.25rem', 
          fontWeight: '600', 
          margin: 0,
          marginBottom: '0.5rem'
        }}>
          Configurazione Iniziale
        </h2>
        <p style={{ 
          color: 'rgba(255,255,255,0.7)', 
          fontSize: '0.875rem', 
          margin: 0 
        }}>
          Soul vuole conoscerti meglio
        </p>
      </div>

      {/* Messages */}
      <div 
        ref={chatContainerRef}
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '1rem',
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem'
        }}
      >
        {messages.map((message) => (
          <ChatBubble
            key={message.id}
            message={message.content}
            isUser={message.isUser}
          />
        ))}
      </div>

      {/* Input */}
      {isWaiting && (
        <div style={{
          padding: '1rem',
          borderTop: '1px solid rgba(255,255,255,0.1)',
          background: 'rgba(0,0,0,0.3)',
          display: 'flex',
          gap: '0.5rem'
        }}>
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Scrivi la tua risposta..."
            style={{
              flex: 1,
              padding: '0.75rem',
              borderRadius: '0.5rem',
              border: '1px solid rgba(255,255,255,0.3)',
              background: 'rgba(255,255,255,0.1)',
              color: 'white',
              fontSize: '1rem',
              outline: 'none'
            }}
            autoFocus
          />
          <button
            onClick={handleUserInput}
            disabled={!inputMessage.trim()}
            style={{
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              border: 'none',
              background: inputMessage.trim() ? '#6366f1' : 'rgba(99, 102, 241, 0.5)',
              color: 'white',
              fontSize: '1rem',
              cursor: inputMessage.trim() ? 'pointer' : 'not-allowed'
            }}
          >
            →
          </button>
        </div>
      )}
    </div>
  );
};

export default OnboardingChat;
