import React, { useState, useEffect, useRef } from 'react';
import ChatBubble from './ChatBubble';
import { completeOnboarding } from '../lib/auth';

interface OnboardingChatProps {
  userId: string;
  onComplete: (userData: { name: string; age: number }) => void;
}

interface OnboardingMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isTyping?: boolean;
}

const OnboardingChat: React.FC<OnboardingChatProps> = ({ userId, onComplete }) => {
  const [messages, setMessages] = useState<OnboardingMessage[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [inputMessage, setInputMessage] = useState('');
  const [isWaiting, setIsWaiting] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [userData, setUserData] = useState({ name: '', age: 0 });
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const hasProcessedStep = useRef<Set<number>>(new Set());
  const isInitialized = useRef(false);

  const totalSteps = 6; // Numero totale di step per la progress bar

  const onboardingSteps = [
    {
      message: "Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟",
      waitForInput: false,
      delay: 1500
    },
    {
      message: "Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...",
      waitForInput: false,
      delay: 2000
    },
    {
      message: "Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊",
      waitForInput: true,
      validator: (input: string) => {
        if (input.trim().length < 2) return "Il nome deve essere di almeno 2 caratteri 😅";
        if (input.trim().length > 50) return "Il nome è un po' troppo lungo, puoi abbreviarlo?";
        if (!/^[a-zA-ZÀ-ÿ\s]+$/.test(input.trim())) return "Il nome può contenere solo lettere e spazi";
        return null;
      },
      processor: (input: string) => {
        setUserData(prev => ({ ...prev, name: input.trim() }));
      }
    },
    {
      message: (name: string) => `${name}... che bel nome! Mi piace molto come suona. 💫`,
      waitForInput: false,
      delay: 1800
    },
    {
      message: "Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯",
      waitForInput: true,
      validator: (input: string) => {
        const age = parseInt(input);
        if (isNaN(age)) return "Per favore inserisci un numero valido 🔢";
        if (age < 13) return "Devi avere almeno 13 anni per usare SoulTalk 📚";
        if (age > 120) return "Inserisci un'età realistica, per favore! 😄";
        return null;
      },
      processor: (input: string) => {
        setUserData(prev => ({ ...prev, age: parseInt(input) }));
      }
    },
    {
      message: (name: string, age: number) => {
        if (age < 18) return `Perfetto ${name}! ${age} anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡`;
        if (age < 30) return `Fantastico ${name}! ${age} anni... un'età perfetta per esplorare nuove idee insieme! ✨`;
        if (age < 50) return `Ottimo ${name}! ${age} anni... hai esperienza e saggezza, sarà bellissimo parlare con te! 🌟`;
        return `Meraviglioso ${name}! ${age} anni... la tua esperienza di vita sarà preziosa per le nostre conversazioni! 🎭`;
      },
      waitForInput: false,
      delay: 2200
    },
    {
      message: "Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀",
      waitForInput: false,
      isLast: true,
      delay: 2500
    }
  ];

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTo({
            top: chatContainerRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  };

  const addMessage = (content: string, isUser: boolean, withTyping: boolean = false) => {
    const newMessage: OnboardingMessage = {
      id: `onb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      isUser,
      timestamp: new Date(),
      isTyping: withTyping
    };
    setMessages(prev => [...prev, newMessage]);
    setTimeout(scrollToBottom, 50);
  };

  const addMessageWithTyping = async (content: string) => {
    // Mostra indicatore typing
    setIsTyping(true);

    // Calcola tempo di typing basato sulla lunghezza del messaggio
    const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);

    await new Promise(resolve => setTimeout(resolve, typingTime));

    // Nascondi typing e aggiungi messaggio
    setIsTyping(false);
    addMessage(content, false);
  };

  const processStep = async () => {
    // Previeni chiamate multiple per lo stesso step
    if (hasProcessedStep.current.has(currentStep)) {
      return;
    }

    hasProcessedStep.current.add(currentStep);
    const step = onboardingSteps[currentStep];

    // Determina il messaggio da mostrare
    let messageContent: string;
    if (typeof step.message === 'function') {
      messageContent = step.message(userData.name, userData.age);
    } else {
      messageContent = step.message;
    }

    // Aggiungi messaggio con effetto typing
    await addMessageWithTyping(messageContent);

    if (step.isLast) {
      // Completa l'onboarding con delay personalizzato
      setTimeout(async () => {
        await completeOnboarding(userId);
        onComplete(userData);
      }, step.delay || 2500);
      return;
    }

    if (!step.waitForInput) {
      setTimeout(() => {
        setCurrentStep(prev => prev + 1);
      }, step.delay || 2000);
    } else {
      setIsWaiting(true);
    }
  };

  const handleUserInput = () => {
    if (!inputMessage.trim() || !isWaiting) return;

    const step = onboardingSteps[currentStep];

    // Validazione input
    if (step.validator) {
      const error = step.validator(inputMessage.trim());
      if (error) {
        addMessage(error, false);
        return;
      }
    }

    // Aggiungi messaggio utente
    addMessage(inputMessage.trim(), true);

    // Processa l'input
    if (step.processor) {
      step.processor(inputMessage.trim());
    }

    setInputMessage('');
    setIsWaiting(false);

    // Vai al prossimo step
    setTimeout(() => {
      setCurrentStep(prev => prev + 1);
    }, 1000);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleUserInput();
    }
  };

  useEffect(() => {
    // Inizializzazione una sola volta
    if (!isInitialized.current && currentStep === 0) {
      isInitialized.current = true;
      const timer = setTimeout(() => {
        processStep();
      }, 1000);
      return () => clearTimeout(timer);
    }
    // Per tutti gli altri step, processa solo se non già processato
    else if (currentStep > 0 && currentStep < onboardingSteps.length && !hasProcessedStep.current.has(currentStep)) {
      processStep();
    }
  }, [currentStep]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.95)',
      display: 'flex',
      flexDirection: 'column',
      zIndex: 1500
    }}>
      {/* Header con progress bar */}
      <div style={{
        padding: '1rem',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
        background: 'rgba(0,0,0,0.3)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '1rem', marginBottom: '1rem' }}>
          <div style={{
            width: '50px',
            height: '50px',
            borderRadius: '50%',
            background: `radial-gradient(circle, #8b5cf6 0%, transparent 70%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.5rem',
            animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'
          }}>
            🌟
          </div>
          <div style={{ textAlign: 'left' }}>
            <h2 style={{
              color: 'white',
              fontSize: '1.25rem',
              fontWeight: '600',
              margin: 0,
              marginBottom: '0.25rem'
            }}>
              Configurazione Iniziale
            </h2>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '0.875rem',
              margin: 0
            }}>
              Soul vuole conoscerti meglio
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div style={{
          width: '100%',
          height: '4px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '2px',
          overflow: 'hidden'
        }}>
          <div style={{
            width: `${(currentStep / totalSteps) * 100}%`,
            height: '100%',
            background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',
            borderRadius: '2px',
            transition: 'width 0.5s ease-in-out'
          }} />
        </div>

        <div style={{
          textAlign: 'center',
          marginTop: '0.5rem',
          color: 'rgba(255,255,255,0.6)',
          fontSize: '0.75rem'
        }}>
          Step {currentStep + 1} di {totalSteps + 1}
        </div>
      </div>

      {/* Messages */}
      <div 
        ref={chatContainerRef}
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: '1rem',
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem'
        }}
      >
        {messages.map((message) => (
          <ChatBubble
            key={message.id}
            message={message.content}
            isUser={message.isUser}
          />
        ))}

        {/* Indicatore typing */}
        {isTyping && (
          <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '1rem' }}>
            <div style={{
              background: 'rgba(255,255,255,0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '1rem',
              padding: '0.75rem 1rem',
              border: '1px solid rgba(255,255,255,0.2)',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <div style={{ display: 'flex', gap: '0.25rem' }}>
                <div style={{
                  width: '6px',
                  height: '6px',
                  backgroundColor: '#8b5cf6',
                  borderRadius: '50%',
                  animation: 'bounce 1.4s ease-in-out infinite'
                }} />
                <div style={{
                  width: '6px',
                  height: '6px',
                  backgroundColor: '#8b5cf6',
                  borderRadius: '50%',
                  animation: 'bounce 1.4s ease-in-out infinite',
                  animationDelay: '0.2s'
                }} />
                <div style={{
                  width: '6px',
                  height: '6px',
                  backgroundColor: '#8b5cf6',
                  borderRadius: '50%',
                  animation: 'bounce 1.4s ease-in-out infinite',
                  animationDelay: '0.4s'
                }} />
              </div>
              <span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '0.875rem' }}>
                Soul sta scrivendo...
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Input Area Migliorata */}
      {isWaiting && (
        <div style={{
          padding: '1rem',
          borderTop: '1px solid rgba(255,255,255,0.1)',
          background: 'rgba(0,0,0,0.3)'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '1rem',
            padding: '0.5rem',
            border: '1px solid rgba(255,255,255,0.1)',
            display: 'flex',
            gap: '0.5rem',
            alignItems: 'center'
          }}>
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Scrivi la tua risposta..."
              style={{
                flex: 1,
                padding: '0.75rem 1rem',
                borderRadius: '0.75rem',
                border: 'none',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                fontSize: '1rem',
                outline: 'none',
                backdropFilter: 'blur(10px)'
              }}
              autoFocus
            />
            <button
              onClick={handleUserInput}
              disabled={!inputMessage.trim()}
              style={{
                width: '48px',
                height: '48px',
                borderRadius: '50%',
                border: 'none',
                background: inputMessage.trim()
                  ? 'linear-gradient(135deg, #8b5cf6, #a855f7)'
                  : 'rgba(139, 92, 246, 0.3)',
                color: 'white',
                fontSize: '1.2rem',
                cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.2s ease',
                transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'
              }}
            >
              →
            </button>
          </div>

          <div style={{
            textAlign: 'center',
            marginTop: '0.75rem',
            color: 'rgba(255,255,255,0.5)',
            fontSize: '0.75rem'
          }}>
            Premi Invio per inviare
          </div>
        </div>
      )}
    </div>
  );
};

export default OnboardingChat;
