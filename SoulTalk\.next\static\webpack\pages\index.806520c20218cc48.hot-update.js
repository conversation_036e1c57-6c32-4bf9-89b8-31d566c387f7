"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOnboardingStatus: () => (/* binding */ checkOnboardingStatus),\n/* harmony export */   completeOnboarding: () => (/* binding */ completeOnboarding),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(pages-dir-browser)/./node_modules/bcryptjs/index.js\");\n\n\n// Funzione per hash della password\nconst hashPassword = async (password)=>{\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n};\n// Funzione per verificare la password\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n};\n// Registrazione nuovo utente\nconst registerUser = async (username, password)=>{\n    try {\n        // Controlla se l'username esiste già\n        const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('username').eq('username', username.toLowerCase()).single();\n        if (existingUser) {\n            return {\n                success: false,\n                error: 'Username già esistente'\n            };\n        }\n        // Crea hash della password\n        const passwordHash = await hashPassword(password);\n        // Genera ID utente\n        const userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        // Inserisci nella tabella auth\n        const { error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            password_hash: passwordHash\n        });\n        if (authError) {\n            console.error('Error creating auth:', authError);\n            return {\n                success: false,\n                error: 'Errore durante la registrazione'\n            };\n        }\n        // Crea profilo utente\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            name: '',\n            is_onboarded: false\n        });\n        if (profileError) {\n            console.error('Error creating profile:', profileError);\n            // Rollback: elimina l'auth se il profilo fallisce\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').delete().eq('id', userId);\n            return {\n                success: false,\n                error: 'Errore durante la creazione del profilo'\n            };\n        }\n        return {\n            success: true,\n            userId\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Login utente\nconst loginUser = async (username, password)=>{\n    try {\n        // Trova l'utente\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('*').eq('username', username.toLowerCase()).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Verifica password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Aggiorna last_login\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').update({\n            last_login: new Date().toISOString()\n        }).eq('id', user.id);\n        return {\n            success: true,\n            userId: user.id\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Verifica se l'utente è autenticato (controlla localStorage)\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const authData = localStorage.getItem('soul_auth');\n    if (!authData) return null;\n    try {\n        const { userId, timestamp } = JSON.parse(authData);\n        // Controlla se la sessione è scaduta (24 ore)\n        const now = Date.now();\n        const sessionAge = now - timestamp;\n        const maxAge = 24 * 60 * 60 * 1000 // 24 ore\n        ;\n        if (sessionAge > maxAge) {\n            localStorage.removeItem('soul_auth');\n            return null;\n        }\n        return userId;\n    } catch (e) {\n        localStorage.removeItem('soul_auth');\n        return null;\n    }\n};\n// Salva sessione utente\nconst saveUserSession = (userId)=>{\n    if (false) {}\n    const authData = {\n        userId,\n        timestamp: Date.now()\n    };\n    localStorage.setItem('soul_auth', JSON.stringify(authData));\n};\n// Logout utente\nconst logoutUser = ()=>{\n    if (false) {}\n    localStorage.removeItem('soul_auth');\n    localStorage.removeItem('soul_user_id'); // Rimuovi anche il vecchio ID\n};\n// Controlla se l'utente ha completato l'onboarding\nconst checkOnboardingStatus = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('is_onboarded').eq('id', userId).single();\n        if (error) {\n            console.log('Error checking onboarding status:', error);\n            // Se la tabella non esiste o l'utente non esiste, ha bisogno di onboarding\n            return false;\n        }\n        if (!data) {\n            console.log('No profile data found for user:', userId);\n            return false;\n        }\n        console.log('Onboarding status for user:', userId, 'is_onboarded:', data.is_onboarded);\n        return data.is_onboarded || false;\n    } catch (error) {\n        console.error('Exception in checkOnboardingStatus:', error);\n        return false;\n    }\n};\n// Completa l'onboarding\nconst completeOnboarding = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: true\n        }).eq('id', userId);\n        return !error;\n    } catch (e) {\n        return false;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/lib/auth.ts\n"));

/***/ })

});