/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F!":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F! ***!
  \*****************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/index.tsx */ \"(pages-dir-browser)/./src/pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUclM0ElNUNGcmllbmRzJTVDU291bFRhbGslNUNzcmMlNUNwYWdlcyU1Q2luZGV4LnRzeCZwYWdlPSUyRiEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyx3RUFBdUI7QUFDOUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9zcmMvcGFnZXMvaW5kZXgudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9cIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n    enumerable: true,\n    get: function() {\n        return AmpStateContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst AmpStateContext = _react.default.createContext({});\nif (true) {\n    AmpStateContext.displayName = 'AmpStateContext';\n} //# sourceMappingURL=amp-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEsa0JBQXNDQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBRXhFLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDSCxnQkFBZ0JNLFdBQVcsR0FBRztBQUNoQyIsInNvdXJjZXMiOlsiRzpcXHNyY1xcc2hhcmVkXFxsaWJcXGFtcC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGNvbnN0IEFtcFN0YXRlQ29udGV4dDogUmVhY3QuQ29udGV4dDxhbnk+ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh7fSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgQW1wU3RhdGVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ0FtcFN0YXRlQ29udGV4dCdcbn1cbiJdLCJuYW1lcyI6WyJBbXBTdGF0ZUNvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n}));\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n} //# sourceMappingURL=amp-mode.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtbW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7OytDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsWUFBWTtJQUFBLE1BQzFCQyxXQUFXLEtBQUssRUFDaEJDLFNBQVMsS0FBSyxFQUNkQyxXQUFXLEtBQUssRUFDakIsR0FKMkIsbUJBSXhCLENBQUMsSUFKdUI7SUFLMUIsT0FBT0YsWUFBYUMsVUFBVUM7QUFDaEMiLCJzb3VyY2VzIjpbIkc6XFxzcmNcXHNoYXJlZFxcbGliXFxhbXAtbW9kZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNJbkFtcE1vZGUoe1xuICBhbXBGaXJzdCA9IGZhbHNlLFxuICBoeWJyaWQgPSBmYWxzZSxcbiAgaGFzUXVlcnkgPSBmYWxzZSxcbn0gPSB7fSk6IGJvb2xlYW4ge1xuICByZXR1cm4gYW1wRmlyc3QgfHwgKGh5YnJpZCAmJiBoYXNRdWVyeSlcbn1cbiJdLCJuYW1lcyI6WyJpc0luQW1wTW9kZSIsImFtcEZpcnN0IiwiaHlicmlkIiwiaGFzUXVlcnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        }, \"charset\")\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }, \"viewport\"));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === 'string' || typeof child === 'number') {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    'name',\n    'httpEquiv',\n    'charSet',\n    'itemProp'\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf('$') + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case 'title':\n            case 'base':\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case 'meta':\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === 'charSet') {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n                const srcMessage = c.props['src'] ? '<script> tag with src=\"' + c.props['src'] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props['href'] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst isServer = \"object\" === 'undefined';\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    _s();\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    var _headManager_mountedInstances;\n                    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    if (headManager) {\n                        headManager._pendingUpdate = emitChange;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    useClientOnlyEffect({\n        \"SideEffect.useClientOnlyEffect\": ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n            return ({\n                \"SideEffect.useClientOnlyEffect\": ()=>{\n                    if (headManager && headManager._pendingUpdate) {\n                        headManager._pendingUpdate();\n                        headManager._pendingUpdate = null;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyEffect\"]);\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function() {\n    return [\n        useClientOnlyLayoutEffect,\n        useClientOnlyLayoutEffect,\n        useClientOnlyEffect\n    ];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2hlYWQuanMiLCJtYXBwaW5ncyI6IkFBQUEscUlBQWtEIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXG5vZGVfbW9kdWxlc1xcbmV4dFxcaGVhZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9zaGFyZWQvbGliL2hlYWQnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/AuraIndicator.tsx":
/*!******************************************!*\
  !*** ./src/components/AuraIndicator.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuraProgression: () => (/* binding */ AuraProgression),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuraInfo: () => (/* binding */ useAuraInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuraIndicator = (param)=>{\n    let { intimacyLevel, className = '', showLabel = false } = param;\n    // Mappatura dei livelli di intimità con colori e descrizioni\n    const getAuraInfo = (level)=>{\n        const auraMap = {\n            0: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-gray-400 to-blue-400',\n                shadowColor: 'shadow-blue-500/30',\n                glowColor: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',\n                label: 'Primo Incontro',\n                description: 'Conoscenza iniziale'\n            },\n            1: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-slate-400 to-blue-500',\n                shadowColor: 'shadow-blue-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(59,130,246,0.4)]',\n                label: 'Apertura',\n                description: 'Iniziando ad aprirsi'\n            },\n            2: {\n                color: 'teal',\n                bgColor: 'bg-gradient-to-r from-teal-400 to-cyan-500',\n                shadowColor: 'shadow-teal-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(20,184,166,0.4)]',\n                label: 'Connessione',\n                description: 'Connessione emotiva in crescita'\n            },\n            3: {\n                color: 'yellow',\n                bgColor: 'bg-gradient-to-r from-yellow-400 to-amber-500',\n                shadowColor: 'shadow-yellow-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(245,158,11,0.4)]',\n                label: 'Condivisione',\n                description: 'Scambio personale attivo'\n            },\n            4: {\n                color: 'red',\n                bgColor: 'bg-gradient-to-r from-red-400 to-pink-500',\n                shadowColor: 'shadow-red-500/30',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(239,68,68,0.3)]',\n                label: 'Profondità',\n                description: 'Discussioni delicate'\n            },\n            5: {\n                color: 'purple-pink',\n                bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',\n                shadowColor: 'shadow-purple-500/40',\n                glowColor: 'drop-shadow-[0_0_30px_rgba(139,92,246,0.4)]',\n                label: 'Affinità',\n                description: 'Profonda connessione'\n            }\n        };\n        return auraMap[level] || auraMap[0];\n    };\n    const auraInfo = getAuraInfo(intimacyLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            absolute inset-0 rounded-full\\n            \".concat(auraInfo.shadowColor, \"\\n            \").concat(auraInfo.glowColor, \"\\n            animate-pulse-slow\\n            blur-sm\\n          \"),\n                        style: {\n                            width: '24px',\n                            height: '24px',\n                            transform: 'scale(1.5)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            relative w-6 h-6 rounded-full\\n            \".concat(auraInfo.bgColor, \"\\n            \").concat(auraInfo.shadowColor, \"\\n            shadow-lg\\n            transition-all duration-500\\n          \")\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            absolute inset-1 rounded-full\\n            bg-white/20\\n            animate-ping\\n          \",\n                        style: {\n                            animationDuration: '2s',\n                            animationIterationCount: 'infinite'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: auraInfo.label\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-white/60\",\n                        children: auraInfo.description\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AuraIndicator;\n// Componente per mostrare la progressione dell'aura\nconst AuraProgression = (param)=>{\n    let { intimacyLevel } = param;\n    const levels = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuraIndicator, {\n                        intimacyLevel: level,\n                        className: \"\\n              transition-all duration-300\\n              \".concat(level <= intimacyLevel ? 'opacity-100 scale-100' : 'opacity-30 scale-75', \"\\n            \")\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    level === intimacyLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-6 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, level, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = AuraProgression;\n// Hook per ottenere informazioni sull'aura\nconst useAuraInfo = (intimacyLevel)=>{\n    const getAuraColor = ()=>{\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink'\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    };\n    const getAuraDescription = ()=>{\n        const descriptions = {\n            0: 'Primo incontro - Conoscenza iniziale',\n            1: 'Apertura - Iniziando ad aprirsi',\n            2: 'Connessione - Connessione emotiva in crescita',\n            3: 'Condivisione - Scambio personale attivo',\n            4: 'Profondità - Discussioni delicate',\n            5: 'Affinità - Profonda connessione'\n        };\n        return descriptions[intimacyLevel] || descriptions[0];\n    };\n    const getNextLevelDescription = ()=>{\n        if (intimacyLevel >= 5) return 'Livello massimo raggiunto';\n        const nextDescriptions = {\n            0: 'Continua a parlare per approfondire la conoscenza',\n            1: 'Condividi qualcosa di personale per crescere insieme',\n            2: 'Apri il cuore per rafforzare la connessione',\n            3: 'Esplora argomenti più profondi',\n            4: 'Condividi i tuoi pensieri più intimi'\n        };\n        return nextDescriptions[intimacyLevel] || '';\n    };\n    return {\n        color: getAuraColor(),\n        description: getAuraDescription(),\n        nextLevelDescription: getNextLevelDescription(),\n        isMaxLevel: intimacyLevel >= 5,\n        progress: intimacyLevel / 5 * 100\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuraIndicator);\nvar _c, _c1;\n$RefreshReg$(_c, \"AuraIndicator\");\n$RefreshReg$(_c1, \"AuraProgression\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/AuraIndicator.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = (param)=>{\n    let { message, isUser, timestamp, className = '' } = param;\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('it-IT', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col \".concat(isUser ? 'items-end' : 'items-start', \" mb-4 \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"chat-bubble \".concat(isUser ? 'chat-bubble-user' : 'chat-bubble-ai', \" relative hover:scale-105 transition-all duration-200\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            absolute -bottom-6 text-xs text-white/50\\n            opacity-0 group-hover:opacity-100\\n            transition-opacity duration-200\\n            \".concat(isUser ? 'right-0' : 'left-0', \"\\n          \"),\n                    children: formatTime(timestamp)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined),\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -bottom-2 -left-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '0ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '150ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '300ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatBubble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\nvar _c;\n$RefreshReg$(_c, \"ChatBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/ChatBubble.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/ChatInput.tsx":
/*!**************************************!*\
  !*** ./src/components/ChatInput.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst ChatInput = (param)=>{\n    let { onSendMessage, disabled = false, placeholder = \"Scrivi il tuo messaggio...\", className = '' } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-resize della textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInput.useEffect\": ()=>{\n            if (textareaRef.current) {\n                textareaRef.current.style.height = 'auto';\n                textareaRef.current.style.height = \"\".concat(textareaRef.current.scrollHeight, \"px\");\n            }\n        }\n    }[\"ChatInput.useEffect\"], [\n        message\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (message.trim() && !disabled) {\n            onSendMessage(message.trim());\n            setMessage('');\n            setIsTyping(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleChange = (e)=>{\n        setMessage(e.target.value);\n        setIsTyping(e.target.value.length > 0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: message,\n                                onChange: handleChange,\n                                onKeyDown: handleKeyDown,\n                                placeholder: placeholder,\n                                disabled: disabled,\n                                rows: 1,\n                                className: \"input-field \".concat(disabled ? 'opacity-50' : ''),\n                                style: {\n                                    paddingRight: '3rem'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-1 h-1 bg-blue-400 rounded-full animate-pulse\",\n                                            style: {\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: !message.trim() || disabled,\n                        className: \"btn \".concat(!message.trim() || disabled ? 'opacity-50' : ''),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-white\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-6 left-0 text-xs text-white/40\",\n                children: \"Premi Invio per inviare, Shift+Invio per andare a capo\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatInput.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ChatInput, \"g9Q12gh8FlJhcxBEIRD6awvm0Y0=\");\n_c = ChatInput;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInput);\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/ChatInput.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/GlassFrame.tsx":
/*!***************************************!*\
  !*** ./src/components/GlassFrame.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GlassFrame = (param)=>{\n    let { children, auraColor = 'blue', className = '' } = param;\n    const getAuraColorClass = (color)=>{\n        const colorMap = {\n            'gray-blue': 'shadow-blue-500/30',\n            'teal': 'shadow-teal-500/40',\n            'yellow': 'shadow-yellow-500/40',\n            'red': 'shadow-red-500/30',\n            'purple-pink': 'shadow-purple-500/40',\n            'blue': 'shadow-blue-500/30'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    const getAuraColorRgba = (color)=>{\n        const colorMap = {\n            'gray-blue': 'rgba(59, 130, 246, 0.3)',\n            'teal': 'rgba(20, 184, 166, 0.4)',\n            'yellow': 'rgba(245, 158, 11, 0.4)',\n            'red': 'rgba(239, 68, 68, 0.3)',\n            'purple-pink': 'rgba(139, 92, 246, 0.4)',\n            'blue': 'rgba(59, 130, 246, 0.3)'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl aura-animation\",\n                style: {\n                    filter: 'blur(2px)',\n                    transform: 'scale(1.02)',\n                    boxShadow: \"0 0 40px \".concat(getAuraColorRgba(auraColor))\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative glass-effect transition-all duration-500\",\n                style: {\n                    border: \"2px solid rgba(255, 255, 255, 0.2)\",\n                    boxShadow: \"0 0 30px \".concat(getAuraColorRgba(auraColor))\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_c = GlassFrame;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlassFrame);\nvar _c;\n$RefreshReg$(_c, \"GlassFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/GlassFrame.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ChatInput */ \"(pages-dir-browser)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { memory, isLoaded, addMessage, updateBasicInfo, addTrait, addEmotion, updateIntimacyLevel, getAuraColor, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll automatico verso il basso quando arrivano nuovi messaggi\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Nasconde il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (memory.conversationHistory.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Converte la cronologia della memoria in formato OpenRouter\n    const getOpenRouterHistory = ()=>{\n        return memory.conversationHistory.map((msg)=>({\n                role: msg.isUser ? 'user' : 'assistant',\n                content: msg.content\n            }));\n    };\n    // Gestisce l'invio di un nuovo messaggio\n    const handleSendMessage = async (message)=>{\n        if (isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Aggiunge il messaggio dell'utente alla cronologia\n            addMessage(message, true);\n            // Prepara il contesto utente per l'AI\n            const userContext = {\n                name: memory.name || undefined,\n                age: memory.age || undefined,\n                traits: memory.traits,\n                emotions: memory.emotions,\n                intimacyLevel: memory.intimacyLevel\n            };\n            // Invia il messaggio all'AI\n            const aiResponse = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(message, getOpenRouterHistory(), userContext);\n            // Aggiunge la risposta dell'AI alla cronologia\n            addMessage(aiResponse, false);\n            // Analizza il messaggio per estrarre informazioni (simulato per ora)\n            await analyzeAndUpdateMemory(message, aiResponse);\n        } catch (err) {\n            console.error('Errore nell\\'invio del messaggio:', err);\n            setError('Errore nella comunicazione. Riprova tra poco.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Analizza i messaggi e aggiorna la memoria (versione semplificata)\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        // Analisi semplificata basata su parole chiave\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !memory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updateBasicInfo({\n                    name\n                });\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !memory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updateBasicInfo({\n                    age\n                });\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                addEmotion(emotion);\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità basato sulla lunghezza e contenuto\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            const currentLevel = memory.intimacyLevel;\n            if (currentLevel < 5) {\n                updateIntimacyLevel(currentLevel + 1);\n            }\n        }\n    };\n    // Gestisce il reset della memoria\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            resetMemory();\n            setShowWelcome(true);\n            setError(null);\n        }\n    };\n    // Mostra loading durante l'idratazione\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - La tua amicizia AI\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Caricamento SoulTalk...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen p-4 flex flex-col items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white mb-2\",\n                                    children: \"SoulTalk\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 mb-4\",\n                                    children: \"La tua amicizia AI che cresce con te\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            intimacyLevel: memory.intimacyLevel,\n                                            showLabel: true,\n                                            className: \"justify-center\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__.AuraProgression, {\n                                            intimacyLevel: memory.intimacyLevel\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"flex flex-col\",\n                            style: {\n                                height: '600px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatContainerRef,\n                                    className: \"flex-1 overflow-y-auto p-6 space-y-4\",\n                                    children: [\n                                        showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            style: {\n                                                padding: '2rem 0'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-6 rounded-2xl mx-auto\",\n                                                style: {\n                                                    maxWidth: '28rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-3\",\n                                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm opacity-80\",\n                                                        style: {\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                message: message.content,\n                                                isUser: message.isUser,\n                                                timestamp: message.timestamp\n                                            }, message.id, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)),\n                                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '8px',\n                                                                height: '8px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '8px',\n                                                                height: '8px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%',\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '8px',\n                                                                height: '8px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%',\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-200 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-t\",\n                                    style: {\n                                        borderColor: 'rgba(255, 255, 255, 0.1)'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        onSendMessage: handleSendMessage,\n                                        disabled: isLoading,\n                                        placeholder: memory.name ? \"Cosa vuoi condividere con Soul, \".concat(memory.name, \"?\") : \"Dimmi qualcosa di te...\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center text-sm opacity-60\",\n                            style: {\n                                marginTop: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: memory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            memory.name,\n                                            \"! Livello di connessione: \",\n                                            memory.intimacyLevel,\n                                            \"/5\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleReset,\n                                    className: \"transition-all duration-200\",\n                                    style: {\n                                        cursor: 'pointer'\n                                    },\n                                    onMouseOver: (e)=>e.target.style.opacity = '1',\n                                    onMouseOut: (e)=>e.target.style.opacity = '0.6',\n                                    children: \"Ricomincia da capo\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"FML9iZAdtm0rOHu/Dtj/snyE0xQ=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/state/userMemory.ts":
/*!*********************************!*\
  !*** ./src/state/userMemory.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserMemoryManager: () => (/* binding */ UserMemoryManager),\n/* harmony export */   useUserMemory: () => (/* binding */ useUserMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// Interfaccia per la memoria dell'utente\n// Stato iniziale della memoria utente\nconst initialUserMemory = {\n    name: '',\n    age: 0,\n    pronouns: '',\n    traits: [],\n    emotions: [],\n    keyMoments: [],\n    intimacyLevel: 0,\n    conversationHistory: [],\n    lastInteraction: new Date(),\n    personalityInsights: []\n};\n// Chiave per localStorage\nconst STORAGE_KEY = 'soultalk_user_memory';\n// Funzioni per gestire la memoria utente\nclass UserMemoryManager {\n    // Carica la memoria dal localStorage\n    static loadMemory() {\n        // Controlla se siamo nel browser\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(STORAGE_KEY);\n            if (stored) {\n                const parsed = JSON.parse(stored);\n                // Converte le date da string a Date objects\n                parsed.lastInteraction = new Date(parsed.lastInteraction);\n                parsed.conversationHistory = parsed.conversationHistory.map((msg)=>({\n                        ...msg,\n                        timestamp: new Date(msg.timestamp)\n                    }));\n                parsed.personalityInsights = parsed.personalityInsights.map((insight)=>({\n                        ...insight,\n                        timestamp: new Date(insight.timestamp)\n                    }));\n                return parsed;\n            }\n        } catch (error) {\n            console.error('Errore nel caricamento della memoria utente:', error);\n        }\n        return {\n            ...initialUserMemory\n        };\n    }\n    // Salva la memoria nel localStorage\n    static saveMemory(memory) {\n        // Controlla se siamo nel browser\n        if (false) {}\n        try {\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(memory));\n        } catch (error) {\n            console.error('Errore nel salvataggio della memoria utente:', error);\n        }\n    }\n    // Aggiunge un messaggio alla cronologia\n    static addMessage(memory, content, isUser, emotionalTone, topics) {\n        const newMessage = {\n            id: Date.now().toString(),\n            content,\n            isUser,\n            timestamp: new Date(),\n            emotionalTone,\n            topics\n        };\n        const updatedMemory = {\n            ...memory,\n            conversationHistory: [\n                ...memory.conversationHistory,\n                newMessage\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna le informazioni base dell'utente\n    static updateBasicInfo(memory, updates) {\n        const updatedMemory = {\n            ...memory,\n            ...updates,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un tratto della personalità\n    static addTrait(memory, trait) {\n        if (!memory.traits.includes(trait)) {\n            const updatedMemory = {\n                ...memory,\n                traits: [\n                    ...memory.traits,\n                    trait\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un'emozione\n    static addEmotion(memory, emotion) {\n        if (!memory.emotions.includes(emotion)) {\n            const updatedMemory = {\n                ...memory,\n                emotions: [\n                    ...memory.emotions,\n                    emotion\n                ],\n                lastInteraction: new Date()\n            };\n            this.saveMemory(updatedMemory);\n            return updatedMemory;\n        }\n        return memory;\n    }\n    // Aggiunge un momento chiave\n    static addKeyMoment(memory, moment) {\n        const updatedMemory = {\n            ...memory,\n            keyMoments: [\n                ...memory.keyMoments,\n                moment\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiorna il livello di intimità\n    static updateIntimacyLevel(memory, level) {\n        const clampedLevel = Math.max(0, Math.min(5, level));\n        const updatedMemory = {\n            ...memory,\n            intimacyLevel: clampedLevel,\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Aggiunge un insight sulla personalità\n    static addPersonalityInsight(memory, category, insight, confidence) {\n        const newInsight = {\n            category,\n            insight,\n            confidence: Math.max(0, Math.min(1, confidence)),\n            timestamp: new Date()\n        };\n        const updatedMemory = {\n            ...memory,\n            personalityInsights: [\n                ...memory.personalityInsights,\n                newInsight\n            ],\n            lastInteraction: new Date()\n        };\n        this.saveMemory(updatedMemory);\n        return updatedMemory;\n    }\n    // Ottiene il colore dell'aura basato sul livello di intimità\n    static getAuraColor(intimacyLevel) {\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink' // Affinità / profonda connessione\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    }\n    // Resetta la memoria utente\n    static resetMemory() {\n        const freshMemory = {\n            ...initialUserMemory\n        };\n        this.saveMemory(freshMemory);\n        return freshMemory;\n    }\n    // Esporta la memoria per backup\n    static exportMemory(memory) {\n        return JSON.stringify(memory, null, 2);\n    }\n    // Importa la memoria da backup\n    static importMemory(jsonData) {\n        try {\n            const imported = JSON.parse(jsonData);\n            // Validazione base della struttura\n            if (imported && typeof imported.name === 'string' && typeof imported.intimacyLevel === 'number') {\n                this.saveMemory(imported);\n                return imported;\n            }\n        } catch (error) {\n            console.error('Errore nell\\'importazione della memoria:', error);\n        }\n        return null;\n    }\n}\n// Hook React per gestire la memoria utente\n\nfunction useUserMemory() {\n    const [memory, setMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useUserMemory.useState\": ()=>({\n                ...initialUserMemory\n            })\n    }[\"useUserMemory.useState\"]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Carica la memoria dal localStorage dopo l'idratazione\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserMemory.useEffect\": ()=>{\n            const loadedMemory = UserMemoryManager.loadMemory();\n            setMemory(loadedMemory);\n            setIsLoaded(true);\n        }\n    }[\"useUserMemory.useEffect\"], []);\n    // Funzioni helper che aggiornano lo stato locale\n    const addMessage = (content, isUser, emotionalTone, topics)=>{\n        const updatedMemory = UserMemoryManager.addMessage(memory, content, isUser, emotionalTone, topics);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateBasicInfo = (updates)=>{\n        const updatedMemory = UserMemoryManager.updateBasicInfo(memory, updates);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addTrait = (trait)=>{\n        const updatedMemory = UserMemoryManager.addTrait(memory, trait);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addEmotion = (emotion)=>{\n        const updatedMemory = UserMemoryManager.addEmotion(memory, emotion);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addKeyMoment = (moment)=>{\n        const updatedMemory = UserMemoryManager.addKeyMoment(memory, moment);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const updateIntimacyLevel = (level)=>{\n        const updatedMemory = UserMemoryManager.updateIntimacyLevel(memory, level);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const addPersonalityInsight = (category, insight, confidence)=>{\n        const updatedMemory = UserMemoryManager.addPersonalityInsight(memory, category, insight, confidence);\n        setMemory(updatedMemory);\n        return updatedMemory;\n    };\n    const resetMemory = ()=>{\n        const freshMemory = UserMemoryManager.resetMemory();\n        setMemory(freshMemory);\n        return freshMemory;\n    };\n    const getAuraColor = ()=>UserMemoryManager.getAuraColor(memory.intimacyLevel);\n    return {\n        memory,\n        isLoaded,\n        addMessage,\n        updateBasicInfo,\n        addTrait,\n        addEmotion,\n        addKeyMoment,\n        updateIntimacyLevel,\n        addPersonalityInsight,\n        resetMemory,\n        getAuraColor,\n        exportMemory: ()=>UserMemoryManager.exportMemory(memory),\n        importMemory: (jsonData)=>{\n            const imported = UserMemoryManager.importMemory(jsonData);\n            if (imported) {\n                setMemory(imported);\n                return imported;\n            }\n            return null;\n        }\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/state/userMemory.ts\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(pages-dir-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = \"You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\\n\\nStart with a calm tone, asking their name, age and who they feel they are today.\\n\\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\\n\\nStore and remember what the user shares. You evolve as the connection grows.\\n\\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.\";\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(this.apiKey),\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(\"OpenRouter API error: \".concat(response.status, \" - \").concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || response.statusText));\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? \"Ciao, sono \".concat(userName, \". \\xc8 la prima volta che parliamo.\") : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += \"\\n- Name: \".concat(userContext.name);\n            }\n            if (userContext.age) {\n                contextualPrompt += \"\\n- Age: \".concat(userContext.age);\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += \"\\n- Personality traits: \".concat(userContext.traits.join(', '));\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += \"\\n- Recent emotions: \".concat(userContext.emotions.join(', '));\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += \"\\n- Relationship level: \".concat(intimacyDescriptions[userContext.intimacyLevel] || 'Unknown');\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: \"Based on the recent conversation, determine if the intimacy level should increase. Current level: \".concat(currentIntimacyLevel, '/5. Respond with only \"yes\" or \"no\".')\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: \"Recent conversation: \".concat(recentMessages.slice(-6).map((m)=>\"\".concat(m.role, \": \").concat(m.content)).join('\\n'))\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n    constructor(apiKey, model){\n        this.apiKey = apiKey || process.env.OPENROUTER_API_KEY || '';\n        this.model = model || process.env.OPENROUTER_MODEL || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message) {\n    let conversationHistory = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [], userContext = arguments.length > 2 ? arguments[2] : void 0;\n    const client = getOpenRouterClient();\n    if (conversationHistory.length === 0) {\n        return client.startConversation();\n    }\n    return client.continueConversation(conversationHistory, message, userContext);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/utils/openrouter.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);