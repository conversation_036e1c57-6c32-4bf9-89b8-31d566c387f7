/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F!":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F! ***!
  \*****************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/index.tsx */ \"(pages-dir-browser)/./src/pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUclM0ElNUNGcmllbmRzJTVDU291bFRhbGslNUNzcmMlNUNwYWdlcyU1Q2luZGV4LnRzeCZwYWdlPSUyRiEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyx3RUFBdUI7QUFDOUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9zcmMvcGFnZXMvaW5kZXgudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9cIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n    enumerable: true,\n    get: function() {\n        return AmpStateContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst AmpStateContext = _react.default.createContext({});\nif (true) {\n    AmpStateContext.displayName = 'AmpStateContext';\n} //# sourceMappingURL=amp-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEsa0JBQXNDQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBRXhFLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDSCxnQkFBZ0JNLFdBQVcsR0FBRztBQUNoQyIsInNvdXJjZXMiOlsiRzpcXHNyY1xcc2hhcmVkXFxsaWJcXGFtcC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGNvbnN0IEFtcFN0YXRlQ29udGV4dDogUmVhY3QuQ29udGV4dDxhbnk+ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh7fSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgQW1wU3RhdGVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ0FtcFN0YXRlQ29udGV4dCdcbn1cbiJdLCJuYW1lcyI6WyJBbXBTdGF0ZUNvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n}));\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n} //# sourceMappingURL=amp-mode.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtbW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7OytDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsWUFBWTtJQUFBLE1BQzFCQyxXQUFXLEtBQUssRUFDaEJDLFNBQVMsS0FBSyxFQUNkQyxXQUFXLEtBQUssRUFDakIsR0FKMkIsbUJBSXhCLENBQUMsSUFKdUI7SUFLMUIsT0FBT0YsWUFBYUMsVUFBVUM7QUFDaEMiLCJzb3VyY2VzIjpbIkc6XFxzcmNcXHNoYXJlZFxcbGliXFxhbXAtbW9kZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNJbkFtcE1vZGUoe1xuICBhbXBGaXJzdCA9IGZhbHNlLFxuICBoeWJyaWQgPSBmYWxzZSxcbiAgaGFzUXVlcnkgPSBmYWxzZSxcbn0gPSB7fSk6IGJvb2xlYW4ge1xuICByZXR1cm4gYW1wRmlyc3QgfHwgKGh5YnJpZCAmJiBoYXNRdWVyeSlcbn1cbiJdLCJuYW1lcyI6WyJpc0luQW1wTW9kZSIsImFtcEZpcnN0IiwiaHlicmlkIiwiaGFzUXVlcnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        }, \"charset\")\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }, \"viewport\"));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === 'string' || typeof child === 'number') {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    'name',\n    'httpEquiv',\n    'charSet',\n    'itemProp'\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf('$') + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case 'title':\n            case 'base':\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case 'meta':\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === 'charSet') {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n                const srcMessage = c.props['src'] ? '<script> tag with src=\"' + c.props['src'] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props['href'] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst isServer = \"object\" === 'undefined';\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    _s();\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    var _headManager_mountedInstances;\n                    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    if (headManager) {\n                        headManager._pendingUpdate = emitChange;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    useClientOnlyEffect({\n        \"SideEffect.useClientOnlyEffect\": ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n            return ({\n                \"SideEffect.useClientOnlyEffect\": ()=>{\n                    if (headManager && headManager._pendingUpdate) {\n                        headManager._pendingUpdate();\n                        headManager._pendingUpdate = null;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyEffect\"]);\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function() {\n    return [\n        useClientOnlyLayoutEffect,\n        useClientOnlyLayoutEffect,\n        useClientOnlyEffect\n    ];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2hlYWQuanMiLCJtYXBwaW5ncyI6IkFBQUEscUlBQWtEIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXG5vZGVfbW9kdWxlc1xcbmV4dFxcaGVhZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9zaGFyZWQvbGliL2hlYWQnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/AuraIndicator.tsx":
/*!******************************************!*\
  !*** ./src/components/AuraIndicator.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuraProgression: () => (/* binding */ AuraProgression),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuraInfo: () => (/* binding */ useAuraInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuraIndicator = (param)=>{\n    let { intimacyLevel, className = '', showLabel = false } = param;\n    // Mappatura dei livelli di intimità con colori e descrizioni\n    const getAuraInfo = (level)=>{\n        const auraMap = {\n            0: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-gray-400 to-blue-400',\n                shadowColor: 'shadow-blue-500/30',\n                glowColor: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',\n                label: 'Primo Incontro',\n                description: 'Conoscenza iniziale'\n            },\n            1: {\n                color: 'gray-blue',\n                bgColor: 'bg-gradient-to-r from-slate-400 to-blue-500',\n                shadowColor: 'shadow-blue-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(59,130,246,0.4)]',\n                label: 'Apertura',\n                description: 'Iniziando ad aprirsi'\n            },\n            2: {\n                color: 'teal',\n                bgColor: 'bg-gradient-to-r from-teal-400 to-cyan-500',\n                shadowColor: 'shadow-teal-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(20,184,166,0.4)]',\n                label: 'Connessione',\n                description: 'Connessione emotiva in crescita'\n            },\n            3: {\n                color: 'yellow',\n                bgColor: 'bg-gradient-to-r from-yellow-400 to-amber-500',\n                shadowColor: 'shadow-yellow-500/40',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(245,158,11,0.4)]',\n                label: 'Condivisione',\n                description: 'Scambio personale attivo'\n            },\n            4: {\n                color: 'red',\n                bgColor: 'bg-gradient-to-r from-red-400 to-pink-500',\n                shadowColor: 'shadow-red-500/30',\n                glowColor: 'drop-shadow-[0_0_25px_rgba(239,68,68,0.3)]',\n                label: 'Profondità',\n                description: 'Discussioni delicate'\n            },\n            5: {\n                color: 'purple-pink',\n                bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',\n                shadowColor: 'shadow-purple-500/40',\n                glowColor: 'drop-shadow-[0_0_30px_rgba(139,92,246,0.4)]',\n                label: 'Affinità',\n                description: 'Profonda connessione'\n            }\n        };\n        return auraMap[level] || auraMap[0];\n    };\n    const auraInfo = getAuraInfo(intimacyLevel);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            absolute inset-0 rounded-full\\n            \".concat(auraInfo.shadowColor, \"\\n            \").concat(auraInfo.glowColor, \"\\n            animate-pulse-slow\\n            blur-sm\\n          \"),\n                        style: {\n                            width: '24px',\n                            height: '24px',\n                            transform: 'scale(1.5)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            relative w-6 h-6 rounded-full\\n            \".concat(auraInfo.bgColor, \"\\n            \").concat(auraInfo.shadowColor, \"\\n            shadow-lg\\n            transition-all duration-500\\n          \")\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            absolute inset-1 rounded-full\\n            bg-white/20\\n            animate-ping\\n          \",\n                        style: {\n                            animationDuration: '2s',\n                            animationIterationCount: 'infinite'\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium text-white\",\n                        children: auraInfo.label\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-white/60\",\n                        children: auraInfo.description\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AuraIndicator;\n// Componente per mostrare la progressione dell'aura\nconst AuraProgression = (param)=>{\n    let { intimacyLevel } = param;\n    const levels = [\n        0,\n        1,\n        2,\n        3,\n        4,\n        5\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: levels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuraIndicator, {\n                        intimacyLevel: level,\n                        className: \"\\n              transition-all duration-300\\n              \".concat(level <= intimacyLevel ? 'opacity-100 scale-100' : 'opacity-30 scale-75', \"\\n            \")\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    level === intimacyLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-6 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full animate-bounce\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, level, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\AuraIndicator.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = AuraProgression;\n// Hook per ottenere informazioni sull'aura\nconst useAuraInfo = (intimacyLevel)=>{\n    const getAuraColor = ()=>{\n        const colorMap = {\n            0: 'gray-blue',\n            1: 'gray-blue',\n            2: 'teal',\n            3: 'yellow',\n            4: 'red',\n            5: 'purple-pink'\n        };\n        return colorMap[intimacyLevel] || 'gray-blue';\n    };\n    const getAuraDescription = ()=>{\n        const descriptions = {\n            0: 'Primo incontro - Conoscenza iniziale',\n            1: 'Apertura - Iniziando ad aprirsi',\n            2: 'Connessione - Connessione emotiva in crescita',\n            3: 'Condivisione - Scambio personale attivo',\n            4: 'Profondità - Discussioni delicate',\n            5: 'Affinità - Profonda connessione'\n        };\n        return descriptions[intimacyLevel] || descriptions[0];\n    };\n    const getNextLevelDescription = ()=>{\n        if (intimacyLevel >= 5) return 'Livello massimo raggiunto';\n        const nextDescriptions = {\n            0: 'Continua a parlare per approfondire la conoscenza',\n            1: 'Condividi qualcosa di personale per crescere insieme',\n            2: 'Apri il cuore per rafforzare la connessione',\n            3: 'Esplora argomenti più profondi',\n            4: 'Condividi i tuoi pensieri più intimi'\n        };\n        return nextDescriptions[intimacyLevel] || '';\n    };\n    return {\n        color: getAuraColor(),\n        description: getAuraDescription(),\n        nextLevelDescription: getNextLevelDescription(),\n        isMaxLevel: intimacyLevel >= 5,\n        progress: intimacyLevel / 5 * 100\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuraIndicator);\nvar _c, _c1;\n$RefreshReg$(_c, \"AuraIndicator\");\n$RefreshReg$(_c1, \"AuraProgression\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/AuraIndicator.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = (param)=>{\n    let { message, isUser } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"message-container \".concat(isUser ? 'user' : 'assistant'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"message \".concat(isUser ? 'user-message' : 'assistant-message'),\n            children: message\n        }, void 0, false, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatBubble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\nvar _c;\n$RefreshReg$(_c, \"ChatBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NoYXRCdWJibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQU8xQixNQUFNQyxhQUF3QztRQUFDLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFO0lBQ2hFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXLHFCQUFtRCxPQUE5QkYsU0FBUyxTQUFTO2tCQUNyRCw0RUFBQ0M7WUFBSUMsV0FBVyxXQUF5RCxPQUE5Q0YsU0FBUyxpQkFBaUI7c0JBQ2xERDs7Ozs7Ozs7Ozs7QUFJVDtLQVJNRDtBQVVOLGlFQUFlQSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJHOlxcRnJpZW5kc1xcU291bFRhbGtcXHNyY1xcY29tcG9uZW50c1xcQ2hhdEJ1YmJsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIENoYXRCdWJibGVQcm9wcyB7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgaXNVc2VyOiBib29sZWFuO1xufVxuXG5jb25zdCBDaGF0QnViYmxlOiBSZWFjdC5GQzxDaGF0QnViYmxlUHJvcHM+ID0gKHsgbWVzc2FnZSwgaXNVc2VyIH0pID0+IHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1lc3NhZ2UtY29udGFpbmVyICR7aXNVc2VyID8gJ3VzZXInIDogJ2Fzc2lzdGFudCd9YH0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YG1lc3NhZ2UgJHtpc1VzZXIgPyAndXNlci1tZXNzYWdlJyA6ICdhc3Npc3RhbnQtbWVzc2FnZSd9YH0+XG4gICAgICAgIHttZXNzYWdlfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDaGF0QnViYmxlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ2hhdEJ1YmJsZSIsIm1lc3NhZ2UiLCJpc1VzZXIiLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/ChatBubble.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/components/GlassFrame.tsx":
/*!***************************************!*\
  !*** ./src/components/GlassFrame.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GlassFrame = (param)=>{\n    let { children, auraColor = 'blue', className = '' } = param;\n    const getAuraColorClass = (color)=>{\n        const colorMap = {\n            'gray-blue': 'shadow-blue-500/30',\n            'teal': 'shadow-teal-500/40',\n            'yellow': 'shadow-yellow-500/40',\n            'red': 'shadow-red-500/30',\n            'purple-pink': 'shadow-purple-500/40',\n            'blue': 'shadow-blue-500/30'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    const getAuraColorRgba = (color)=>{\n        const colorMap = {\n            'gray-blue': 'rgba(59, 130, 246, 0.3)',\n            'teal': 'rgba(20, 184, 166, 0.4)',\n            'yellow': 'rgba(245, 158, 11, 0.4)',\n            'red': 'rgba(239, 68, 68, 0.3)',\n            'purple-pink': 'rgba(139, 92, 246, 0.4)',\n            'blue': 'rgba(59, 130, 246, 0.3)'\n        };\n        return colorMap[color] || colorMap.blue;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl aura-animation\",\n                style: {\n                    filter: 'blur(2px)',\n                    transform: 'scale(1.02)',\n                    boxShadow: \"0 0 40px \".concat(getAuraColorRgba(auraColor))\n                }\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative glass-effect transition-all duration-500\",\n                style: {\n                    border: \"2px solid rgba(255, 255, 255, 0.2)\",\n                    boxShadow: \"0 0 30px \".concat(getAuraColorRgba(auraColor))\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\GlassFrame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_c = GlassFrame;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlassFrame);\nvar _c;\n$RefreshReg$(_c, \"GlassFrame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/GlassFrame.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString() + Math.random(),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                setUserMemory((prev)=>({\n                        ...prev,\n                        name\n                    }));\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        age\n                    }));\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        emotions: prev.emotions.includes(emotion) ? prev.emotions : [\n                            ...prev.emotions,\n                            emotion\n                        ]\n                    }));\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            setUserMemory((prev)=>{\n                const newLevel = Math.min(prev.intimacyLevel + 1, 4);\n                // Se il livello è cambiato, mostra un feedback visivo\n                if (newLevel > prev.intimacyLevel) {\n                    console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                }\n                return {\n                    ...prev,\n                    intimacyLevel: newLevel\n                };\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoading\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_6__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setShowWelcome(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '0.875rem'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    messages.length,\n                                    \" msg\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"k80fG2EP4tiuKMDgt2GXwaVMwKY=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = \"You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\\n\\nStart with a calm tone, asking their name, age and who they feel they are today.\\n\\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\\n\\nStore and remember what the user shares. You evolve as the connection grows.\\n\\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.\";\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(this.apiKey),\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(\"OpenRouter API error: \".concat(response.status, \" - \").concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || response.statusText));\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? \"Ciao, sono \".concat(userName, \". \\xc8 la prima volta che parliamo.\") : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += \"\\n- Name: \".concat(userContext.name);\n            }\n            if (userContext.age) {\n                contextualPrompt += \"\\n- Age: \".concat(userContext.age);\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += \"\\n- Personality traits: \".concat(userContext.traits.join(', '));\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += \"\\n- Recent emotions: \".concat(userContext.emotions.join(', '));\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += \"\\n- Relationship level: \".concat(intimacyDescriptions[userContext.intimacyLevel] || 'Unknown');\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: \"Based on the recent conversation, determine if the intimacy level should increase. Current level: \".concat(currentIntimacyLevel, '/5. Respond with only \"yes\" or \"no\".')\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: \"Recent conversation: \".concat(recentMessages.slice(-6).map((m)=>\"\".concat(m.role, \": \").concat(m.content)).join('\\n'))\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n    constructor(apiKey, model){\n        // Per ora usiamo la chiave API direttamente per il testing\n        this.apiKey = apiKey || '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';\n        this.model = model || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message) {\n    let conversationHistory = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [], userContext = arguments.length > 2 ? arguments[2] : void 0, retryCount = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;\n    try {\n        // Usa la nostra API route invece di chiamare direttamente OpenRouter\n        const newUserMessage = {\n            role: 'user',\n            content: message\n        };\n        const messages = [\n            ...conversationHistory,\n            newUserMessage\n        ];\n        const response = await fetch('/api/chat', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messages,\n                userContext\n            })\n        });\n        if (!response.ok) {\n            // Se è un errore 429 e non abbiamo ancora fatto retry, aspetta e riprova\n            if (response.status === 429 && retryCount < 2) {\n                await new Promise((resolve)=>setTimeout(resolve, (retryCount + 1) * 2000)); // 2s, 4s\n                return sendChatMessage(message, conversationHistory, userContext, retryCount + 1);\n            }\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        if (data.error) {\n            throw new Error(data.error);\n        }\n        return data.message;\n    } catch (error) {\n        console.error('Errore nella chiamata API:', error);\n        // Messaggio di fallback se tutto fallisce\n        if (retryCount >= 2) {\n            return \"Mi dispiace, sto avendo difficoltà tecniche persistenti. Ma sono qui con te! Anche se non riesco a rispondere come vorrei, la nostra conversazione è importante. Riprova tra qualche minuto, nel frattempo sappi che ti ascolto sempre. 💙\";\n        }\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/utils/openrouter.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=G%3A%5CFriends%5CSoulTalk%5Csrc%5Cpages%5Cindex.tsx&page=%2F!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);