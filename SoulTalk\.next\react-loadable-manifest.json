{"..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__app_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__error_js.js"]}}