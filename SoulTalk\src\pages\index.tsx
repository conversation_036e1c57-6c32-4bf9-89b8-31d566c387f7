import React, { useState, useRef, useEffect } from 'react';
import Head from 'next/head';
import ChatBubble from '../components/ChatBubble';
import { sendChatMessage } from '../utils/openrouter';
import { useUserMemory } from '../state/userMemory';

export default function Home() {
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { memory, addMessage, resetMemory } = useUserMemory();

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTo({
            top: chatContainerRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [memory.conversationHistory, isLoading]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);

    // Aggiungi immediatamente il messaggio dell'utente
    addMessage(userMessage, true);

    // Scroll dopo aver aggiunto il messaggio utente
    setTimeout(scrollToBottom, 50);

    try {
      // Prepara la cronologia per l'API (incluso il nuovo messaggio utente)
      const conversationHistory = [
        ...memory.conversationHistory.map(msg => ({
          role: msg.isUser ? 'user' as const : 'assistant' as const,
          content: msg.content
        })),
        {
          role: 'user' as const,
          content: userMessage
        }
      ];

      const response = await sendChatMessage(
        userMessage,
        conversationHistory,
        {
          name: memory.name,
          intimacyLevel: memory.intimacyLevel,
          traits: memory.traits,
          emotions: memory.emotions
        }
      );

      // Aggiungi la risposta dell'AI
      addMessage(response, false);
    } catch (error) {
      console.error('Error:', error);
      addMessage("Scusa, ho avuto un problema tecnico. Riprova!", false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleReset = () => {
    if (confirm('Ricominciare da capo?')) {
      resetMemory();
    }
  };

  return (
    <>
      <Head>
        <title>Soul Chat</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="chat-container">
        {/* Header */}
        <div className="chat-header">
          <h1 style={{ color: 'white', fontSize: '1.5rem', fontWeight: 'bold', margin: 0 }}>
            Soul Chat
          </h1>
          <button onClick={handleReset} style={{ color: 'white', background: 'none', border: 'none', cursor: 'pointer' }}>
            Reset
          </button>
        </div>

        {/* Messages Area */}
        <div 
          ref={chatContainerRef}
          className="chat-messages"
        >
          {/* Welcome Message */}
          {memory.conversationHistory.length === 0 && (
            <div style={{ textAlign: 'center', padding: '2rem', color: 'white' }}>
              <h2>Ciao, sono Soul 👋</h2>
              <p>Dimmi, come ti chiami e come ti senti oggi?</p>
            </div>
          )}

          {/* Messages */}
          {memory.conversationHistory.map((message) => (
            <ChatBubble
              key={message.id}
              message={message.content}
              isUser={message.isUser}
            />
          ))}

          {/* Loading */}
          {isLoading && (
            <div style={{ padding: '1rem', color: 'white' }}>
              Soul sta scrivendo...
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="chat-input-area">
          <textarea
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Scrivi il tuo messaggio..."
            className="input-field"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="send-button"
          >
            →
          </button>
        </div>
      </div>
    </>
  );
}
