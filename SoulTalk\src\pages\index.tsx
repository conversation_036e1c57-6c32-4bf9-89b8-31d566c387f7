import React, { useState, useEffect, useRef } from 'react';
import Head from 'next/head';
import GlassFrame from '../components/GlassFrame';
import ChatBubble from '../components/ChatBubble';
import ChatInput from '../components/ChatInput';
import AuraIndicator, { AuraProgression } from '../components/AuraIndicator';
import { useUserMemory } from '../state/userMemory';
import { sendChatMessage, OpenRouterMessage } from '../utils/openrouter';

export default function Home() {
  const {
    memory,
    isLoaded,
    addMessage,
    updateBasicInfo,
    addTrait,
    addEmotion,
    updateIntimacyLevel,
    getAuraColor,
    resetMemory
  } = useUserMemory();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showWelcome, setShowWelcome] = useState(true);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Scroll automatico smooth verso il basso quando arrivano nuovi messaggi
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [memory.conversationHistory]);

  // Nasconde il messaggio di benvenuto dopo il primo messaggio
  useEffect(() => {
    if (memory.conversationHistory.length > 0) {
      setShowWelcome(false);
    }
  }, [memory.conversationHistory]);

  // Converte la cronologia della memoria in formato OpenRouter
  const getOpenRouterHistory = (): OpenRouterMessage[] => {
    return memory.conversationHistory.map(msg => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.content
    }));
  };

  // Gestisce l'invio di un nuovo messaggio
  const handleSendMessage = async (message: string) => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      // Aggiunge il messaggio dell'utente alla cronologia
      addMessage(message, true);

      // Prepara il contesto utente per l'AI
      const userContext = {
        name: memory.name || undefined,
        age: memory.age || undefined,
        traits: memory.traits,
        emotions: memory.emotions,
        intimacyLevel: memory.intimacyLevel
      };

      // Invia il messaggio all'AI
      const aiResponse = await sendChatMessage(
        message,
        getOpenRouterHistory(),
        userContext
      );

      // Aggiunge la risposta dell'AI alla cronologia
      addMessage(aiResponse, false);

      // Analizza il messaggio per estrarre informazioni (simulato per ora)
      await analyzeAndUpdateMemory(message, aiResponse);

    } catch (err) {
      console.error('Errore nell\'invio del messaggio:', err);

      // Messaggio di fallback più empatico
      const fallbackResponse = "Scusa, sto avendo qualche difficoltà tecnica in questo momento, ma sono qui con te! 💙 Anche se non riesco a rispondere come vorrei, la nostra conversazione è importante per me. Riprova tra qualche secondo - nel frattempo, continua pure a scrivermi quello che hai in mente.";

      // Aggiungi comunque una risposta di fallback alla conversazione
      addMessage(fallbackResponse, false);
      setError(null); // Non mostrare errore, ma la risposta di fallback
    } finally {
      setIsLoading(false);
    }
  };

  // Analizza i messaggi e aggiorna la memoria (versione semplificata)
  const analyzeAndUpdateMemory = async (userMessage: string, aiResponse: string) => {
    // Analisi semplificata basata su parole chiave
    const lowerUserMessage = userMessage.toLowerCase();
    
    // Estrae informazioni base
    if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {
      const nameMatch = userMessage.match(/mi chiamo (\w+)|sono (\w+)/i);
      if (nameMatch && !memory.name) {
        const name = nameMatch[1] || nameMatch[2];
        updateBasicInfo({ name });
      }
    }

    // Estrae età
    const ageMatch = userMessage.match(/ho (\d+) anni|(\d+) anni/);
    if (ageMatch && !memory.age) {
      const age = parseInt(ageMatch[1] || ageMatch[2]);
      if (age > 0 && age < 120) {
        updateBasicInfo({ age });
      }
    }

    // Rileva emozioni
    const emotions = {
      'felice': ['felice', 'contento', 'gioioso', 'allegro'],
      'triste': ['triste', 'depresso', 'giù', 'male'],
      'arrabbiato': ['arrabbiato', 'furioso', 'incazzato'],
      'preoccupato': ['preoccupato', 'ansioso', 'nervoso'],
      'eccitato': ['eccitato', 'entusiasta', 'carico'],
      'calmo': ['calmo', 'tranquillo', 'sereno']
    };

    for (const [emotion, keywords] of Object.entries(emotions)) {
      if (keywords.some(keyword => lowerUserMessage.includes(keyword))) {
        addEmotion(emotion);
        break;
      }
    }

    // Aggiorna il livello di intimità basato sulla lunghezza e contenuto
    if (userMessage.length > 100 || 
        lowerUserMessage.includes('personale') ||
        lowerUserMessage.includes('segreto') ||
        lowerUserMessage.includes('famiglia') ||
        lowerUserMessage.includes('amore')) {
      
      const currentLevel = memory.intimacyLevel;
      if (currentLevel < 5) {
        updateIntimacyLevel(currentLevel + 1);
      }
    }
  };

  // Gestisce il reset della memoria
  const handleReset = () => {
    if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {
      resetMemory();
      setShowWelcome(true);
      setError(null);
    }
  };

  // Mostra loading durante l'idratazione
  if (!isLoaded) {
    return (
      <>
        <Head>
          <title>SoulTalk - La tua amicizia AI</title>
          <meta name="description" content="Una piattaforma AI che si comporta come un'amicizia da costruire" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <main className="min-h-screen flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p>Caricamento SoulTalk...</p>
          </div>
        </main>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>SoulTalk - La tua amicizia AI</title>
        <meta name="description" content="Una piattaforma AI che si comporta come un'amicizia da costruire" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="chat-layout">

        {/* Header compatto con solo indicatori aura */}
        <div className="chat-header text-center">
          {/* Indicatore dell'aura e livello di intimità */}
          <div className="flex flex-col items-center space-y-3">
            <AuraIndicator
              intimacyLevel={memory.intimacyLevel}
              showLabel={false}
              className="justify-center"
            />
            <AuraProgression intimacyLevel={memory.intimacyLevel} />
          </div>
        </div>

        {/* Container principale della chat con aura */}
        <div className="chat-main-container flex-1 mx-4">
          <GlassFrame auraColor={getAuraColor()} className="glass-frame-container">

            {/* Area messaggi scrollabile */}
            <div
              ref={chatContainerRef}
              className="chat-messages flex-1 space-y-4"
            >
              {/* Messaggio di benvenuto */}
              {showWelcome && (
                <div className="text-center" style={{ padding: '1rem 0' }}>
                  <div className="glass-effect rounded-2xl mx-auto" style={{ maxWidth: '20rem', padding: '1rem' }}>
                    <h2 className="text-xl font-semibold text-white mb-2">
                      Ciao, sono Soul 👋
                    </h2>
                    <p className="text-xs opacity-80" style={{ lineHeight: '1.4' }}>
                      È la prima volta che ci parliamo. Sono qui per conoscerti davvero,
                      non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?
                    </p>
                  </div>
                </div>
              )}

              {/* Cronologia messaggi */}
              {memory.conversationHistory.map((message) => (
                <ChatBubble
                  key={message.id}
                  message={message.content}
                  isUser={message.isUser}
                  timestamp={message.timestamp}
                />
              ))}

              {/* Pulsante di test per aggiungere messaggi (temporaneo) */}
              {memory.conversationHistory.length > 0 && (
                <div className="text-center">
                  <button
                    onClick={() => {
                      for (let i = 0; i < 5; i++) {
                        addMessage(`Messaggio di test ${Date.now() + i}. Questo è un messaggio lungo per testare lo scrolling della chat. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`, i % 2 === 0);
                      }
                    }}
                    className="btn text-xs opacity-50"
                  >
                    Aggiungi messaggi test
                  </button>
                </div>
              )}

              {/* Indicatore di caricamento */}
              {isLoading && (
                <div className="flex">
                  <div className="glass-effect rounded-xl" style={{ padding: '0.75rem' }}>
                    <div className="flex space-x-2">
                      <div className="animate-bounce opacity-60" style={{
                        width: '6px',
                        height: '6px',
                        backgroundColor: 'white',
                        borderRadius: '50%'
                      }} />
                      <div className="animate-bounce opacity-60" style={{
                        width: '6px',
                        height: '6px',
                        backgroundColor: 'white',
                        borderRadius: '50%',
                        animationDelay: '0.1s'
                      }} />
                      <div className="animate-bounce opacity-60" style={{
                        width: '6px',
                        height: '6px',
                        backgroundColor: 'white',
                        borderRadius: '50%',
                        animationDelay: '0.2s'
                      }} />
                    </div>
                  </div>
                </div>
              )}

              {/* Messaggio di errore */}
              {error && (
                <div className="text-center">
                  <div className="glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30">
                    <p className="text-red-200 text-sm">{error}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Area input fissa */}
            <div className="chat-input-area">
              <ChatInput
                onSendMessage={handleSendMessage}
                disabled={isLoading}
                placeholder={
                  memory.name
                    ? `Cosa vuoi condividere con Soul, ${memory.name}?`
                    : "Dimmi qualcosa di te..."
                }
              />
            </div>
          </GlassFrame>
        </div>

        {/* Footer fisso con controlli */}
        <div className="flex justify-between items-center text-xs opacity-60" style={{ padding: '0.5rem' }}>
          <div>
            {memory.name && (
              <span>Ciao {memory.name}! Livello: {memory.intimacyLevel}/5</span>
            )}
          </div>
          <button
            onClick={handleReset}
            className="transition-all duration-200 text-xs"
            style={{ cursor: 'pointer' }}
            onMouseOver={(e) => e.target.style.opacity = '1'}
            onMouseOut={(e) => e.target.style.opacity = '0.6'}
          >
            Reset
          </button>
        </div>
      </main>
    </>
  );
}
