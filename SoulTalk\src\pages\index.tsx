import React, { useState, useEffect, useRef } from 'react';
import Head from 'next/head';
import GlassFrame from '../components/GlassFrame';
import ChatBubble from '../components/ChatBubble';
import ChatInput from '../components/ChatInput';
import AuraIndicator, { AuraProgression } from '../components/AuraIndicator';
import { useUserMemory } from '../state/userMemory';
import { sendChatMessage, OpenRouterMessage } from '../utils/openrouter';

export default function Home() {
  const {
    memory,
    addMessage,
    updateBasicInfo,
    addTrait,
    addEmotion,
    updateIntimacyLevel,
    getAuraColor,
    resetMemory
  } = useUserMemory();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showWelcome, setShowWelcome] = useState(true);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Scroll automatico verso il basso quando arrivano nuovi messaggi
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [memory.conversationHistory]);

  // Nasconde il messaggio di benvenuto dopo il primo messaggio
  useEffect(() => {
    if (memory.conversationHistory.length > 0) {
      setShowWelcome(false);
    }
  }, [memory.conversationHistory]);

  // Converte la cronologia della memoria in formato OpenRouter
  const getOpenRouterHistory = (): OpenRouterMessage[] => {
    return memory.conversationHistory.map(msg => ({
      role: msg.isUser ? 'user' : 'assistant',
      content: msg.content
    }));
  };

  // Gestisce l'invio di un nuovo messaggio
  const handleSendMessage = async (message: string) => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      // Aggiunge il messaggio dell'utente alla cronologia
      addMessage(message, true);

      // Prepara il contesto utente per l'AI
      const userContext = {
        name: memory.name || undefined,
        age: memory.age || undefined,
        traits: memory.traits,
        emotions: memory.emotions,
        intimacyLevel: memory.intimacyLevel
      };

      // Invia il messaggio all'AI
      const aiResponse = await sendChatMessage(
        message,
        getOpenRouterHistory(),
        userContext
      );

      // Aggiunge la risposta dell'AI alla cronologia
      addMessage(aiResponse, false);

      // Analizza il messaggio per estrarre informazioni (simulato per ora)
      await analyzeAndUpdateMemory(message, aiResponse);

    } catch (err) {
      console.error('Errore nell\'invio del messaggio:', err);
      setError('Errore nella comunicazione. Riprova tra poco.');
    } finally {
      setIsLoading(false);
    }
  };

  // Analizza i messaggi e aggiorna la memoria (versione semplificata)
  const analyzeAndUpdateMemory = async (userMessage: string, aiResponse: string) => {
    // Analisi semplificata basata su parole chiave
    const lowerUserMessage = userMessage.toLowerCase();
    
    // Estrae informazioni base
    if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {
      const nameMatch = userMessage.match(/mi chiamo (\w+)|sono (\w+)/i);
      if (nameMatch && !memory.name) {
        const name = nameMatch[1] || nameMatch[2];
        updateBasicInfo({ name });
      }
    }

    // Estrae età
    const ageMatch = userMessage.match(/ho (\d+) anni|(\d+) anni/);
    if (ageMatch && !memory.age) {
      const age = parseInt(ageMatch[1] || ageMatch[2]);
      if (age > 0 && age < 120) {
        updateBasicInfo({ age });
      }
    }

    // Rileva emozioni
    const emotions = {
      'felice': ['felice', 'contento', 'gioioso', 'allegro'],
      'triste': ['triste', 'depresso', 'giù', 'male'],
      'arrabbiato': ['arrabbiato', 'furioso', 'incazzato'],
      'preoccupato': ['preoccupato', 'ansioso', 'nervoso'],
      'eccitato': ['eccitato', 'entusiasta', 'carico'],
      'calmo': ['calmo', 'tranquillo', 'sereno']
    };

    for (const [emotion, keywords] of Object.entries(emotions)) {
      if (keywords.some(keyword => lowerUserMessage.includes(keyword))) {
        addEmotion(emotion);
        break;
      }
    }

    // Aggiorna il livello di intimità basato sulla lunghezza e contenuto
    if (userMessage.length > 100 || 
        lowerUserMessage.includes('personale') ||
        lowerUserMessage.includes('segreto') ||
        lowerUserMessage.includes('famiglia') ||
        lowerUserMessage.includes('amore')) {
      
      const currentLevel = memory.intimacyLevel;
      if (currentLevel < 5) {
        updateIntimacyLevel(currentLevel + 1);
      }
    }
  };

  // Gestisce il reset della memoria
  const handleReset = () => {
    if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {
      resetMemory();
      setShowWelcome(true);
      setError(null);
    }
  };

  return (
    <>
      <Head>
        <title>SoulTalk - La tua amicizia AI</title>
        <meta name="description" content="Una piattaforma AI che si comporta come un'amicizia da costruire" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen p-4 flex flex-col items-center justify-center">
        <div className="w-full max-w-4xl mx-auto">
          
          {/* Header con indicatore aura */}
          <div className="mb-6 text-center">
            <h1 className="text-3xl font-bold text-white mb-2">SoulTalk</h1>
            <p className="text-white/60 mb-4">La tua amicizia AI che cresce con te</p>
            
            {/* Indicatore dell'aura e livello di intimità */}
            <div className="flex flex-col items-center space-y-3">
              <AuraIndicator 
                intimacyLevel={memory.intimacyLevel} 
                showLabel={true}
                className="justify-center"
              />
              <AuraProgression intimacyLevel={memory.intimacyLevel} />
            </div>
          </div>

          {/* Container principale della chat */}
          <GlassFrame auraColor={getAuraColor()} className="h-[600px] flex flex-col">
            
            {/* Area messaggi */}
            <div 
              ref={chatContainerRef}
              className="flex-1 overflow-y-auto p-6 space-y-4"
            >
              {/* Messaggio di benvenuto */}
              {showWelcome && (
                <div className="text-center py-8">
                  <div className="glass-effect p-6 rounded-2xl max-w-md mx-auto">
                    <h2 className="text-xl font-semibold text-white mb-3">
                      Ciao, sono Soul 👋
                    </h2>
                    <p className="text-white/80 text-sm leading-relaxed">
                      È la prima volta che ci parliamo. Sono qui per conoscerti davvero, 
                      non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?
                    </p>
                  </div>
                </div>
              )}

              {/* Cronologia messaggi */}
              {memory.conversationHistory.map((message) => (
                <ChatBubble
                  key={message.id}
                  message={message.content}
                  isUser={message.isUser}
                  timestamp={message.timestamp}
                />
              ))}

              {/* Indicatore di caricamento */}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="glass-effect p-4 rounded-2xl">
                    <div className="flex space-x-2">
                      <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce" 
                           style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce" 
                           style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                </div>
              )}

              {/* Messaggio di errore */}
              {error && (
                <div className="text-center">
                  <div className="glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30">
                    <p className="text-red-200 text-sm">{error}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Area input */}
            <div className="p-6 border-t border-white/10">
              <ChatInput
                onSendMessage={handleSendMessage}
                disabled={isLoading}
                placeholder={
                  memory.name 
                    ? `Cosa vuoi condividere con Soul, ${memory.name}?`
                    : "Dimmi qualcosa di te..."
                }
              />
            </div>
          </GlassFrame>

          {/* Footer con controlli */}
          <div className="mt-4 flex justify-between items-center text-sm text-white/60">
            <div>
              {memory.name && (
                <span>Ciao {memory.name}! Livello di connessione: {memory.intimacyLevel}/5</span>
              )}
            </div>
            <button
              onClick={handleReset}
              className="hover:text-white transition-colors duration-200"
            >
              Ricomincia da capo
            </button>
          </div>
        </div>
      </main>
    </>
  );
}
