import React, { useState, useRef, useEffect } from 'react';
import Head from 'next/head';
import ChatBubble from '../components/ChatBubble';
import GlassFrame from '../components/GlassFrame';
import AuraIndicator, { AuraProgression } from '../components/AuraIndicator';
import AuthForm from '../components/AuthForm';
import OnboardingChat from '../components/OnboardingChat';
import ProfileCard from '../components/ProfileCard';
import HomePage from '../components/HomePage';
import { sendChatMessage } from '../utils/openrouter';
import { useSupabaseMemory } from '../hooks/useSupabaseMemory';

export default function Home() {
  const [inputMessage, setInputMessage] = useState('');
  const [isLoadingAI, setIsLoadingAI] = useState(false);
  const [showWelcome, setShowWelcome] = useState(true);
  const [showProfile, setShowProfile] = useState(false);
  const [showHomePage, setShowHomePage] = useState(true);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Usa il hook Supabase per gestire i dati
  const {
    userId,
    messages,
    userMemory,
    isLoading: isLoadingData,
    isSaving,
    isAuthenticated,
    needsOnboarding,
    addMessage,
    updateUserMemory,
    resetMemory,
    authenticateUser,
    logout,
    completeOnboardingWithData
  } = useSupabaseMemory();

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTo({
            top: chatContainerRef.current.scrollHeight,
            behavior: 'smooth'
          });
        }
      }, 100);
    }
  };

  // Funzione per determinare il colore dell'aura basato sul livello di intimità
  const getAuraColor = () => {
    const colors = [
      '#6366f1', // Indigo - Sconosciuto
      '#8b5cf6', // Violet - Conoscente
      '#a855f7', // Purple - Amico
      '#ec4899', // Pink - Amico stretto
      '#f43f5e'  // Rose - Intimo
    ];
    return colors[userMemory.intimacyLevel] || colors[0];
  };

  // Funzione per il gradiente animato del bordo input
  const getConnectionGradient = () => {
    const level = userMemory.intimacyLevel;
    const gradients = [
      'linear-gradient(45deg, #6366f1, #8b5cf6, #6366f1, #8b5cf6)', // Livello 0
      'linear-gradient(45deg, #8b5cf6, #a855f7, #8b5cf6, #a855f7)', // Livello 1
      'linear-gradient(45deg, #a855f7, #ec4899, #a855f7, #ec4899)', // Livello 2
      'linear-gradient(45deg, #ec4899, #f43f5e, #ec4899, #f43f5e)', // Livello 3
      'linear-gradient(45deg, #f43f5e, #ff6b9d, #f43f5e, #ff6b9d)'  // Livello 4
    ];
    return gradients[level] || gradients[0];
  };

  // Funzione per generare lo sfondo dinamico basato sul livello di intimità
  const getDynamicBackground = () => {
    const intimacyLevel = userMemory.intimacyLevel;

    const backgroundConfigs = [
      // Livello 0 - Sconosciuto: Blu freddi e neutri
      {
        colors: ['rgba(99, 102, 241, 0.25)', 'rgba(139, 92, 246, 0.2)', 'rgba(168, 85, 247, 0.15)'],
        baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
      },
      // Livello 1 - Conoscente: Viola più caldi
      {
        colors: ['rgba(139, 92, 246, 0.3)', 'rgba(168, 85, 247, 0.25)', 'rgba(236, 72, 153, 0.2)'],
        baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'
      },
      // Livello 2 - Amico: Mix viola-rosa
      {
        colors: ['rgba(168, 85, 247, 0.35)', 'rgba(236, 72, 153, 0.3)', 'rgba(244, 63, 94, 0.25)'],
        baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'
      },
      // Livello 3 - Amico stretto: Rosa intensi
      {
        colors: ['rgba(236, 72, 153, 0.4)', 'rgba(244, 63, 94, 0.35)', 'rgba(251, 113, 133, 0.3)'],
        baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'
      },
      // Livello 4 - Intimo: Rosa caldi e dorati
      {
        colors: ['rgba(244, 63, 94, 0.45)', 'rgba(251, 113, 133, 0.4)', 'rgba(252, 165, 165, 0.35)'],
        baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'
      }
    ];

    const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];

    return {
      background: `
        radial-gradient(circle at 20% 80%, ${config.colors[0]} 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${config.colors[1]} 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, ${config.colors[2]} 0%, transparent 50%),
        ${config.baseGradient}
      `,
      backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',
      backgroundAttachment: 'fixed',
      animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',
      transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'
    };
  };

  // Analizza i messaggi e aggiorna la memoria utente
  const analyzeAndUpdateMemory = async (userMessage: string, aiResponse: string) => {
    const lowerUserMessage = userMessage.toLowerCase();
    const updates: any = {};

    // Estrae informazioni base
    if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {
      const nameMatch = userMessage.match(/mi chiamo (\w+)|sono (\w+)/i);
      if (nameMatch && !userMemory.name) {
        const name = nameMatch[1] || nameMatch[2];
        updates.name = name;
      }
    }

    // Estrae età
    const ageMatch = userMessage.match(/ho (\d+) anni|(\d+) anni/);
    if (ageMatch && !userMemory.age) {
      const age = parseInt(ageMatch[1] || ageMatch[2]);
      if (age > 0 && age < 120) {
        updates.age = age;
      }
    }

    // Rileva emozioni
    const emotions = {
      'felice': ['felice', 'contento', 'gioioso', 'allegro'],
      'triste': ['triste', 'depresso', 'giù', 'male'],
      'arrabbiato': ['arrabbiato', 'furioso', 'incazzato'],
      'preoccupato': ['preoccupato', 'ansioso', 'nervoso'],
      'eccitato': ['eccitato', 'entusiasta', 'carico'],
      'calmo': ['calmo', 'tranquillo', 'sereno']
    };

    for (const [emotion, keywords] of Object.entries(emotions)) {
      if (keywords.some(keyword => lowerUserMessage.includes(keyword))) {
        if (!userMemory.emotions.includes(emotion)) {
          updates.emotions = [...userMemory.emotions, emotion];
        }
        break;
      }
    }

    // Aggiorna il livello di intimità
    if (userMessage.length > 100 ||
        lowerUserMessage.includes('personale') ||
        lowerUserMessage.includes('segreto') ||
        lowerUserMessage.includes('famiglia') ||
        lowerUserMessage.includes('amore') ||
        lowerUserMessage.includes('problema') ||
        lowerUserMessage.includes('difficoltà') ||
        lowerUserMessage.includes('paura') ||
        lowerUserMessage.includes('sogno')) {

      const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);

      // Se il livello è cambiato, mostra un feedback visivo
      if (newLevel > userMemory.intimacyLevel) {
        console.log(`🌟 Livello di connessione aumentato: ${newLevel}/4`);
        updates.intimacyLevel = newLevel;
      }
    }

    // Applica tutti gli aggiornamenti
    if (Object.keys(updates).length > 0) {
      updateUserMemory(updates);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoadingAI]);

  // Nascondi il messaggio di benvenuto dopo il primo messaggio
  useEffect(() => {
    if (messages.length > 0) {
      setShowWelcome(false);
    }
  }, [messages]);

  // Aggiorna lo sfondo dinamicamente in base al livello di intimità
  useEffect(() => {
    const backgroundStyle = getDynamicBackground();
    const body = document.body;

    // Applica le proprietà di sfondo con transizione fluida
    Object.assign(body.style, backgroundStyle);

    // Aggiungi una classe per indicare il livello corrente
    body.className = `intimacy-level-${userMemory.intimacyLevel}`;

    // Cleanup function per ripristinare lo sfondo originale se necessario
    return () => {
      // Non facciamo cleanup per mantenere l'effetto
    };
  }, [userMemory.intimacyLevel]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoadingAI) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    setIsLoadingAI(true);

    // Aggiungi immediatamente il messaggio dell'utente
    const userMsg = await addMessage(userMessage, true);
    console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);

    // Scroll dopo aver aggiunto il messaggio utente
    setTimeout(scrollToBottom, 50);

    try {
      // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)
      const conversationHistory = [...messages, userMsg].map(msg => ({
        role: msg.isUser ? 'user' as const : 'assistant' as const,
        content: msg.content
      }));

      const response = await sendChatMessage(
        userMessage,
        conversationHistory,
        {
          name: userMemory.name || undefined,
          age: userMemory.age || undefined,
          traits: userMemory.traits,
          emotions: userMemory.emotions,
          intimacyLevel: userMemory.intimacyLevel
        }
      );

      // Aggiungi la risposta dell'AI
      await addMessage(response, false);
      console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);

      // Analizza il messaggio per aggiornare la memoria
      await analyzeAndUpdateMemory(userMessage, response);

    } catch (error) {
      console.error('Error:', error);
      await addMessage("Scusa, ho avuto un problema tecnico. Riprova!", false);
    } finally {
      setIsLoadingAI(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleReset = async () => {
    if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {
      await resetMemory();
      setShowWelcome(true);
    }
  };

  // Se mostra la HomePage, mostrala prima del login
  if (showHomePage) {
    return (
      <>
        <Head>
          <title>SoulTalk - By Agtechdesigne</title>
          <meta name="description" content="SoulTalk - L' AI che ti conosce davvero. Connessione autentica, memoria emotiva, crescita insieme." />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <HomePage onGetStarted={() => setShowHomePage(false)} />
      </>
    )
  }

  // Se non è autenticato, mostra il form di login
  if (!isAuthenticated) {
    return (
      <>
        <Head>
          <title>SoulTalk - Accedi</title>
          <meta name="description" content="Accedi a SoulTalk per continuare la tua conversazione con Soul" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <AuthForm onAuthSuccess={authenticateUser} />
      </>
    )
  }

  // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione
  if (isLoadingData) {
    return (
      <>
        <Head>
          <title>SoulTalk - Caricamento...</title>
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.9)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{ color: 'white', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>🌟</div>
            <div>Caricando i tuoi dati...</div>
          </div>
        </div>
      </>
    )
  }

  // Se ha bisogno di onboarding, mostra la chat di configurazione
  if (needsOnboarding) {
    return (
      <>
        <Head>
          <title>SoulTalk - Configurazione</title>
          <meta name="description" content="Configura il tuo profilo per iniziare con Soul" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <OnboardingChat
          userId={userId}
          onComplete={completeOnboardingWithData}
        />
      </>
    )
  }

  return (
    <>
      <Head>
        <title>SoulTalk - La tua amicizia AI</title>
        <meta name="description" content="Una piattaforma AI che si comporta come un'amicizia da costruire" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="chat-container">
        {/* Loading iniziale */}
        {(isLoadingData || !userId) && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0,0,0,0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{ color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>🌟</div>
              <div>{!userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'}</div>
            </div>
          </div>
        )}

        {/* Profile Button */}
        <button
          onClick={() => setShowProfile(true)}
          style={{
            position: 'absolute',
            top: '0.4rem',
            right: '1rem',
            zIndex: 10,
            width: '45px',
            height: '45px',
            borderRadius: '50%',
            background: 'rgba(255,255,255,0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            color: 'white',
            fontSize: '1.1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(255,255,255,0.2)';
            e.currentTarget.style.transform = 'scale(1.05)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
            e.currentTarget.style.transform = 'scale(1)';
          }}
        >
          {userMemory.name ? userMemory.name.charAt(0).toUpperCase() : '👤'}
        </button>

        {/* Header con indicatori aura */}
        <div className="chat-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div
                style={{
                  width: '38px',
                  height: '38px',
                  borderRadius: '50%',
                  background: `radial-gradient(circle, ${getAuraColor()} 0%, transparent 70%)`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.3rem',
                  animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'
                }}
              >
                🌟
              </div>
              <div>
                <h1 style={{ color: 'white', fontSize: '1.3rem', fontWeight: 'bold', margin: 0 }}>
                  Soul
                </h1>
                <p style={{ color: 'rgba(255,255,255,0.7)', fontSize: '0.65rem', margin: 0 }}>
                  BY Agtechdesigne. 
                </p>
              </div>
            </div>
            <AuraProgression intimacyLevel={userMemory.intimacyLevel} />
          </div>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button onClick={logout} style={{ color: 'rgba(255,255,255,0.8)', background: 'none', border: 'none', cursor: 'pointer', fontSize: '0.875rem' }}>
              Logout
            </button>
            <button onClick={handleReset} style={{ color: 'white', background: 'none', border: 'none', cursor: 'pointer', fontSize: '0.875rem' }}>
              Reset
            </button>
          </div>
        </div>

        {/* Messages Area con GlassFrame */}
        <div style={{ flex: 1, padding: '0 1rem' }}>
          <GlassFrame auraColor={getAuraColor()} className="h-full flex flex-col">
            <div
              ref={chatContainerRef}
              className="chat-messages"
              style={{ flex: 1, overflowY: 'auto', padding: '1rem' }}
            >
              {/* Messaggio di benvenuto avanzato */}
              {showWelcome && (
                <div style={{ textAlign: 'center', padding: '1.5rem 0' }}>
                  <div className="glass-effect" style={{
                    maxWidth: '22rem',
                    margin: '0 auto',
                    padding: '1.25rem',
                    borderRadius: '0.875rem',
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}>
                    <h2 style={{
                      color: 'white',
                      fontSize: '1.125rem',
                      fontWeight: '600',
                      marginBottom: '0.625rem',
                      margin: 0
                    }}>
                      Ciao, sono Soul 👋
                    </h2>
                    <p style={{
                      color: 'rgba(255,255,255,0.8)',
                      fontSize: '0.75rem',
                      lineHeight: '1.4',
                      margin: 0
                    }}>
                      È la prima volta che ci parliamo. Sono qui per conoscerti davvero,
                      non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?
                    </p>
                  </div>
                </div>
              )}

              {/* Messages */}
              {messages.map((message) => (
                <ChatBubble
                  key={message.id}
                  message={message.content}
                  isUser={message.isUser}
                />
              ))}

              {/* Indicatore di caricamento avanzato */}
              {isLoadingAI && (
                <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                  <div className="glass-effect" style={{
                    padding: '0.625rem 0.875rem',
                    borderRadius: '0.875rem',
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <div style={{ display: 'flex', gap: '0.375rem', alignItems: 'center' }}>
                      <div style={{ display: 'flex', gap: '0.2rem' }}>
                        <div style={{
                          width: '5px',
                          height: '5px',
                          backgroundColor: 'white',
                          borderRadius: '50%',
                          animation: 'bounce 1s infinite',
                          opacity: 0.6
                        }} />
                        <div style={{
                          width: '5px',
                          height: '5px',
                          backgroundColor: 'white',
                          borderRadius: '50%',
                          animation: 'bounce 1s infinite',
                          animationDelay: '0.1s',
                          opacity: 0.6
                        }} />
                        <div style={{
                          width: '5px',
                          height: '5px',
                          backgroundColor: 'white',
                          borderRadius: '50%',
                          animation: 'bounce 1s infinite',
                          animationDelay: '0.2s',
                          opacity: 0.6
                        }} />
                      </div>
                      <span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '0.75rem' }}>
                        Soul sta scrivendo...
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </GlassFrame>
        </div>

        {/* Input Area */}
        <div
          className="chat-input-area"
          style={{
            position: 'relative',
            background: getConnectionGradient(),
            backgroundSize: '300% 300%',
            animation: 'gradientWave 3s ease-in-out infinite',
            padding: '2px',
            borderRadius: '1.5rem'
          }}
        >
          <div style={{
            background: 'rgba(0,0,0,0.9)',
            borderRadius: '1.4rem',
            padding: '1rem',
            display: 'flex',
            alignItems: 'flex-end',
            gap: '0.75rem'
          }}>
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                userMemory.name
                  ? `Cosa vuoi condividere con Soul, ${userMemory.name}?`
                  : "Dimmi qualcosa di te..."
              }
              className="input-field"
              disabled={isLoadingAI}
              style={{
                background: 'transparent',
                border: 'none',
                outline: 'none'
              }}
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoadingAI}
              className="send-button"
            >
              {isLoadingAI ? '⏳' : '→'}
            </button>
          </div>
        </div>


      </div>

      {/* Profile Card */}
      <ProfileCard
        isOpen={showProfile}
        onClose={() => setShowProfile(false)}
        userName={userMemory.name || 'Utente'}
        intimacyLevel={userMemory.intimacyLevel}
        messageCount={messages.length}
        userId={userId}
        onLogout={logout}
        onReset={handleReset}
      />
    </>
  );
}
