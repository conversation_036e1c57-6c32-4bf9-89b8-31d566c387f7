"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString() + Math.random(),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                setUserMemory((prev)=>({\n                        ...prev,\n                        name\n                    }));\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        age\n                    }));\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                setUserMemory((prev)=>({\n                        ...prev,\n                        emotions: prev.emotions.includes(emotion) ? prev.emotions : [\n                            ...prev.emotions,\n                            emotion\n                        ]\n                    }));\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            setUserMemory((prev)=>({\n                    ...prev,\n                    intimacyLevel: Math.min(prev.intimacyLevel + 1, 4)\n                }));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoading\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_6__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setShowWelcome(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '0.875rem'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.5rem 1rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello di connessione: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• Emozioni: \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.75rem'\n                                },\n                                children: [\n                                    messages.length,\n                                    \" messaggi\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"cgrLIBRXCj1AA7cqIMfvoMc77lM=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});