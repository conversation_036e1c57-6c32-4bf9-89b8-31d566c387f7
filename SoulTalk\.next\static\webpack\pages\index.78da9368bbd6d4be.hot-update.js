"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_7__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_6__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"La tua compagna AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer',\n                                    fontSize: '0.875rem'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this),\n                    userMemory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '0.375rem 0.875rem',\n                            borderTop: '1px solid rgba(255,255,255,0.1)',\n                            background: 'rgba(0,0,0,0.2)',\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.7)',\n                                    fontSize: '0.65rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Ciao \",\n                                            userMemory.name,\n                                            \"! \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Livello: \",\n                                            userMemory.intimacyLevel,\n                                            \"/4 \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    userMemory.emotions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"• \",\n                                            userMemory.emotions.slice(-2).join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: 'rgba(255,255,255,0.5)',\n                                    fontSize: '0.65rem',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            messages.length,\n                                            \" msg\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCBE\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"ID: \",\n                                            userId.slice(-6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"1Ufb3RIuGn3DPiXmxRwWzC8X8Z4=\", false, function() {\n    return [\n        _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_7__.useSupabaseMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3NyYy9wYWdlcy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMkQ7QUFDOUI7QUFDcUI7QUFDQTtBQUMyQjtBQUd2QjtBQUNTO0FBRWhELFNBQVNVOztJQUN0QixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHWCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNZLGFBQWFDLGVBQWUsR0FBR2IsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1nQixXQUFXZiw2Q0FBTUEsQ0FBc0I7SUFDN0MsTUFBTWdCLG1CQUFtQmhCLDZDQUFNQSxDQUFpQjtJQUVoRCwwQ0FBMEM7SUFDMUMsTUFBTSxFQUNKaUIsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLFVBQVUsRUFDVkMsV0FBV0MsYUFBYSxFQUN4QkMsUUFBUSxFQUNSQyxlQUFlLEVBQ2ZDLGVBQWUsRUFDZkMsVUFBVSxFQUNWQyxnQkFBZ0IsRUFDaEJDLFdBQVcsRUFDWEMsZ0JBQWdCLEVBQ2hCQyxNQUFNLEVBQ05DLDBCQUEwQixFQUMzQixHQUFHdkIsMkVBQWlCQTtJQUVyQixNQUFNd0IsaUJBQWlCO1FBQ3JCLElBQUlmLGlCQUFpQmdCLE9BQU8sRUFBRTtZQUM1QkMsV0FBVztnQkFDVCxJQUFJakIsaUJBQWlCZ0IsT0FBTyxFQUFFO29CQUM1QmhCLGlCQUFpQmdCLE9BQU8sQ0FBQ0UsUUFBUSxDQUFDO3dCQUNoQ0MsS0FBS25CLGlCQUFpQmdCLE9BQU8sQ0FBQ0ksWUFBWTt3QkFDMUNDLFVBQVU7b0JBQ1o7Z0JBQ0Y7WUFDRixHQUFHO1FBQ0w7SUFDRjtJQUVBLDhFQUE4RTtJQUM5RSxNQUFNQyxlQUFlO1FBQ25CLE1BQU1DLFNBQVM7WUFDYjtZQUNBO1lBQ0E7WUFDQTtZQUNBLFVBQVcsZ0JBQWdCO1NBQzVCO1FBQ0QsT0FBT0EsTUFBTSxDQUFDcEIsV0FBV3FCLGFBQWEsQ0FBQyxJQUFJRCxNQUFNLENBQUMsRUFBRTtJQUN0RDtJQUVBLDBFQUEwRTtJQUMxRSxNQUFNRSx1QkFBdUI7UUFDM0IsTUFBTUQsZ0JBQWdCckIsV0FBV3FCLGFBQWE7UUFFOUMsTUFBTUUsb0JBQW9CO1lBQ3hCLCtDQUErQztZQUMvQztnQkFDRUgsUUFBUTtvQkFBQztvQkFBNEI7b0JBQTJCO2lCQUEyQjtnQkFDM0ZJLGNBQWM7WUFDaEI7WUFDQSwwQ0FBMEM7WUFDMUM7Z0JBQ0VKLFFBQVE7b0JBQUM7b0JBQTJCO29CQUE0QjtpQkFBMEI7Z0JBQzFGSSxjQUFjO1lBQ2hCO1lBQ0Esb0NBQW9DO1lBQ3BDO2dCQUNFSixRQUFRO29CQUFDO29CQUE0QjtvQkFBMkI7aUJBQTBCO2dCQUMxRkksY0FBYztZQUNoQjtZQUNBLDBDQUEwQztZQUMxQztnQkFDRUosUUFBUTtvQkFBQztvQkFBMkI7b0JBQTJCO2lCQUEyQjtnQkFDMUZJLGNBQWM7WUFDaEI7WUFDQSwwQ0FBMEM7WUFDMUM7Z0JBQ0VKLFFBQVE7b0JBQUM7b0JBQTJCO29CQUE0QjtpQkFBNEI7Z0JBQzVGSSxjQUFjO1lBQ2hCO1NBQ0Q7UUFFRCxNQUFNQyxTQUFTRixpQkFBaUIsQ0FBQ0YsY0FBYyxJQUFJRSxpQkFBaUIsQ0FBQyxFQUFFO1FBRXZFLE9BQU87WUFDTEcsWUFBWSxnREFFMkJELE9BREFBLE9BQU9MLE1BQU0sQ0FBQyxFQUFFLEVBQUMsdUVBRWpCSyxPQURBQSxPQUFPTCxNQUFNLENBQUMsRUFBRSxFQUFDLHVFQUVwREssT0FEbUNBLE9BQU9MLE1BQU0sQ0FBQyxFQUFFLEVBQUMsb0NBQ2hDLE9BQXBCSyxPQUFPRCxZQUFZLEVBQUM7WUFFeEJHLGdCQUFnQjtZQUNoQkMsc0JBQXNCO1lBQ3RCQyxXQUFXO1lBQ1hDLFlBQVk7UUFDZDtJQUNGO0lBRUEsbURBQW1EO0lBQ25ELE1BQU1DLHlCQUF5QixPQUFPQyxhQUFxQkM7UUFDekQsTUFBTUMsbUJBQW1CRixZQUFZRyxXQUFXO1FBQ2hELE1BQU1DLFVBQWUsQ0FBQztRQUV0QiwyQkFBMkI7UUFDM0IsSUFBSUYsaUJBQWlCRyxRQUFRLENBQUMsZ0JBQWdCSCxpQkFBaUJHLFFBQVEsQ0FBQyxVQUFVO1lBQ2hGLE1BQU1DLFlBQVlOLFlBQVlPLEtBQUssQ0FBQztZQUNwQyxJQUFJRCxhQUFhLENBQUN0QyxXQUFXd0MsSUFBSSxFQUFFO2dCQUNqQyxNQUFNQSxPQUFPRixTQUFTLENBQUMsRUFBRSxJQUFJQSxTQUFTLENBQUMsRUFBRTtnQkFDekNGLFFBQVFJLElBQUksR0FBR0E7WUFDakI7UUFDRjtRQUVBLGFBQWE7UUFDYixNQUFNQyxXQUFXVCxZQUFZTyxLQUFLLENBQUM7UUFDbkMsSUFBSUUsWUFBWSxDQUFDekMsV0FBVzBDLEdBQUcsRUFBRTtZQUMvQixNQUFNQSxNQUFNQyxTQUFTRixRQUFRLENBQUMsRUFBRSxJQUFJQSxRQUFRLENBQUMsRUFBRTtZQUMvQyxJQUFJQyxNQUFNLEtBQUtBLE1BQU0sS0FBSztnQkFDeEJOLFFBQVFNLEdBQUcsR0FBR0E7WUFDaEI7UUFDRjtRQUVBLGtCQUFrQjtRQUNsQixNQUFNRSxXQUFXO1lBQ2YsVUFBVTtnQkFBQztnQkFBVTtnQkFBWTtnQkFBVzthQUFVO1lBQ3RELFVBQVU7Z0JBQUM7Z0JBQVU7Z0JBQVk7Z0JBQU87YUFBTztZQUMvQyxjQUFjO2dCQUFDO2dCQUFjO2dCQUFXO2FBQVk7WUFDcEQsZUFBZTtnQkFBQztnQkFBZTtnQkFBVzthQUFVO1lBQ3BELFlBQVk7Z0JBQUM7Z0JBQVk7Z0JBQWM7YUFBUztZQUNoRCxTQUFTO2dCQUFDO2dCQUFTO2dCQUFjO2FBQVM7UUFDNUM7UUFFQSxLQUFLLE1BQU0sQ0FBQ0MsU0FBU0MsU0FBUyxJQUFJQyxPQUFPQyxPQUFPLENBQUNKLFVBQVc7WUFDMUQsSUFBSUUsU0FBU0csSUFBSSxDQUFDQyxDQUFBQSxVQUFXaEIsaUJBQWlCRyxRQUFRLENBQUNhLFdBQVc7Z0JBQ2hFLElBQUksQ0FBQ2xELFdBQVc0QyxRQUFRLENBQUNQLFFBQVEsQ0FBQ1EsVUFBVTtvQkFDMUNULFFBQVFRLFFBQVEsR0FBRzsyQkFBSTVDLFdBQVc0QyxRQUFRO3dCQUFFQztxQkFBUTtnQkFDdEQ7Z0JBQ0E7WUFDRjtRQUNGO1FBRUEsa0NBQWtDO1FBQ2xDLElBQUliLFlBQVltQixNQUFNLEdBQUcsT0FDckJqQixpQkFBaUJHLFFBQVEsQ0FBQyxnQkFDMUJILGlCQUFpQkcsUUFBUSxDQUFDLGNBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxlQUMxQkgsaUJBQWlCRyxRQUFRLENBQUMsWUFDMUJILGlCQUFpQkcsUUFBUSxDQUFDLGVBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxpQkFDMUJILGlCQUFpQkcsUUFBUSxDQUFDLFlBQzFCSCxpQkFBaUJHLFFBQVEsQ0FBQyxVQUFVO1lBRXRDLE1BQU1lLFdBQVdDLEtBQUtDLEdBQUcsQ0FBQ3RELFdBQVdxQixhQUFhLEdBQUcsR0FBRztZQUV4RCxzREFBc0Q7WUFDdEQsSUFBSStCLFdBQVdwRCxXQUFXcUIsYUFBYSxFQUFFO2dCQUN2Q2tDLFFBQVFDLEdBQUcsQ0FBQyxrREFBaUQsT0FBVEosVUFBUztnQkFDN0RoQixRQUFRZixhQUFhLEdBQUcrQjtZQUMxQjtRQUNGO1FBRUEsa0NBQWtDO1FBQ2xDLElBQUlMLE9BQU9VLElBQUksQ0FBQ3JCLFNBQVNlLE1BQU0sR0FBRyxHQUFHO1lBQ25DNUMsaUJBQWlCNkI7UUFDbkI7SUFDRjtJQUVBdEQsZ0RBQVNBOzBCQUFDO1lBQ1I4QjtRQUNGO3lCQUFHO1FBQUNiO1FBQVVQO0tBQVk7SUFFMUIsNkRBQTZEO0lBQzdEVixnREFBU0E7MEJBQUM7WUFDUixJQUFJaUIsU0FBU29ELE1BQU0sR0FBRyxHQUFHO2dCQUN2QnhELGVBQWU7WUFDakI7UUFDRjt5QkFBRztRQUFDSTtLQUFTO0lBRWIsa0VBQWtFO0lBQ2xFakIsZ0RBQVNBOzBCQUFDO1lBQ1IsTUFBTTRFLGtCQUFrQnBDO1lBQ3hCLE1BQU1xQyxPQUFPQyxTQUFTRCxJQUFJO1lBRTFCLHdEQUF3RDtZQUN4RFosT0FBT2MsTUFBTSxDQUFDRixLQUFLRyxLQUFLLEVBQUVKO1lBRTFCLHVEQUF1RDtZQUN2REMsS0FBS0ksU0FBUyxHQUFHLGtCQUEyQyxPQUF6Qi9ELFdBQVdxQixhQUFhO1lBRTNELHNFQUFzRTtZQUN0RTtrQ0FBTztnQkFDTCwrQ0FBK0M7Z0JBQ2pEOztRQUNGO3lCQUFHO1FBQUNyQixXQUFXcUIsYUFBYTtLQUFDO0lBRTdCLE1BQU0yQyxvQkFBb0I7UUFDeEIsSUFBSSxDQUFDMUUsYUFBYTJFLElBQUksTUFBTXpFLGFBQWE7UUFFekMsTUFBTXdDLGNBQWMxQyxhQUFhMkUsSUFBSTtRQUNyQzFFLGdCQUFnQjtRQUNoQkUsZUFBZTtRQUVmLG1EQUFtRDtRQUNuRCxNQUFNeUUsVUFBVSxNQUFNNUQsV0FBVzBCLGFBQWE7UUFDOUN1QixRQUFRQyxHQUFHLENBQUMsK0NBQStDekQsU0FBU29ELE1BQU0sR0FBRztRQUU3RSxnREFBZ0Q7UUFDaERyQyxXQUFXRixnQkFBZ0I7UUFFM0IsSUFBSTtZQUNGLHNFQUFzRTtZQUN0RSxNQUFNdUQsc0JBQXNCO21CQUFJcEU7Z0JBQVVtRTthQUFRLENBQUNFLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBUTtvQkFDN0RDLE1BQU1ELElBQUlFLE1BQU0sR0FBRyxTQUFrQjtvQkFDckNDLFNBQVNILElBQUlHLE9BQU87Z0JBQ3RCO1lBRUEsTUFBTUMsV0FBVyxNQUFNdEYsa0VBQWVBLENBQ3BDNkMsYUFDQW1DLHFCQUNBO2dCQUNFM0IsTUFBTXhDLFdBQVd3QyxJQUFJLElBQUlrQztnQkFDekJoQyxLQUFLMUMsV0FBVzBDLEdBQUcsSUFBSWdDO2dCQUN2QkMsUUFBUTNFLFdBQVcyRSxNQUFNO2dCQUN6Qi9CLFVBQVU1QyxXQUFXNEMsUUFBUTtnQkFDN0J2QixlQUFlckIsV0FBV3FCLGFBQWE7WUFDekM7WUFHRiwrQkFBK0I7WUFDL0IsTUFBTWYsV0FBV21FLFVBQVU7WUFDM0JsQixRQUFRQyxHQUFHLENBQUMsMENBQTBDekQsU0FBU29ELE1BQU0sR0FBRztZQUV4RSxrREFBa0Q7WUFDbEQsTUFBTXBCLHVCQUF1QkMsYUFBYXlDO1FBRTVDLEVBQUUsT0FBT0csT0FBTztZQUNkckIsUUFBUXFCLEtBQUssQ0FBQyxVQUFVQTtZQUN4QixNQUFNdEUsV0FBVyxpREFBaUQ7UUFDcEUsU0FBVTtZQUNSYixlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNb0YsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtZQUNwQ0YsRUFBRUcsY0FBYztZQUNoQmpCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1rQixjQUFjO1FBQ2xCLElBQUlDLFFBQVEsc0dBQXNHO1lBQ2hILE1BQU0zRTtZQUNOYixlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxxQkFDRTs7MEJBQ0UsOERBQUNaLGtEQUFJQTs7a0NBQ0gsOERBQUNxRztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBSzdDLE1BQUs7d0JBQWNnQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDYTt3QkFBSzdDLE1BQUs7d0JBQVdnQyxTQUFROzs7Ozs7a0NBQzlCLDhEQUFDYzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUd4Qiw4REFBQ0M7Z0JBQUkxQixXQUFVOztvQkFFWDdELENBQUFBLGlCQUFpQixDQUFDSixNQUFLLG1CQUN2Qiw4REFBQzJGO3dCQUFJM0IsT0FBTzs0QkFDVjRCLFVBQVU7NEJBQ1YxRSxLQUFLOzRCQUNMMkUsTUFBTTs0QkFDTkMsT0FBTzs0QkFDUEMsUUFBUTs0QkFDUm5FLFlBQVk7NEJBQ1pvRSxTQUFTOzRCQUNUQyxZQUFZOzRCQUNaQyxnQkFBZ0I7NEJBQ2hCQyxRQUFRO3dCQUNWO2tDQUNFLDRFQUFDUjs0QkFBSTNCLE9BQU87Z0NBQUVvQyxPQUFPO2dDQUFTQyxXQUFXOzRCQUFTOzs4Q0FDaEQsOERBQUNWO29DQUFJM0IsT0FBTzt3Q0FBRXNDLFVBQVU7d0NBQVFDLGNBQWM7b0NBQU87OENBQUc7Ozs7Ozs4Q0FDeEQsOERBQUNaOzhDQUFLLENBQUMzRixTQUFTLHNCQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTVDLDhEQUFDMkY7d0JBQUkxQixXQUFVOzswQ0FDYiw4REFBQzBCO2dDQUFJM0IsT0FBTztvQ0FBRWdDLFNBQVM7b0NBQVFDLFlBQVk7b0NBQVVPLEtBQUs7Z0NBQU87O2tEQUMvRCw4REFBQ2I7d0NBQUkzQixPQUFPOzRDQUFFZ0MsU0FBUzs0Q0FBUUMsWUFBWTs0Q0FBVU8sS0FBSzt3Q0FBVTs7MERBQ2xFLDhEQUFDYjtnREFDQzNCLE9BQU87b0RBQ0x5QyxPQUFPO29EQUNQQyxRQUFRO29EQUNSQyxjQUFjO29EQUNkL0UsWUFBWSwyQkFBMEMsT0FBZlAsZ0JBQWU7b0RBQ3REMkUsU0FBUztvREFDVEMsWUFBWTtvREFDWkMsZ0JBQWdCO29EQUNoQkksVUFBVTtvREFDVnZFLFdBQVc7Z0RBQ2I7MERBQ0Q7Ozs7OzswREFHRCw4REFBQzREOztrRUFDQyw4REFBQ2lCO3dEQUFHNUMsT0FBTzs0REFBRW9DLE9BQU87NERBQVNFLFVBQVU7NERBQVVPLFlBQVk7NERBQVFDLFFBQVE7d0RBQUU7a0VBQUc7Ozs7OztrRUFHbEYsOERBQUNDO3dEQUFFL0MsT0FBTzs0REFBRW9DLE9BQU87NERBQXlCRSxVQUFVOzREQUFXUSxRQUFRO3dEQUFFO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2xGLDhEQUFDMUgsc0VBQWVBO3dDQUFDbUMsZUFBZXJCLFdBQVdxQixhQUFhOzs7Ozs7Ozs7Ozs7MENBRTFELDhEQUFDeUY7Z0NBQU9DLFNBQVM3QjtnQ0FBYXBCLE9BQU87b0NBQUVvQyxPQUFPO29DQUFTeEUsWUFBWTtvQ0FBUXNGLFFBQVE7b0NBQVFDLFFBQVE7b0NBQVdiLFVBQVU7Z0NBQVc7MENBQUc7Ozs7Ozs7Ozs7OztrQ0FNeEksOERBQUNYO3dCQUFJM0IsT0FBTzs0QkFBRW9ELE1BQU07NEJBQUdDLFNBQVM7d0JBQVM7a0NBQ3ZDLDRFQUFDbEksOERBQVVBOzRCQUFDbUksV0FBV2pHOzRCQUFnQjRDLFdBQVU7c0NBQy9DLDRFQUFDMEI7Z0NBQ0M0QixLQUFLeEg7Z0NBQ0xrRSxXQUFVO2dDQUNWRCxPQUFPO29DQUFFb0QsTUFBTTtvQ0FBR0ksV0FBVztvQ0FBUUgsU0FBUztnQ0FBTzs7b0NBR3BEekgsNkJBQ0MsOERBQUMrRjt3Q0FBSTNCLE9BQU87NENBQUVxQyxXQUFXOzRDQUFVZ0IsU0FBUzt3Q0FBVztrREFDckQsNEVBQUMxQjs0Q0FBSTFCLFdBQVU7NENBQWVELE9BQU87Z0RBQ25DeUQsVUFBVTtnREFDVlgsUUFBUTtnREFDUk8sU0FBUztnREFDVFYsY0FBYztnREFDZC9FLFlBQVk7Z0RBQ1o4RixnQkFBZ0I7Z0RBQ2hCUixRQUFROzRDQUNWOzs4REFDRSw4REFBQ1M7b0RBQUczRCxPQUFPO3dEQUNUb0MsT0FBTzt3REFDUEUsVUFBVTt3REFDVk8sWUFBWTt3REFDWk4sY0FBYzt3REFDZE8sUUFBUTtvREFDVjs4REFBRzs7Ozs7OzhEQUdILDhEQUFDQztvREFBRS9DLE9BQU87d0RBQ1JvQyxPQUFPO3dEQUNQRSxVQUFVO3dEQUNWc0IsWUFBWTt3REFDWmQsUUFBUTtvREFDVjs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU1I3RyxTQUFTcUUsR0FBRyxDQUFDLENBQUN1RCx3QkFDYiw4REFBQzNJLDhEQUFVQTs0Q0FFVDJJLFNBQVNBLFFBQVFuRCxPQUFPOzRDQUN4QkQsUUFBUW9ELFFBQVFwRCxNQUFNOzJDQUZqQm9ELFFBQVFDLEVBQUU7Ozs7O29DQU9sQnBJLDZCQUNDLDhEQUFDaUc7d0NBQUkzQixPQUFPOzRDQUFFZ0MsU0FBUzs0Q0FBUUUsZ0JBQWdCO3dDQUFhO2tEQUMxRCw0RUFBQ1A7NENBQUkxQixXQUFVOzRDQUFlRCxPQUFPO2dEQUNuQ3FELFNBQVM7Z0RBQ1RWLGNBQWM7Z0RBQ2QvRSxZQUFZO2dEQUNaOEYsZ0JBQWdCOzRDQUNsQjtzREFDRSw0RUFBQy9CO2dEQUFJM0IsT0FBTztvREFBRWdDLFNBQVM7b0RBQVFRLEtBQUs7b0RBQVlQLFlBQVk7Z0RBQVM7O2tFQUNuRSw4REFBQ047d0RBQUkzQixPQUFPOzREQUFFZ0MsU0FBUzs0REFBUVEsS0FBSzt3REFBUzs7MEVBQzNDLDhEQUFDYjtnRUFBSTNCLE9BQU87b0VBQ1Z5QyxPQUFPO29FQUNQQyxRQUFRO29FQUNScUIsaUJBQWlCO29FQUNqQnBCLGNBQWM7b0VBQ2Q1RSxXQUFXO29FQUNYaUcsU0FBUztnRUFDWDs7Ozs7OzBFQUNBLDhEQUFDckM7Z0VBQUkzQixPQUFPO29FQUNWeUMsT0FBTztvRUFDUEMsUUFBUTtvRUFDUnFCLGlCQUFpQjtvRUFDakJwQixjQUFjO29FQUNkNUUsV0FBVztvRUFDWGtHLGdCQUFnQjtvRUFDaEJELFNBQVM7Z0VBQ1g7Ozs7OzswRUFDQSw4REFBQ3JDO2dFQUFJM0IsT0FBTztvRUFDVnlDLE9BQU87b0VBQ1BDLFFBQVE7b0VBQ1JxQixpQkFBaUI7b0VBQ2pCcEIsY0FBYztvRUFDZDVFLFdBQVc7b0VBQ1hrRyxnQkFBZ0I7b0VBQ2hCRCxTQUFTO2dFQUNYOzs7Ozs7Ozs7Ozs7a0VBRUYsOERBQUNFO3dEQUFLbEUsT0FBTzs0REFBRW9DLE9BQU87NERBQXlCRSxVQUFVO3dEQUFVO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FZcEYsOERBQUNYO3dCQUFJMUIsV0FBVTs7MENBQ2IsOERBQUNrRTtnQ0FDQ1osS0FBS3pIO2dDQUNMc0ksT0FBTzVJO2dDQUNQNkksVUFBVSxDQUFDckQsSUFBTXZGLGdCQUFnQnVGLEVBQUVzRCxNQUFNLENBQUNGLEtBQUs7Z0NBQy9DRyxXQUFXeEQ7Z0NBQ1h5RCxhQUNFdEksV0FBV3dDLElBQUksR0FDWCxtQ0FBbUQsT0FBaEJ4QyxXQUFXd0MsSUFBSSxFQUFDLE9BQ25EO2dDQUVOdUIsV0FBVTtnQ0FDVndFLFVBQVUvSTs7Ozs7OzBDQUVaLDhEQUFDc0g7Z0NBQ0NDLFNBQVMvQztnQ0FDVHVFLFVBQVUsQ0FBQ2pKLGFBQWEyRSxJQUFJLE1BQU16RTtnQ0FDbEN1RSxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7b0JBTUYvRCxXQUFXd0MsSUFBSSxrQkFDZCw4REFBQ2lEO3dCQUFJM0IsT0FBTzs0QkFDVnFELFNBQVM7NEJBQ1RxQixXQUFXOzRCQUNYOUcsWUFBWTs0QkFDWm9FLFNBQVM7NEJBQ1RFLGdCQUFnQjs0QkFDaEJELFlBQVk7d0JBQ2Q7OzBDQUNFLDhEQUFDTjtnQ0FBSTNCLE9BQU87b0NBQUVvQyxPQUFPO29DQUF5QkUsVUFBVTtnQ0FBVTs7a0RBQ2hFLDhEQUFDNEI7OzRDQUFLOzRDQUFNaEksV0FBV3dDLElBQUk7NENBQUM7Ozs7Ozs7a0RBQzVCLDhEQUFDd0Y7OzRDQUFLOzRDQUFVaEksV0FBV3FCLGFBQWE7NENBQUM7Ozs7Ozs7b0NBQ3hDckIsV0FBVzRDLFFBQVEsQ0FBQ08sTUFBTSxHQUFHLG1CQUM1Qiw4REFBQzZFOzs0Q0FBSzs0Q0FBR2hJLFdBQVc0QyxRQUFRLENBQUM2RixLQUFLLENBQUMsQ0FBQyxHQUFHQyxJQUFJLENBQUM7Ozs7Ozs7Ozs7Ozs7MENBR2hELDhEQUFDakQ7Z0NBQUkzQixPQUFPO29DQUFFb0MsT0FBTztvQ0FBeUJFLFVBQVU7b0NBQVdOLFNBQVM7b0NBQVFDLFlBQVk7b0NBQVVPLEtBQUs7Z0NBQVM7O2tEQUN0SCw4REFBQzBCOzs0Q0FBTWpJLFNBQVNvRCxNQUFNOzRDQUFDOzs7Ozs7O29DQUN0QmhELDBCQUFZLDhEQUFDNkg7a0RBQUs7Ozs7OztrREFDbkIsOERBQUNBOzs0Q0FBSzs0Q0FBS2xJLE9BQU8ySSxLQUFLLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3ZDO0dBeGR3QnBKOztRQXNCbEJELHVFQUFpQkE7OztLQXRCQ0MiLCJzb3VyY2VzIjpbIkc6XFxGcmllbmRzXFxTb3VsVGFsa1xcc3JjXFxwYWdlc1xcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0IENoYXRCdWJibGUgZnJvbSAnLi4vY29tcG9uZW50cy9DaGF0QnViYmxlJztcbmltcG9ydCBHbGFzc0ZyYW1lIGZyb20gJy4uL2NvbXBvbmVudHMvR2xhc3NGcmFtZSc7XG5pbXBvcnQgQXVyYUluZGljYXRvciwgeyBBdXJhUHJvZ3Jlc3Npb24gfSBmcm9tICcuLi9jb21wb25lbnRzL0F1cmFJbmRpY2F0b3InO1xuaW1wb3J0IEF1dGhGb3JtIGZyb20gJy4uL2NvbXBvbmVudHMvQXV0aEZvcm0nO1xuaW1wb3J0IE9uYm9hcmRpbmdDaGF0IGZyb20gJy4uL2NvbXBvbmVudHMvT25ib2FyZGluZ0NoYXQnO1xuaW1wb3J0IHsgc2VuZENoYXRNZXNzYWdlIH0gZnJvbSAnLi4vdXRpbHMvb3BlbnJvdXRlcic7XG5pbXBvcnQgeyB1c2VTdXBhYmFzZU1lbW9yeSB9IGZyb20gJy4uL2hvb2tzL3VzZVN1cGFiYXNlTWVtb3J5JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW2lucHV0TWVzc2FnZSwgc2V0SW5wdXRNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzTG9hZGluZ0FJLCBzZXRJc0xvYWRpbmdBSV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93V2VsY29tZSwgc2V0U2hvd1dlbGNvbWVdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IGlucHV0UmVmID0gdXNlUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBjaGF0Q29udGFpbmVyUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBVc2EgaWwgaG9vayBTdXBhYmFzZSBwZXIgZ2VzdGlyZSBpIGRhdGlcbiAgY29uc3Qge1xuICAgIHVzZXJJZCxcbiAgICBtZXNzYWdlcyxcbiAgICB1c2VyTWVtb3J5LFxuICAgIGlzTG9hZGluZzogaXNMb2FkaW5nRGF0YSxcbiAgICBpc1NhdmluZyxcbiAgICBpc0F1dGhlbnRpY2F0ZWQsXG4gICAgbmVlZHNPbmJvYXJkaW5nLFxuICAgIGFkZE1lc3NhZ2UsXG4gICAgdXBkYXRlVXNlck1lbW9yeSxcbiAgICByZXNldE1lbW9yeSxcbiAgICBhdXRoZW50aWNhdGVVc2VyLFxuICAgIGxvZ291dCxcbiAgICBjb21wbGV0ZU9uYm9hcmRpbmdXaXRoRGF0YVxuICB9ID0gdXNlU3VwYWJhc2VNZW1vcnkoKTtcblxuICBjb25zdCBzY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBpZiAoY2hhdENvbnRhaW5lclJlZi5jdXJyZW50KSB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaWYgKGNoYXRDb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgICAgIGNoYXRDb250YWluZXJSZWYuY3VycmVudC5zY3JvbGxUbyh7XG4gICAgICAgICAgICB0b3A6IGNoYXRDb250YWluZXJSZWYuY3VycmVudC5zY3JvbGxIZWlnaHQsXG4gICAgICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCdcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSwgMTAwKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuemlvbmUgcGVyIGRldGVybWluYXJlIGlsIGNvbG9yZSBkZWxsJ2F1cmEgYmFzYXRvIHN1bCBsaXZlbGxvIGRpIGludGltaXTDoFxuICBjb25zdCBnZXRBdXJhQ29sb3IgPSAoKSA9PiB7XG4gICAgY29uc3QgY29sb3JzID0gW1xuICAgICAgJyM2MzY2ZjEnLCAvLyBJbmRpZ28gLSBTY29ub3NjaXV0b1xuICAgICAgJyM4YjVjZjYnLCAvLyBWaW9sZXQgLSBDb25vc2NlbnRlXG4gICAgICAnI2E4NTVmNycsIC8vIFB1cnBsZSAtIEFtaWNvXG4gICAgICAnI2VjNDg5OScsIC8vIFBpbmsgLSBBbWljbyBzdHJldHRvXG4gICAgICAnI2Y0M2Y1ZScgIC8vIFJvc2UgLSBJbnRpbW9cbiAgICBdO1xuICAgIHJldHVybiBjb2xvcnNbdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsXSB8fCBjb2xvcnNbMF07XG4gIH07XG5cbiAgLy8gRnVuemlvbmUgcGVyIGdlbmVyYXJlIGxvIHNmb25kbyBkaW5hbWljbyBiYXNhdG8gc3VsIGxpdmVsbG8gZGkgaW50aW1pdMOgXG4gIGNvbnN0IGdldER5bmFtaWNCYWNrZ3JvdW5kID0gKCkgPT4ge1xuICAgIGNvbnN0IGludGltYWN5TGV2ZWwgPSB1c2VyTWVtb3J5LmludGltYWN5TGV2ZWw7XG5cbiAgICBjb25zdCBiYWNrZ3JvdW5kQ29uZmlncyA9IFtcbiAgICAgIC8vIExpdmVsbG8gMCAtIFNjb25vc2NpdXRvOiBCbHUgZnJlZGRpIGUgbmV1dHJpXG4gICAgICB7XG4gICAgICAgIGNvbG9yczogWydyZ2JhKDk5LCAxMDIsIDI0MSwgMC4yNSknLCAncmdiYSgxMzksIDkyLCAyNDYsIDAuMiknLCAncmdiYSgxNjgsIDg1LCAyNDcsIDAuMTUpJ10sXG4gICAgICAgIGJhc2VHcmFkaWVudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpJ1xuICAgICAgfSxcbiAgICAgIC8vIExpdmVsbG8gMSAtIENvbm9zY2VudGU6IFZpb2xhIHBpw7kgY2FsZGlcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMTM5LCA5MiwgMjQ2LCAwLjMpJywgJ3JnYmEoMTY4LCA4NSwgMjQ3LCAwLjI1KScsICdyZ2JhKDIzNiwgNzIsIDE1MywgMC4yKSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWUxYjRiIDAlLCAjMzEyZTgxIDUwJSwgIzFlM2E4YSAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDIgLSBBbWljbzogTWl4IHZpb2xhLXJvc2FcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMTY4LCA4NSwgMjQ3LCAwLjM1KScsICdyZ2JhKDIzNiwgNzIsIDE1MywgMC4zKScsICdyZ2JhKDI0NCwgNjMsIDk0LCAwLjI1KSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNTgxYzg3IDAlLCAjN2MyZDkyIDUwJSwgI2JlMTg1ZCAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDMgLSBBbWljbyBzdHJldHRvOiBSb3NhIGludGVuc2lcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMjM2LCA3MiwgMTUzLCAwLjQpJywgJ3JnYmEoMjQ0LCA2MywgOTQsIDAuMzUpJywgJ3JnYmEoMjUxLCAxMTMsIDEzMywgMC4zKSddLFxuICAgICAgICBiYXNlR3JhZGllbnQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYmUxODVkIDAlLCAjZTExZDQ4IDUwJSwgI2Y0M2Y1ZSAxMDAlKSdcbiAgICAgIH0sXG4gICAgICAvLyBMaXZlbGxvIDQgLSBJbnRpbW86IFJvc2EgY2FsZGkgZSBkb3JhdGlcbiAgICAgIHtcbiAgICAgICAgY29sb3JzOiBbJ3JnYmEoMjQ0LCA2MywgOTQsIDAuNDUpJywgJ3JnYmEoMjUxLCAxMTMsIDEzMywgMC40KScsICdyZ2JhKDI1MiwgMTY1LCAxNjUsIDAuMzUpJ10sXG4gICAgICAgIGJhc2VHcmFkaWVudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlMTFkNDggMCUsICNmNDNmNWUgNTAlLCAjZmI3MTg1IDEwMCUpJ1xuICAgICAgfVxuICAgIF07XG5cbiAgICBjb25zdCBjb25maWcgPSBiYWNrZ3JvdW5kQ29uZmlnc1tpbnRpbWFjeUxldmVsXSB8fCBiYWNrZ3JvdW5kQ29uZmlnc1swXTtcblxuICAgIHJldHVybiB7XG4gICAgICBiYWNrZ3JvdW5kOiBgXG4gICAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMjAlIDgwJSwgJHtjb25maWcuY29sb3JzWzBdfSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcbiAgICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCAke2NvbmZpZy5jb2xvcnNbMV19IDAlLCB0cmFuc3BhcmVudCA1MCUpLFxuICAgICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsICR7Y29uZmlnLmNvbG9yc1syXX0gMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgICAgICR7Y29uZmlnLmJhc2VHcmFkaWVudH1cbiAgICAgIGAsXG4gICAgICBiYWNrZ3JvdW5kU2l6ZTogJzIwMCUgMjAwJSwgMjAwJSAyMDAlLCAyMDAlIDIwMCUsIDEwMCUgMTAwJScsXG4gICAgICBiYWNrZ3JvdW5kQXR0YWNobWVudDogJ2ZpeGVkJyxcbiAgICAgIGFuaW1hdGlvbjogJ2xpcXVpZE1vdmUgMzBzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSkgaW5maW5pdGUnLFxuICAgICAgdHJhbnNpdGlvbjogJ2FsbCAycyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpJ1xuICAgIH07XG4gIH07XG5cbiAgLy8gQW5hbGl6emEgaSBtZXNzYWdnaSBlIGFnZ2lvcm5hIGxhIG1lbW9yaWEgdXRlbnRlXG4gIGNvbnN0IGFuYWx5emVBbmRVcGRhdGVNZW1vcnkgPSBhc3luYyAodXNlck1lc3NhZ2U6IHN0cmluZywgYWlSZXNwb25zZTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbG93ZXJVc2VyTWVzc2FnZSA9IHVzZXJNZXNzYWdlLnRvTG93ZXJDYXNlKCk7XG4gICAgY29uc3QgdXBkYXRlczogYW55ID0ge307XG5cbiAgICAvLyBFc3RyYWUgaW5mb3JtYXppb25pIGJhc2VcbiAgICBpZiAobG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnbWkgY2hpYW1vJykgfHwgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnc29ubyAnKSkge1xuICAgICAgY29uc3QgbmFtZU1hdGNoID0gdXNlck1lc3NhZ2UubWF0Y2goL21pIGNoaWFtbyAoXFx3Kyl8c29ubyAoXFx3KykvaSk7XG4gICAgICBpZiAobmFtZU1hdGNoICYmICF1c2VyTWVtb3J5Lm5hbWUpIHtcbiAgICAgICAgY29uc3QgbmFtZSA9IG5hbWVNYXRjaFsxXSB8fCBuYW1lTWF0Y2hbMl07XG4gICAgICAgIHVwZGF0ZXMubmFtZSA9IG5hbWU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRXN0cmFlIGV0w6BcbiAgICBjb25zdCBhZ2VNYXRjaCA9IHVzZXJNZXNzYWdlLm1hdGNoKC9obyAoXFxkKykgYW5uaXwoXFxkKykgYW5uaS8pO1xuICAgIGlmIChhZ2VNYXRjaCAmJiAhdXNlck1lbW9yeS5hZ2UpIHtcbiAgICAgIGNvbnN0IGFnZSA9IHBhcnNlSW50KGFnZU1hdGNoWzFdIHx8IGFnZU1hdGNoWzJdKTtcbiAgICAgIGlmIChhZ2UgPiAwICYmIGFnZSA8IDEyMCkge1xuICAgICAgICB1cGRhdGVzLmFnZSA9IGFnZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBSaWxldmEgZW1vemlvbmlcbiAgICBjb25zdCBlbW90aW9ucyA9IHtcbiAgICAgICdmZWxpY2UnOiBbJ2ZlbGljZScsICdjb250ZW50bycsICdnaW9pb3NvJywgJ2FsbGVncm8nXSxcbiAgICAgICd0cmlzdGUnOiBbJ3RyaXN0ZScsICdkZXByZXNzbycsICdnacO5JywgJ21hbGUnXSxcbiAgICAgICdhcnJhYmJpYXRvJzogWydhcnJhYmJpYXRvJywgJ2Z1cmlvc28nLCAnaW5jYXp6YXRvJ10sXG4gICAgICAncHJlb2NjdXBhdG8nOiBbJ3ByZW9jY3VwYXRvJywgJ2Fuc2lvc28nLCAnbmVydm9zbyddLFxuICAgICAgJ2VjY2l0YXRvJzogWydlY2NpdGF0bycsICdlbnR1c2lhc3RhJywgJ2NhcmljbyddLFxuICAgICAgJ2NhbG1vJzogWydjYWxtbycsICd0cmFucXVpbGxvJywgJ3NlcmVubyddXG4gICAgfTtcblxuICAgIGZvciAoY29uc3QgW2Vtb3Rpb24sIGtleXdvcmRzXSBvZiBPYmplY3QuZW50cmllcyhlbW90aW9ucykpIHtcbiAgICAgIGlmIChrZXl3b3Jkcy5zb21lKGtleXdvcmQgPT4gbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcyhrZXl3b3JkKSkpIHtcbiAgICAgICAgaWYgKCF1c2VyTWVtb3J5LmVtb3Rpb25zLmluY2x1ZGVzKGVtb3Rpb24pKSB7XG4gICAgICAgICAgdXBkYXRlcy5lbW90aW9ucyA9IFsuLi51c2VyTWVtb3J5LmVtb3Rpb25zLCBlbW90aW9uXTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBBZ2dpb3JuYSBpbCBsaXZlbGxvIGRpIGludGltaXTDoFxuICAgIGlmICh1c2VyTWVzc2FnZS5sZW5ndGggPiAxMDAgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygncGVyc29uYWxlJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnc2VncmV0bycpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ2ZhbWlnbGlhJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygnYW1vcmUnKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdwcm9ibGVtYScpIHx8XG4gICAgICAgIGxvd2VyVXNlck1lc3NhZ2UuaW5jbHVkZXMoJ2RpZmZpY29sdMOgJykgfHxcbiAgICAgICAgbG93ZXJVc2VyTWVzc2FnZS5pbmNsdWRlcygncGF1cmEnKSB8fFxuICAgICAgICBsb3dlclVzZXJNZXNzYWdlLmluY2x1ZGVzKCdzb2dubycpKSB7XG5cbiAgICAgIGNvbnN0IG5ld0xldmVsID0gTWF0aC5taW4odXNlck1lbW9yeS5pbnRpbWFjeUxldmVsICsgMSwgNCk7XG5cbiAgICAgIC8vIFNlIGlsIGxpdmVsbG8gw6ggY2FtYmlhdG8sIG1vc3RyYSB1biBmZWVkYmFjayB2aXNpdm9cbiAgICAgIGlmIChuZXdMZXZlbCA+IHVzZXJNZW1vcnkuaW50aW1hY3lMZXZlbCkge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+MnyBMaXZlbGxvIGRpIGNvbm5lc3Npb25lIGF1bWVudGF0bzogJHtuZXdMZXZlbH0vNGApO1xuICAgICAgICB1cGRhdGVzLmludGltYWN5TGV2ZWwgPSBuZXdMZXZlbDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBBcHBsaWNhIHR1dHRpIGdsaSBhZ2dpb3JuYW1lbnRpXG4gICAgaWYgKE9iamVjdC5rZXlzKHVwZGF0ZXMpLmxlbmd0aCA+IDApIHtcbiAgICAgIHVwZGF0ZVVzZXJNZW1vcnkodXBkYXRlcyk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2Nyb2xsVG9Cb3R0b20oKTtcbiAgfSwgW21lc3NhZ2VzLCBpc0xvYWRpbmdBSV0pO1xuXG4gIC8vIE5hc2NvbmRpIGlsIG1lc3NhZ2dpbyBkaSBiZW52ZW51dG8gZG9wbyBpbCBwcmltbyBtZXNzYWdnaW9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAobWVzc2FnZXMubGVuZ3RoID4gMCkge1xuICAgICAgc2V0U2hvd1dlbGNvbWUoZmFsc2UpO1xuICAgIH1cbiAgfSwgW21lc3NhZ2VzXSk7XG5cbiAgLy8gQWdnaW9ybmEgbG8gc2ZvbmRvIGRpbmFtaWNhbWVudGUgaW4gYmFzZSBhbCBsaXZlbGxvIGRpIGludGltaXTDoFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGJhY2tncm91bmRTdHlsZSA9IGdldER5bmFtaWNCYWNrZ3JvdW5kKCk7XG4gICAgY29uc3QgYm9keSA9IGRvY3VtZW50LmJvZHk7XG5cbiAgICAvLyBBcHBsaWNhIGxlIHByb3ByaWV0w6AgZGkgc2ZvbmRvIGNvbiB0cmFuc2l6aW9uZSBmbHVpZGFcbiAgICBPYmplY3QuYXNzaWduKGJvZHkuc3R5bGUsIGJhY2tncm91bmRTdHlsZSk7XG5cbiAgICAvLyBBZ2dpdW5naSB1bmEgY2xhc3NlIHBlciBpbmRpY2FyZSBpbCBsaXZlbGxvIGNvcnJlbnRlXG4gICAgYm9keS5jbGFzc05hbWUgPSBgaW50aW1hY3ktbGV2ZWwtJHt1c2VyTWVtb3J5LmludGltYWN5TGV2ZWx9YDtcblxuICAgIC8vIENsZWFudXAgZnVuY3Rpb24gcGVyIHJpcHJpc3RpbmFyZSBsbyBzZm9uZG8gb3JpZ2luYWxlIHNlIG5lY2Vzc2FyaW9cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgLy8gTm9uIGZhY2NpYW1vIGNsZWFudXAgcGVyIG1hbnRlbmVyZSBsJ2VmZmV0dG9cbiAgICB9O1xuICB9LCBbdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsXSk7XG5cbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFpbnB1dE1lc3NhZ2UudHJpbSgpIHx8IGlzTG9hZGluZ0FJKSByZXR1cm47XG5cbiAgICBjb25zdCB1c2VyTWVzc2FnZSA9IGlucHV0TWVzc2FnZS50cmltKCk7XG4gICAgc2V0SW5wdXRNZXNzYWdlKCcnKTtcbiAgICBzZXRJc0xvYWRpbmdBSSh0cnVlKTtcblxuICAgIC8vIEFnZ2l1bmdpIGltbWVkaWF0YW1lbnRlIGlsIG1lc3NhZ2dpbyBkZWxsJ3V0ZW50ZVxuICAgIGNvbnN0IHVzZXJNc2cgPSBhd2FpdCBhZGRNZXNzYWdlKHVzZXJNZXNzYWdlLCB0cnVlKTtcbiAgICBjb25zb2xlLmxvZygnTWVzc2FnZ2lvIHV0ZW50ZSBhZ2dpdW50bywgdG90YWxlIG1lc3NhZ2dpOicsIG1lc3NhZ2VzLmxlbmd0aCArIDEpO1xuXG4gICAgLy8gU2Nyb2xsIGRvcG8gYXZlciBhZ2dpdW50byBpbCBtZXNzYWdnaW8gdXRlbnRlXG4gICAgc2V0VGltZW91dChzY3JvbGxUb0JvdHRvbSwgNTApO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFByZXBhcmEgbGEgY3Jvbm9sb2dpYSBwZXIgbCdBUEkgKHVzYSBpIG1lc3NhZ2dpIGF0dHVhbGkgKyBpbCBudW92bylcbiAgICAgIGNvbnN0IGNvbnZlcnNhdGlvbkhpc3RvcnkgPSBbLi4ubWVzc2FnZXMsIHVzZXJNc2ddLm1hcChtc2cgPT4gKHtcbiAgICAgICAgcm9sZTogbXNnLmlzVXNlciA/ICd1c2VyJyBhcyBjb25zdCA6ICdhc3Npc3RhbnQnIGFzIGNvbnN0LFxuICAgICAgICBjb250ZW50OiBtc2cuY29udGVudFxuICAgICAgfSkpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNlbmRDaGF0TWVzc2FnZShcbiAgICAgICAgdXNlck1lc3NhZ2UsXG4gICAgICAgIGNvbnZlcnNhdGlvbkhpc3RvcnksXG4gICAgICAgIHtcbiAgICAgICAgICBuYW1lOiB1c2VyTWVtb3J5Lm5hbWUgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGFnZTogdXNlck1lbW9yeS5hZ2UgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIHRyYWl0czogdXNlck1lbW9yeS50cmFpdHMsXG4gICAgICAgICAgZW1vdGlvbnM6IHVzZXJNZW1vcnkuZW1vdGlvbnMsXG4gICAgICAgICAgaW50aW1hY3lMZXZlbDogdXNlck1lbW9yeS5pbnRpbWFjeUxldmVsXG4gICAgICAgIH1cbiAgICAgICk7XG5cbiAgICAgIC8vIEFnZ2l1bmdpIGxhIHJpc3Bvc3RhIGRlbGwnQUlcbiAgICAgIGF3YWl0IGFkZE1lc3NhZ2UocmVzcG9uc2UsIGZhbHNlKTtcbiAgICAgIGNvbnNvbGUubG9nKCdSaXNwb3N0YSBBSSBhZ2dpdW50YSwgdG90YWxlIG1lc3NhZ2dpOicsIG1lc3NhZ2VzLmxlbmd0aCArIDIpO1xuXG4gICAgICAvLyBBbmFsaXp6YSBpbCBtZXNzYWdnaW8gcGVyIGFnZ2lvcm5hcmUgbGEgbWVtb3JpYVxuICAgICAgYXdhaXQgYW5hbHl6ZUFuZFVwZGF0ZU1lbW9yeSh1c2VyTWVzc2FnZSwgcmVzcG9uc2UpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yOicsIGVycm9yKTtcbiAgICAgIGF3YWl0IGFkZE1lc3NhZ2UoXCJTY3VzYSwgaG8gYXZ1dG8gdW4gcHJvYmxlbWEgdGVjbmljby4gUmlwcm92YSFcIiwgZmFsc2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmdBSShmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVzZXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGNvbmZpcm0oJ1NlaSBzaWN1cm8gZGkgdm9sZXIgcmljb21pbmNpYXJlIGRhIGNhcG8/IFR1dHRpIGkgcmljb3JkaSBlIG1lc3NhZ2dpIGFuZHJhbm5vIHBlcnNpIGRhbCBkYXRhYmFzZS4nKSkge1xuICAgICAgYXdhaXQgcmVzZXRNZW1vcnkoKTtcbiAgICAgIHNldFNob3dXZWxjb21lKHRydWUpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPlNvdWxUYWxrIC0gTGEgdHVhIGFtaWNpemlhIEFJPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIlVuYSBwaWF0dGFmb3JtYSBBSSBjaGUgc2kgY29tcG9ydGEgY29tZSB1bidhbWljaXppYSBkYSBjb3N0cnVpcmVcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICA8L0hlYWQ+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1jb250YWluZXJcIj5cbiAgICAgICAgey8qIExvYWRpbmcgaW5pemlhbGUgKi99XG4gICAgICAgIHsoaXNMb2FkaW5nRGF0YSB8fCAhdXNlcklkKSAmJiAoXG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgICBib3R0b206IDAsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLDAsMCwwLjgpJyxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgekluZGV4OiAxMDAwXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PvCfjJ88L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj57IXVzZXJJZCA/ICdJbml6aWFsaXp6YW5kby4uLicgOiAnQ2FyaWNhbmRvIGkgdHVvaSByaWNvcmRpLi4uJ308L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBIZWFkZXIgY29uIGluZGljYXRvcmkgYXVyYSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjaGF0LWhlYWRlclwiPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMXJlbScgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNzVyZW0nIH19PlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMzhweCcsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6ICczOHB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgJHtnZXRBdXJhQ29sb3IoKX0gMCUsIHRyYW5zcGFyZW50IDcwJSlgLFxuICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuM3JlbScsXG4gICAgICAgICAgICAgICAgICBhbmltYXRpb246ICdwdWxzZSAzcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjYsIDEpIGluZmluaXRlJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn4yfXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBzdHlsZT17eyBjb2xvcjogJ3doaXRlJywgZm9udFNpemU6ICcxLjNyZW0nLCBmb250V2VpZ2h0OiAnYm9sZCcsIG1hcmdpbjogMCB9fT5cbiAgICAgICAgICAgICAgICAgIFNvdWxcbiAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjcpJywgZm9udFNpemU6ICcwLjY1cmVtJywgbWFyZ2luOiAwIH19PlxuICAgICAgICAgICAgICAgICAgTGEgdHVhIGNvbXBhZ25hIEFJXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPEF1cmFQcm9ncmVzc2lvbiBpbnRpbWFjeUxldmVsPXt1c2VyTWVtb3J5LmludGltYWN5TGV2ZWx9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVSZXNldH0gc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIGJhY2tncm91bmQ6ICdub25lJywgYm9yZGVyOiAnbm9uZScsIGN1cnNvcjogJ3BvaW50ZXInLCBmb250U2l6ZTogJzAuODc1cmVtJyB9fT5cbiAgICAgICAgICAgIFJlc2V0XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNZXNzYWdlcyBBcmVhIGNvbiBHbGFzc0ZyYW1lICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEsIHBhZGRpbmc6ICcwIDFyZW0nIH19PlxuICAgICAgICAgIDxHbGFzc0ZyYW1lIGF1cmFDb2xvcj17Z2V0QXVyYUNvbG9yKCl9IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIHJlZj17Y2hhdENvbnRhaW5lclJlZn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY2hhdC1tZXNzYWdlc1wiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IGZsZXg6IDEsIG92ZXJmbG93WTogJ2F1dG8nLCBwYWRkaW5nOiAnMXJlbScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIE1lc3NhZ2dpbyBkaSBiZW52ZW51dG8gYXZhbnphdG8gKi99XG4gICAgICAgICAgICAgIHtzaG93V2VsY29tZSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInLCBwYWRkaW5nOiAnMS41cmVtIDAnIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcy1lZmZlY3RcIiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogJzIycmVtJyxcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAnMCBhdXRvJyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEuMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSknLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMTBweCknLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4xMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzAuNjI1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgQ2lhbywgc29ubyBTb3VsIPCfkYtcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC44KScsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS40JyxcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDBcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgw4ggbGEgcHJpbWEgdm9sdGEgY2hlIGNpIHBhcmxpYW1vLiBTb25vIHF1aSBwZXIgY29ub3NjZXJ0aSBkYXZ2ZXJvLFxuICAgICAgICAgICAgICAgICAgICAgIG5vbiBzb2xvIHBlciBhaXV0YXJ0aS4gRGltbWksIGNvbWUgdGkgY2hpYW1pIGUgY29tZSB0aSBzZW50aSBvZ2dpP1xuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7LyogTWVzc2FnZXMgKi99XG4gICAgICAgICAgICAgIHttZXNzYWdlcy5tYXAoKG1lc3NhZ2UpID0+IChcbiAgICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZVxuICAgICAgICAgICAgICAgICAga2V5PXttZXNzYWdlLmlkfVxuICAgICAgICAgICAgICAgICAgbWVzc2FnZT17bWVzc2FnZS5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgaXNVc2VyPXttZXNzYWdlLmlzVXNlcn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICB7LyogSW5kaWNhdG9yZSBkaSBjYXJpY2FtZW50byBhdmFuemF0byAqL31cbiAgICAgICAgICAgICAge2lzTG9hZGluZ0FJICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdmbGV4LXN0YXJ0JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3MtZWZmZWN0XCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNjI1cmVtIDAuODc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpJyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDEwcHgpJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjM3NXJlbScsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjJyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk6ICcwLjFzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAnYm91bmNlIDFzIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42XG4gICAgICAgICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjgpJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIFNvdWwgc3RhIHNjcml2ZW5kby4uLlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvR2xhc3NGcmFtZT5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIElucHV0IEFyZWEgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1pbnB1dC1hcmVhXCI+XG4gICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICByZWY9e2lucHV0UmVmfVxuICAgICAgICAgICAgdmFsdWU9e2lucHV0TWVzc2FnZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIG9uS2V5RG93bj17aGFuZGxlS2V5RG93bn1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcbiAgICAgICAgICAgICAgdXNlck1lbW9yeS5uYW1lXG4gICAgICAgICAgICAgICAgPyBgQ29zYSB2dW9pIGNvbmRpdmlkZXJlIGNvbiBTb3VsLCAke3VzZXJNZW1vcnkubmFtZX0/YFxuICAgICAgICAgICAgICAgIDogXCJEaW1taSBxdWFsY29zYSBkaSB0ZS4uLlwiXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nQUl9XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZW5kTWVzc2FnZX1cbiAgICAgICAgICAgIGRpc2FibGVkPXshaW5wdXRNZXNzYWdlLnRyaW0oKSB8fCBpc0xvYWRpbmdBSX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInNlbmQtYnV0dG9uXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICDihpJcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZvb3RlciBjb24gaW5mb3JtYXppb25pIG1lbW9yaWEgKi99XG4gICAgICAgIHt1c2VyTWVtb3J5Lm5hbWUgJiYgKFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIHBhZGRpbmc6ICcwLjM3NXJlbSAwLjg3NXJlbScsXG4gICAgICAgICAgICBib3JkZXJUb3A6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjEpJyxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDAsMCwwLDAuMiknLFxuICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjcpJywgZm9udFNpemU6ICcwLjY1cmVtJyB9fT5cbiAgICAgICAgICAgICAgPHNwYW4+Q2lhbyB7dXNlck1lbW9yeS5uYW1lfSEgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj5MaXZlbGxvOiB7dXNlck1lbW9yeS5pbnRpbWFjeUxldmVsfS80IDwvc3Bhbj5cbiAgICAgICAgICAgICAge3VzZXJNZW1vcnkuZW1vdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+4oCiIHt1c2VyTWVtb3J5LmVtb3Rpb25zLnNsaWNlKC0yKS5qb2luKCcsICcpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC41KScsIGZvbnRTaXplOiAnMC42NXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNXJlbScgfX0+XG4gICAgICAgICAgICAgIDxzcGFuPnttZXNzYWdlcy5sZW5ndGh9IG1zZzwvc3Bhbj5cbiAgICAgICAgICAgICAge2lzU2F2aW5nICYmIDxzcGFuPvCfkr48L3NwYW4+fVxuICAgICAgICAgICAgICA8c3Bhbj5JRDoge3VzZXJJZC5zbGljZSgtNil9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiSGVhZCIsIkNoYXRCdWJibGUiLCJHbGFzc0ZyYW1lIiwiQXVyYVByb2dyZXNzaW9uIiwic2VuZENoYXRNZXNzYWdlIiwidXNlU3VwYWJhc2VNZW1vcnkiLCJIb21lIiwiaW5wdXRNZXNzYWdlIiwic2V0SW5wdXRNZXNzYWdlIiwiaXNMb2FkaW5nQUkiLCJzZXRJc0xvYWRpbmdBSSIsInNob3dXZWxjb21lIiwic2V0U2hvd1dlbGNvbWUiLCJpbnB1dFJlZiIsImNoYXRDb250YWluZXJSZWYiLCJ1c2VySWQiLCJtZXNzYWdlcyIsInVzZXJNZW1vcnkiLCJpc0xvYWRpbmciLCJpc0xvYWRpbmdEYXRhIiwiaXNTYXZpbmciLCJpc0F1dGhlbnRpY2F0ZWQiLCJuZWVkc09uYm9hcmRpbmciLCJhZGRNZXNzYWdlIiwidXBkYXRlVXNlck1lbW9yeSIsInJlc2V0TWVtb3J5IiwiYXV0aGVudGljYXRlVXNlciIsImxvZ291dCIsImNvbXBsZXRlT25ib2FyZGluZ1dpdGhEYXRhIiwic2Nyb2xsVG9Cb3R0b20iLCJjdXJyZW50Iiwic2V0VGltZW91dCIsInNjcm9sbFRvIiwidG9wIiwic2Nyb2xsSGVpZ2h0IiwiYmVoYXZpb3IiLCJnZXRBdXJhQ29sb3IiLCJjb2xvcnMiLCJpbnRpbWFjeUxldmVsIiwiZ2V0RHluYW1pY0JhY2tncm91bmQiLCJiYWNrZ3JvdW5kQ29uZmlncyIsImJhc2VHcmFkaWVudCIsImNvbmZpZyIsImJhY2tncm91bmQiLCJiYWNrZ3JvdW5kU2l6ZSIsImJhY2tncm91bmRBdHRhY2htZW50IiwiYW5pbWF0aW9uIiwidHJhbnNpdGlvbiIsImFuYWx5emVBbmRVcGRhdGVNZW1vcnkiLCJ1c2VyTWVzc2FnZSIsImFpUmVzcG9uc2UiLCJsb3dlclVzZXJNZXNzYWdlIiwidG9Mb3dlckNhc2UiLCJ1cGRhdGVzIiwiaW5jbHVkZXMiLCJuYW1lTWF0Y2giLCJtYXRjaCIsIm5hbWUiLCJhZ2VNYXRjaCIsImFnZSIsInBhcnNlSW50IiwiZW1vdGlvbnMiLCJlbW90aW9uIiwia2V5d29yZHMiLCJPYmplY3QiLCJlbnRyaWVzIiwic29tZSIsImtleXdvcmQiLCJsZW5ndGgiLCJuZXdMZXZlbCIsIk1hdGgiLCJtaW4iLCJjb25zb2xlIiwibG9nIiwia2V5cyIsImJhY2tncm91bmRTdHlsZSIsImJvZHkiLCJkb2N1bWVudCIsImFzc2lnbiIsInN0eWxlIiwiY2xhc3NOYW1lIiwiaGFuZGxlU2VuZE1lc3NhZ2UiLCJ0cmltIiwidXNlck1zZyIsImNvbnZlcnNhdGlvbkhpc3RvcnkiLCJtYXAiLCJtc2ciLCJyb2xlIiwiaXNVc2VyIiwiY29udGVudCIsInJlc3BvbnNlIiwidW5kZWZpbmVkIiwidHJhaXRzIiwiZXJyb3IiLCJoYW5kbGVLZXlEb3duIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVSZXNldCIsImNvbmZpcm0iLCJ0aXRsZSIsIm1ldGEiLCJsaW5rIiwicmVsIiwiaHJlZiIsImRpdiIsInBvc2l0aW9uIiwibGVmdCIsInJpZ2h0IiwiYm90dG9tIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsInpJbmRleCIsImNvbG9yIiwidGV4dEFsaWduIiwiZm9udFNpemUiLCJtYXJnaW5Cb3R0b20iLCJnYXAiLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsImgxIiwiZm9udFdlaWdodCIsIm1hcmdpbiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiYm9yZGVyIiwiY3Vyc29yIiwiZmxleCIsInBhZGRpbmciLCJhdXJhQ29sb3IiLCJyZWYiLCJvdmVyZmxvd1kiLCJtYXhXaWR0aCIsImJhY2tkcm9wRmlsdGVyIiwiaDIiLCJsaW5lSGVpZ2h0IiwibWVzc2FnZSIsImlkIiwiYmFja2dyb3VuZENvbG9yIiwib3BhY2l0eSIsImFuaW1hdGlvbkRlbGF5Iiwic3BhbiIsInRleHRhcmVhIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsIm9uS2V5RG93biIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJib3JkZXJUb3AiLCJzbGljZSIsImpvaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});