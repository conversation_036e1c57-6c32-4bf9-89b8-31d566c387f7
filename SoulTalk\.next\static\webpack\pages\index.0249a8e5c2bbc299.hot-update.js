"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkOnboardingStatus: () => (/* binding */ checkOnboardingStatus),\n/* harmony export */   completeOnboarding: () => (/* binding */ completeOnboarding),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(pages-dir-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(pages-dir-browser)/./node_modules/bcryptjs/index.js\");\n\n\n// Funzione per hash della password\nconst hashPassword = async (password)=>{\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n};\n// Funzione per verificare la password\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n};\n// Registrazione nuovo utente\nconst registerUser = async (username, password)=>{\n    try {\n        console.log('Attempting to register user:', username);\n        // Controlla se l'username esiste già\n        const { data: existingUser, error: checkError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('username').eq('username', username.toLowerCase()).single();\n        if (checkError && checkError.code !== 'PGRST116') {\n            console.error('Error checking existing user:', checkError);\n            return {\n                success: false,\n                error: 'Errore durante il controllo username'\n            };\n        }\n        if (existingUser) {\n            return {\n                success: false,\n                error: 'Username già esistente'\n            };\n        }\n        // Crea hash della password\n        const passwordHash = await hashPassword(password);\n        // Genera ID utente\n        const userId = \"user_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        console.log('Generated userId:', userId);\n        // Inserisci nella tabella auth\n        const { error: authError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            password_hash: passwordHash\n        });\n        if (authError) {\n            console.error('Error creating auth:', authError);\n            return {\n                success: false,\n                error: 'Errore durante la registrazione: ' + authError.message\n            };\n        }\n        // Crea profilo utente\n        const { error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').insert({\n            id: userId,\n            username: username.toLowerCase(),\n            name: '',\n            is_onboarded: false\n        });\n        if (profileError) {\n            console.error('Error creating profile:', profileError);\n            // Rollback: elimina l'auth se il profilo fallisce\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').delete().eq('id', userId);\n            return {\n                success: false,\n                error: 'Errore durante la creazione del profilo: ' + profileError.message\n            };\n        }\n        console.log('User registered successfully:', userId);\n        return {\n            success: true,\n            userId\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Login utente\nconst loginUser = async (username, password)=>{\n    try {\n        // Trova l'utente\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').select('*').eq('username', username.toLowerCase()).single();\n        if (error || !user) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Verifica password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Username o password non corretti'\n            };\n        }\n        // Aggiorna last_login\n        await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_auth').update({\n            last_login: new Date().toISOString()\n        }).eq('id', user.id);\n        return {\n            success: true,\n            userId: user.id\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Errore interno del server'\n        };\n    }\n};\n// Verifica se l'utente è autenticato (controlla localStorage)\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const authData = localStorage.getItem('soul_auth');\n    if (!authData) return null;\n    try {\n        const { userId, timestamp } = JSON.parse(authData);\n        // Controlla se la sessione è scaduta (24 ore)\n        const now = Date.now();\n        const sessionAge = now - timestamp;\n        const maxAge = 24 * 60 * 60 * 1000 // 24 ore\n        ;\n        if (sessionAge > maxAge) {\n            localStorage.removeItem('soul_auth');\n            return null;\n        }\n        return userId;\n    } catch (e) {\n        localStorage.removeItem('soul_auth');\n        return null;\n    }\n};\n// Salva sessione utente\nconst saveUserSession = (userId)=>{\n    if (false) {}\n    const authData = {\n        userId,\n        timestamp: Date.now()\n    };\n    localStorage.setItem('soul_auth', JSON.stringify(authData));\n};\n// Logout utente\nconst logoutUser = ()=>{\n    if (false) {}\n    localStorage.removeItem('soul_auth');\n    localStorage.removeItem('soul_user_id'); // Rimuovi anche il vecchio ID\n};\n// Controlla se l'utente ha completato l'onboarding\nconst checkOnboardingStatus = async (userId)=>{\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').select('is_onboarded').eq('id', userId).single();\n        if (error) {\n            console.log('Error checking onboarding status:', error);\n            // Se la tabella non esiste o l'utente non esiste, ha bisogno di onboarding\n            return false;\n        }\n        if (!data) {\n            console.log('No profile data found for user:', userId);\n            return false;\n        }\n        console.log('Onboarding status for user:', userId, 'is_onboarded:', data.is_onboarded);\n        return data.is_onboarded || false;\n    } catch (error) {\n        console.error('Exception in checkOnboardingStatus:', error);\n        return false;\n    }\n};\n// Completa l'onboarding\nconst completeOnboarding = async (userId)=>{\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('user_profiles').update({\n            is_onboarded: true\n        }).eq('id', userId);\n        return !error;\n    } catch (e) {\n        return false;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/lib/auth.ts\n"));

/***/ })

});