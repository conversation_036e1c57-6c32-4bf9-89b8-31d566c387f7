"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { memory, addMessage, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        addMessage(userMessage, true);\n        try {\n            // Prepara la cronologia per l'API (incluso il nuovo messaggio utente)\n            const conversationHistory = [\n                ...memory.conversationHistory.map((msg)=>({\n                        role: msg.isUser ? 'user' : 'assistant',\n                        content: msg.content\n                    })),\n                {\n                    role: 'user',\n                    content: userMessage\n                }\n            ];\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_4__.sendChatMessage)(userMessage, conversationHistory, {\n                name: memory.name,\n                intimacyLevel: memory.intimacyLevel,\n                traits: memory.traits,\n                emotions: memory.emotions\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Ricominciare da capo?')) {\n            resetMemory();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Soul Chat\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: 'white',\n                                    fontSize: '1.5rem',\n                                    fontWeight: 'bold',\n                                    margin: 0\n                                },\n                                children: \"Soul Chat\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"chat-messages\",\n                        children: [\n                            memory.conversationHistory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '2rem',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Dimmi, come ti chiami e come ti senti oggi?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message.content,\n                                    isUser: message.isUser\n                                }, message.id, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: '1rem',\n                                    color: 'white'\n                                },\n                                children: \"Soul sta scrivendo...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi il tuo messaggio...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"8+J15u+KW8sagTEnFnMD6Ztr7wU=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});