import React from 'react';

interface ChatBubbleProps {
  message: string;
  isUser: boolean;
  timestamp?: Date;
  className?: string;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ 
  message, 
  isUser, 
  timestamp,
  className = '' 
}) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('it-IT', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} mb-4 ${className}`}>
      <div className={`chat-bubble ${isUser ? 'chat-bubble-user' : 'chat-bubble-ai'} relative hover:scale-105 transition-all duration-200`} style={{
        maxWidth: typeof window !== 'undefined' && window.innerWidth < 640 ? '85%' : '20rem'
      }}>
        {/* Contenuto del messaggio */}
        <p className="text-sm leading-relaxed whitespace-pre-wrap">
          {message}
        </p>
        
        {/* Timestamp (visibile al hover) */}
        {timestamp && (
          <div className={`
            absolute -bottom-6 text-xs text-white/50
            opacity-0 group-hover:opacity-100
            transition-opacity duration-200
            ${isUser ? 'right-0' : 'left-0'}
          `}>
            {formatTime(timestamp)}
          </div>
        )}
        
        {/* Indicatore di "pensiero" per l'AI */}
        {!isUser && (
          <div className="absolute -bottom-2 -left-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-white/30 rounded-full animate-bounce" 
                   style={{ animationDelay: '0ms' }} />
              <div className="w-2 h-2 bg-white/30 rounded-full animate-bounce" 
                   style={{ animationDelay: '150ms' }} />
              <div className="w-2 h-2 bg-white/30 rounded-full animate-bounce" 
                   style={{ animationDelay: '300ms' }} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatBubble;
