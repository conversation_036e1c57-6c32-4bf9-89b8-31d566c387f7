import React from 'react';

interface ChatBubbleProps {
  message: string;
  isUser: boolean;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message, isUser }) => {
  return (
    <div className={`message-container ${isUser ? 'user' : 'assistant'}`}>
      <div className={`message ${isUser ? 'user-message' : 'assistant-message'}`}>
        {message}
      </div>
    </div>
  );
};

export default ChatBubble;
