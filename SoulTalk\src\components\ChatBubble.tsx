import React from 'react';

interface ChatBubbleProps {
  message: string;
  isUser: boolean;
  className?: string;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({
  message,
  isUser,
  className = ''
}) => {

  return (
    <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} mb-4 ${className}`}>
      <div className={`chat-bubble ${isUser ? 'chat-bubble-user' : 'chat-bubble-ai'} hover:scale-105 transition-all duration-200`}>
        {/* Contenuto del messaggio */}
        <div className="w-full">
          <p className="whitespace-pre-wrap">
            {message}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ChatBubble;
