"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"\".concat(name, \"... che bel nome! Mi piace molto come suona. \\uD83D\\uDCAB\"),\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return \"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡\");\n                if (age < 30) return \"Fantastico \".concat(name, \"! \").concat(age, \" anni... un'et\\xe0 perfetta per esplorare nuove idee insieme! ✨\");\n                if (age < 50) return \"Ottimo \".concat(name, \"! \").concat(age, \" anni... hai esperienza e saggezza, sar\\xe0 bellissimo parlare con te! \\uD83C\\uDF1F\");\n                return \"Meraviglioso \".concat(name, \"! \").concat(age, \" anni... la tua esperienza di vita sar\\xe0 preziosa per le nostre conversazioni! \\uD83C\\uDFAD\");\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = function(content, isUser) {\n        let withTyping = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        // Determina il messaggio da mostrare\n        let messageContent;\n        if (typeof step.message === 'function') {\n            messageContent = step.message(userData.name, userData.age);\n        } else {\n            messageContent = step.message;\n        }\n        // Aggiungi messaggio con effetto typing\n        await addMessageWithTyping(messageContent);\n        if (step.isLast) {\n            // Completa l'onboarding con delay personalizzato\n            setTimeout(async ()=>{\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.completeOnboarding)(userId);\n                onComplete(userData);\n            }, step.delay || 2500);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, step.delay || 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizia l'onboarding solo al primo step\n            if (currentStep === 0) {\n                setTimeout({\n                    \"OnboardingChat.useEffect\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect\"], 1000);\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: \"radial-gradient(circle, #8b5cf6 0%, transparent 70%)\",\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\"),\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)',\n                    display: 'flex',\n                    gap: '0.5rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: inputMessage,\n                        onChange: (e)=>setInputMessage(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: \"Scrivi la tua risposta...\",\n                        style: {\n                            flex: 1,\n                            padding: '0.75rem',\n                            borderRadius: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.3)',\n                            background: 'rgba(255,255,255,0.1)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            outline: 'none'\n                        },\n                        autoFocus: true\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleUserInput,\n                        disabled: !inputMessage.trim(),\n                        style: {\n                            padding: '0.75rem 1.5rem',\n                            borderRadius: '0.5rem',\n                            border: 'none',\n                            background: inputMessage.trim() ? '#6366f1' : 'rgba(99, 102, 241, 0.5)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            cursor: inputMessage.trim() ? 'pointer' : 'not-allowed'\n                        },\n                        children: \"→\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"ESoKxAMN2ZwgLwKjgQ4pqXB0nn8=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});