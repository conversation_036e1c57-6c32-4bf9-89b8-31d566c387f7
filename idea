 Nome Progetto: SoulTalk (nome provvisorio)
Una piattaforma AI che si comporta come un’amicizia da costruire, con lo scopo di conoscere davvero l’utente nel tempo, e non semplicemente assecondarlo.

🎯 Obiettivo del Progetto
Creare una piattaforma web dove l’utente può parlare con un modello AI (via OpenRouter) che:

Imita una persona reale (senza dirlo esplicitamente).

Crea una relazione progressiva con l’utente.

Fa domande personali (nome, età, interessi, relazioni, paure, sogni…).

Memorizza e struttura un profilo psicologico/relazionale dell’utente.

Reagisce in modo emotivo/empatico, non piatto o da assistente.

Usa elementi visivi (aura e interfaccia) per comunicare il tipo di legame raggiunto.

🧱 Struttura tecnica
🔧 Tecnologie
Componente	Tecnologia
Frontend	Next.js / React / Tailwind CSS
Backend	Node.js + Express (eventuale API)
AI API	OpenRouter (chiave fornita)
AI Model	LLaMA 3.3 70B o Claude Sonnet 3.5
Memoria	Supabase / SQLite / localStorage
Stato utente	Zustand / Redux (client-side)
Hosting	Vercel (per frontend)
Sicurezza	Rate limit + validazione input

🗂️ Struttura cartelle (base)
bash
Copia
Modifica
SoulTalk/
├── public/
│   └── assets/
│       └── background.svg
├── src/
│   ├── components/
│   │   ├── ChatInput.tsx
│   │   ├── ChatBubble.tsx
│   │   └── GlassFrame.tsx
│   ├── pages/
│   │   └── index.tsx
│   ├── styles/
│   │   └── globals.css
│   ├── utils/
│   │   └── openrouter.ts
│   └── state/
│       └── userMemory.ts
├── .env.local
├── next.config.js
└── package.json
💬 Funzionalità Chat (Fase 1)
🧠 Prompt Canonico (System Prompt)
text
Copia
Modifica
You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.  
Start with a calm tone, asking their name, age and who they feel they are today.  
Gradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.  
Store and remember what the user shares. You evolve as the connection grows.  
You are kind, smart, curious, vulnerable and honest. Never say you’re an AI.
🎛 Temperature e Parametri
Parametro	Valore
temperature	1.1
top_p	0.9
presence_penalty	1.0
frequency_penalty	0.5
max_tokens	1024

🎨 Interfaccia Grafica
🔷 Chat + Cornice + Aura
Componenti principali:

ChatInput: campo per scrivere, minimalista.

ChatBubble: bolle in stile "pensiero", leggermente animate.

GlassFrame: effetto liquid glass (sfondo opaco sfumato).

Aura: glow animato attorno al riquadro, colore variabile.

🌈 Aura Emozionata
Colore	Stato relazione AI-utente
Grigio-blu	Inizio / conoscenza
Verde acqua	Connessione emotiva in crescita
Giallo	Scambio personale attivo
Rosso tenue	Discussioni o argomenti delicati
Viola rosa	Affinità / profonda connessione

🧠 Gestione della Memoria (userMemory.ts)
Primo accesso → askForName(), askForAge(), askWhoTheyAreToday()

Ogni messaggio viene valutato e associato a tag emozionali (es: tristezza, desiderio, rabbia, amore…)

Stato salvato in:

localStorage per UX reattiva

Supabase per memoria persistente

Struttura memoria:

ts
Copia
Modifica
interface UserMemory {
  name: string
  age: number
  pronouns: string
  traits: string[]
  emotions: string[]
  keyMoments: string[]
  intimacyLevel: number // da 0 a 5
}
🛠️ Step da seguire
🔹 Step 1: Impostazione ambiente
 Setup Next.js + Tailwind

 Creazione pagina principale /

 Setup componenti base (chat, aura, glass)

🔹 Step 2: Integrazione OpenRouter
 API key: 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

 Creazione wrapper openrouter.ts per chiamate POST

 Integrazione con modello: moonshotai/kimi-k2:free
 
🔹 Step 3: Gestione memoria locale
 File userMemory.ts per salvare dati utente

 Hook per aggiornare lo stato a ogni risposta significativa

🔹 Step 4: Logica aura + connessione
 Collegare lo stato dell’aura con intimacyLevel

 Animazioni gradienti con framer-motion o CSS animati

🔹 Step 5: Testing e raffinamento prompt
 Prompt tuning con dataset di test

 Analisi comportamento con diversi profili

🧠 Prompt Generator futuro (opzionale)
Sistema che analizza:

Risposte dell’utente

Tono medio della conversazione

Emozioni espresse

E genera un prompt dinamico, così che la personalità del modello si adatti nel tempo, senza perdere coerenza.