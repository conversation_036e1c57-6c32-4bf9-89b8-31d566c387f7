"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"\".concat(name, \"... che bel nome! Mi piace molto come suona. \\uD83D\\uDCAB\"),\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return \"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡\");\n                if (age < 30) return \"Fantastico \".concat(name, \"! \").concat(age, \" anni... un'et\\xe0 perfetta per esplorare nuove idee insieme! ✨\");\n                if (age < 50) return \"Ottimo \".concat(name, \"! \").concat(age, \" anni... hai esperienza e saggezza, sar\\xe0 bellissimo parlare con te! \\uD83C\\uDF1F\");\n                return \"Meraviglioso \".concat(name, \"! \").concat(age, \" anni... la tua esperienza di vita sar\\xe0 preziosa per le nostre conversazioni! \\uD83C\\uDFAD\");\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = function(content, isUser) {\n        let withTyping = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = async ()=>{\n        const step = onboardingSteps[currentStep];\n        // Determina il messaggio da mostrare\n        let messageContent;\n        if (typeof step.message === 'function') {\n            messageContent = step.message(userData.name, userData.age);\n        } else {\n            messageContent = step.message;\n        }\n        // Aggiungi messaggio con effetto typing\n        await addMessageWithTyping(messageContent);\n        if (step.isLast) {\n            // Completa l'onboarding con delay personalizzato\n            setTimeout(async ()=>{\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.completeOnboarding)(userId);\n                onComplete(userData);\n            }, step.delay || 2500);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, step.delay || 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizia l'onboarding solo al primo step\n            if (currentStep === 0) {\n                setTimeout({\n                    \"OnboardingChat.useEffect\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect\"], 1000);\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            if (currentStep > 0 && currentStep < onboardingSteps.length) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: \"radial-gradient(circle, #8b5cf6 0%, transparent 70%)\",\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\"),\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        message: message.content,\n                        isUser: message.isUser\n                    }, message.id, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)',\n                    display: 'flex',\n                    gap: '0.5rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        value: inputMessage,\n                        onChange: (e)=>setInputMessage(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: \"Scrivi la tua risposta...\",\n                        style: {\n                            flex: 1,\n                            padding: '0.75rem',\n                            borderRadius: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.3)',\n                            background: 'rgba(255,255,255,0.1)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            outline: 'none'\n                        },\n                        autoFocus: true\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleUserInput,\n                        disabled: !inputMessage.trim(),\n                        style: {\n                            padding: '0.75rem 1.5rem',\n                            borderRadius: '0.5rem',\n                            border: 'none',\n                            background: inputMessage.trim() ? '#6366f1' : 'rgba(99, 102, 241, 0.5)',\n                            color: 'white',\n                            fontSize: '1rem',\n                            cursor: inputMessage.trim() ? 'pointer' : 'not-allowed'\n                        },\n                        children: \"→\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"ESoKxAMN2ZwgLwKjgQ4pqXB0nn8=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});