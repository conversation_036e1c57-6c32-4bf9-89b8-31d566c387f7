"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/chat";
exports.ids = ["pages/api/chat"];
exports.modules = {

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   handler: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/api-utils */ \"(api-node)/./node_modules/next/dist/server/api-utils/index.js\");\n/* harmony import */ var next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./src\\pages\\api\\chat.ts */ \"(api-node)/./src/pages/api/chat.ts\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(api-node)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(api-node)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n// Import the userland code.\n\n\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_3__.hoist)(_src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_4__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_3__.hoist)(_src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_4__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_2__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/chat\",\n        pathname: \"/api/chat\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_chat_ts__WEBPACK_IMPORTED_MODULE_4__,\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    let srcPage = \"/api/chat\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {}\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return;\n    }\n    const { query, params, prerenderManifest } = prepareResult;\n    try {\n        const method = req.method || 'GET';\n        const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__.getTracer)();\n        const activeSpan = tracer.getActiveScopeSpan();\n        const onRequestError = routeModule.instrumentationOnRequestError.bind(routeModule);\n        const invokeRouteModule = async (span)=>routeModule.render(req, res, {\n                query: {\n                    ...query,\n                    ...params\n                },\n                params,\n                allowedRevalidateHeaderKeys: undefined,\n                multiZoneDraftMode: Boolean(\"false\"),\n                trustHostHeader: undefined,\n                // TODO: get this from from runtime env so manifest\n                // doesn't need to load\n                previewProps: prerenderManifest.preview,\n                propagateError: false,\n                dev: routeModule.isDev,\n                page: \"/api/chat\",\n                projectDir:  false || '',\n                onError: (...args)=>onRequestError(req, ...args)\n            }).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await invokeRouteModule(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_6__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_5__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, invokeRouteModule));\n        }\n    } catch (err) {\n        // we re-throw in dev to show the error overlay\n        if (routeModule.isDev) {\n            throw err;\n        }\n        // this is technically an invariant as error handling\n        // should be done inside of api-resolver onError\n        (0,next_dist_server_api_utils__WEBPACK_IMPORTED_MODULE_0__.sendError)(res, 500, 'Internal Server Error');\n    } finally{\n        // We don't allow any waitUntil work in pages API routes currently\n        // so if callback is present return with resolved promise since no\n        // pending work\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n    }\n}\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./src/pages/api/chat.ts":
/*!*******************************!*\
  !*** ./src/pages/api/chat.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\nconst OPENROUTER_API_KEY = '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';\nconst SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\n\nStart with a calm tone, asking their name, age and who they feel they are today.\n\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\n\nStore and remember what the user shares. You evolve as the connection grows.\n\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed',\n            message: ''\n        });\n    }\n    try {\n        const { messages, userContext } = req.body;\n        if (!messages || !Array.isArray(messages)) {\n            return res.status(400).json({\n                error: 'Invalid messages format',\n                message: ''\n            });\n        }\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += `\\n- Name: ${userContext.name}`;\n            }\n            if (userContext.age) {\n                contextualPrompt += `\\n- Age: ${userContext.age}`;\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += `\\n- Personality traits: ${userContext.traits.join(', ')}`;\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += `\\n- Recent emotions: ${userContext.emotions.join(', ')}`;\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += `\\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const requestBody = {\n            model: 'moonshotai/kimi-k2:free',\n            messages: [\n                systemMessage,\n                ...messages\n            ],\n            temperature: 1.1,\n            top_p: 0.9,\n            presence_penalty: 1.0,\n            frequency_penalty: 0.5,\n            max_tokens: 1024\n        };\n        const response = await fetch(OPENROUTER_API_URL, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,\n                'Content-Type': 'application/json',\n                'HTTP-Referer': 'https://soultalk.app',\n                'X-Title': 'SoulTalk'\n            },\n            body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);\n        }\n        const data = await response.json();\n        if (!data.choices || data.choices.length === 0) {\n            throw new Error('Nessuna risposta ricevuta da OpenRouter');\n        }\n        const aiMessage = data.choices[0].message.content;\n        res.status(200).json({\n            message: aiMessage\n        });\n    } catch (error) {\n        console.error('Errore nella chiamata a OpenRouter:', error);\n        res.status(500).json({\n            error: 'Errore interno del server',\n            message: 'Mi dispiace, c\\'è stato un problema. Riprova tra poco.'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./src/pages/api/chat.ts\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cchat.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();