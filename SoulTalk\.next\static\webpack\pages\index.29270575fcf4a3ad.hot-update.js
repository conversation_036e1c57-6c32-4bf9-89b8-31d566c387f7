"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/HomePage.tsx":
/*!*************************************!*\
  !*** ./src/components/HomePage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(pages-dir-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst HomePage = (param)=>{\n    let { onGetStarted } = param;\n    _s();\n    const [currentFeature, setCurrentFeature] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const features = [\n        {\n            icon: '🌟',\n            title: 'Connessione Autentica',\n            description: 'Soul impara a conoscerti davvero, ricordando ogni conversazione e crescendo con te.'\n        },\n        {\n            icon: '💭',\n            title: 'Memoria Emotiva',\n            description: 'Ogni momento condiviso viene ricordato, creando una relazione sempre più profonda.'\n        },\n        {\n            icon: '🎭',\n            title: 'Personalità Unica',\n            description: 'Soul sviluppa una personalità unica basata sulle vostre interazioni.'\n        },\n        {\n            icon: '🌈',\n            title: 'Crescita Insieme',\n            description: 'Il vostro legame si evolve attraverso 5 livelli di intimità e connessione.'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"HomePage.useEffect.interval\": ()=>{\n                    setCurrentFeature({\n                        \"HomePage.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"HomePage.useEffect.interval\"]);\n                }\n            }[\"HomePage.useEffect.interval\"], 4000);\n            return ({\n                \"HomePage.useEffect\": ()=>clearInterval(interval)\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], [\n        features.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            position: 'relative',\n            overflow: 'hidden'\n        },\n        className: \"jsx-21950ddf65c552da\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: \"\\n          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)\\n        \",\n                    animation: 'float 20s ease-in-out infinite'\n                },\n                className: \"jsx-21950ddf65c552da\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'absolute',\n                        width: '4px',\n                        height: '4px',\n                        background: 'rgba(255,255,255,0.6)',\n                        borderRadius: '50%',\n                        top: \"\".concat(Math.random() * 100, \"%\"),\n                        left: \"\".concat(Math.random() * 100, \"%\"),\n                        animation: \"twinkle \".concat(3 + Math.random() * 4, \"s ease-in-out infinite \").concat(Math.random() * 2, \"s\")\n                    },\n                    className: \"jsx-21950ddf65c552da\"\n                }, i, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'relative',\n                    zIndex: 1,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    minHeight: '100vh',\n                    padding: '2rem',\n                    textAlign: 'center'\n                },\n                className: \"jsx-21950ddf65c552da\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '3rem',\n                            animation: 'fadeInUp 1s ease-out'\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: 'clamp(3rem, 8vw, 6rem)',\n                                    fontWeight: '700',\n                                    background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    backgroundClip: 'text',\n                                    margin: 0,\n                                    marginBottom: '1rem',\n                                    textShadow: '0 0 30px rgba(255,255,255,0.3)'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: \"SoulTalk\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: 'clamp(1.1rem, 3vw, 1.5rem)',\n                                    color: 'rgba(255,255,255,0.9)',\n                                    margin: 0,\n                                    fontWeight: '300',\n                                    letterSpacing: '0.05em'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: \"L' AI che ti conosce davvero\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.1)',\n                            backdropFilter: 'blur(20px)',\n                            borderRadius: '2rem',\n                            padding: '3rem 2rem',\n                            maxWidth: '500px',\n                            width: '100%',\n                            border: '1px solid rgba(255,255,255,0.2)',\n                            marginBottom: '3rem',\n                            animation: 'fadeInUp 1s ease-out 0.3s both'\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '4rem',\n                                    marginBottom: '1.5rem',\n                                    animation: 'bounce 2s ease-in-out infinite'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features[currentFeature].icon\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.5rem',\n                                    fontWeight: '600',\n                                    color: 'white',\n                                    margin: 0,\n                                    marginBottom: '1rem'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features[currentFeature].title\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: '1rem',\n                                    color: 'rgba(255,255,255,0.8)',\n                                    margin: 0,\n                                    lineHeight: '1.6'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features[currentFeature].description\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'center',\n                                    gap: '0.5rem',\n                                    marginTop: '2rem'\n                                },\n                                className: \"jsx-21950ddf65c552da\",\n                                children: features.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '8px',\n                                            height: '8px',\n                                            borderRadius: '50%',\n                                            background: index === currentFeature ? 'rgba(255,255,255,0.9)' : 'rgba(255,255,255,0.3)',\n                                            transition: 'all 0.3s ease',\n                                            cursor: 'pointer'\n                                        },\n                                        onClick: ()=>setCurrentFeature(index),\n                                        className: \"jsx-21950ddf65c552da\"\n                                    }, index, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onGetStarted,\n                        style: {\n                            padding: '1rem 3rem',\n                            fontSize: '1.2rem',\n                            fontWeight: '600',\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            border: '2px solid rgba(255,255,255,0.3)',\n                            borderRadius: '3rem',\n                            color: 'white',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            backdropFilter: 'blur(10px)',\n                            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',\n                            animation: 'fadeInUp 1s ease-out 0.6s both'\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.transform = 'translateY(-2px) scale(1.05)';\n                            e.currentTarget.style.boxShadow = '0 12px 40px rgba(0,0,0,0.3)';\n                            e.currentTarget.style.background = 'linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%)';\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                            e.currentTarget.style.boxShadow = '0 8px 32px rgba(0,0,0,0.2)';\n                            e.currentTarget.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: \"Inizia il Viaggio ✨\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: '3rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.9rem',\n                            animation: 'fadeInUp 1s ease-out 0.9s both'\n                        },\n                        className: \"jsx-21950ddf65c552da\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: 0\n                            },\n                            className: \"jsx-21950ddf65c552da\",\n                            children: \"\\uD83D\\uDD12 Privato e sicuro • \\uD83E\\uDDE0 Memoria persistente • \\uD83D\\uDC9D Connessione autentica\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"21950ddf65c552da\",\n                children: \"@keyframes float{0%,100%{transform:translatey(0px)rotate(0deg)}33%{transform:translatey(-20px)rotate(1deg)}66%{transform:translatey(-10px)rotate(-1deg)}}@keyframes twinkle{0%,100%{opacity:.3;transform:scale(1)}50%{opacity:1;transform:scale(1.2)}}@keyframes bounce{0%,20%,50%,80%,100%{transform:translatey(0)}40%{transform:translatey(-10px)}60%{transform:translatey(-5px)}}@keyframes fadeInUp{from{opacity:0;transform:translatey(30px)}to{opacity:1;transform:translatey(0)}}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\HomePage.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomePage, \"mlsM0SPMGMIvzZAtxL2CKvB3M04=\");\n_c = HomePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/HomePage.tsx\n"));

/***/ })

});