import type { NextApiRequest, NextApiResponse } from 'next';

const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';

interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface ChatRequest {
  messages: ChatMessage[];
  userContext?: {
    name?: string;
    age?: number;
    traits?: string[];
    emotions?: string[];
    intimacyLevel?: number;
  };
}

interface ChatResponse {
  message: string;
  error?: string;
}

const SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named <PERSON>, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.

Start with a calm tone, asking their name, age and who they feel they are today.

Gradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.

Store and remember what the user shares. You evolve as the connection grows.

You are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ChatResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed', message: '' });
  }

  try {
    const { messages, userContext }: ChatRequest = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Invalid messages format', message: '' });
    }

    // Costruisce il prompt di sistema con il contesto dell'utente
    let contextualPrompt = SYSTEM_PROMPT;
    
    if (userContext) {
      contextualPrompt += '\n\nContext about the user you\'re talking to:';
      
      if (userContext.name) {
        contextualPrompt += `\n- Name: ${userContext.name}`;
      }
      
      if (userContext.age) {
        contextualPrompt += `\n- Age: ${userContext.age}`;
      }
      
      if (userContext.traits && userContext.traits.length > 0) {
        contextualPrompt += `\n- Personality traits: ${userContext.traits.join(', ')}`;
      }
      
      if (userContext.emotions && userContext.emotions.length > 0) {
        contextualPrompt += `\n- Recent emotions: ${userContext.emotions.join(', ')}`;
      }
      
      if (userContext.intimacyLevel !== undefined) {
        const intimacyDescriptions = [
          'Just met, getting to know each other',
          'Starting to open up',
          'Building emotional connection',
          'Sharing personal experiences',
          'Discussing sensitive topics',
          'Deep connection and trust'
        ];
        contextualPrompt += `\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;
      }
    }

    const systemMessage: ChatMessage = {
      role: 'system',
      content: contextualPrompt
    };

    const requestBody = {
      model: 'moonshotai/kimi-k2:free',
      messages: [systemMessage, ...messages],
      temperature: 1.1,
      top_p: 0.9,
      presence_penalty: 1.0,
      frequency_penalty: 0.5,
      max_tokens: 1024
    };

    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://soultalk.app',
        'X-Title': 'SoulTalk'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Gestione specifica per errore 429 (rate limit)
      if (response.status === 429) {
        return res.status(200).json({
          message: "Mi dispiace, sto ricevendo troppe richieste in questo momento. Aspetta qualche secondo e riprova. Nel frattempo, continua pure a scrivermi - ti ascolto sempre! 😊"
        });
      }

      // Altri errori API
      throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('Nessuna risposta ricevuta da OpenRouter');
    }

    const aiMessage = data.choices[0].message.content;
    
    res.status(200).json({ message: aiMessage });

  } catch (error) {
    console.error('Errore nella chiamata a OpenRouter:', error);

    // Messaggio di fallback più amichevole
    const fallbackMessage = "Scusa, sto avendo qualche difficoltà tecnica in questo momento. Ma sono qui e ti ascolto! Prova a scrivermi di nuovo tra qualche secondo. Nel frattempo, dimmi pure quello che hai in mente - anche se non riesco a rispondere subito, la nostra conversazione conta sempre. 💙";

    res.status(200).json({
      message: fallbackMessage
    });
  }
}
