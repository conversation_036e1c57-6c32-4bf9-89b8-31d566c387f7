"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { memory, addMessage, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory,\n        isLoading\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente e ottieni la memoria aggiornata\n        const updatedMemoryWithUserMessage = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, cronologia:', updatedMemoryWithUserMessage.conversationHistory.length);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Usa la cronologia aggiornata che include già il messaggio dell'utente\n            const conversationHistory = updatedMemoryWithUserMessage.conversationHistory.map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_4__.sendChatMessage)(userMessage, conversationHistory, {\n                name: updatedMemoryWithUserMessage.name,\n                intimacyLevel: updatedMemoryWithUserMessage.intimacyLevel,\n                traits: updatedMemoryWithUserMessage.traits,\n                emotions: updatedMemoryWithUserMessage.emotions\n            });\n            // Aggiungi la risposta dell'AI\n            const finalMemory = addMessage(response, false);\n            console.log('Risposta AI aggiunta, cronologia finale:', finalMemory.conversationHistory.length);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Ricominciare da capo?')) {\n            resetMemory();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Soul Chat\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: 'white',\n                                    fontSize: '1.5rem',\n                                    fontWeight: 'bold',\n                                    margin: 0\n                                },\n                                children: \"Soul Chat\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"chat-messages\",\n                        children: [\n                            memory.conversationHistory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '2rem',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Dimmi, come ti chiami e come ti senti oggi?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message.content,\n                                    isUser: message.isUser\n                                }, message.id, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: '1rem',\n                                    color: 'white'\n                                },\n                                children: \"Soul sta scrivendo...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi il tuo messaggio...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"8+J15u+KW8sagTEnFnMD6Ztr7wU=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_5__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});