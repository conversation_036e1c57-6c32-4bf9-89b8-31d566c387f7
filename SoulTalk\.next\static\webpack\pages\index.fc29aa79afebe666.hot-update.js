"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-browser)/./src/lib/database.ts\");\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Inizializza solo lato client\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            setIsClient(true);\n            // Genera userId solo lato client\n            if (true) {\n                setUserId(generateUserId());\n            }\n        }\n    }[\"useSupabaseMemory.useEffect\"], []);\n    // Carica i dati iniziali solo quando siamo lato client e abbiamo un userId\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId) return;\n            const loadData = {\n                \"useSupabaseMemory.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Carica profilo utente\n                        const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(userId);\n                        if (profile) {\n                            setUserMemory({\n                                name: profile.name || '',\n                                age: profile.age || 0,\n                                traits: profile.traits || [],\n                                emotions: profile.emotions || [],\n                                intimacyLevel: profile.intimacy_level || 0,\n                                keyMoments: profile.key_moments || []\n                            });\n                        }\n                        // Carica messaggi\n                        const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(userId);\n                        const formattedMessages = chatMessages.map({\n                            \"useSupabaseMemory.useEffect.loadData.formattedMessages\": (msg)=>({\n                                    id: msg.id,\n                                    content: msg.content,\n                                    isUser: msg.is_user,\n                                    timestamp: new Date(msg.timestamp)\n                                })\n                        }[\"useSupabaseMemory.useEffect.loadData.formattedMessages\"]);\n                        setMessages(formattedMessages);\n                    } catch (error) {\n                        console.error('Error loading data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseMemory.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId\n    ]);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || isLoading || !userMemory.name) return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: \"msg_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se siamo lato client e abbiamo userId\n            if (isClient && userId) {\n                setIsSaving(true);\n                try {\n                    const chatMessage = {\n                        id: newMessage.id,\n                        user_id: userId,\n                        content: newMessage.content,\n                        is_user: newMessage.isUser,\n                        timestamp: newMessage.timestamp.toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.saveChatMessage)(chatMessage);\n                } catch (error) {\n                    console.error('Error saving message:', error);\n                } finally{\n                    setIsSaving(false);\n                }\n            }\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        isClient,\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!isClient || !userId) return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                // Reset local state\n                setMessages([]);\n                setUserMemory({\n                    name: '',\n                    age: 0,\n                    traits: [],\n                    emotions: [],\n                    intimacyLevel: 0,\n                    keyMoments: []\n                });\n                // Genera nuovo user ID\n                if (true) {\n                    localStorage.removeItem('soul_user_id');\n                    window.location.reload();\n                }\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        isClient,\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        addMessage,\n        updateUserMemory,\n        resetMemory\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\n"));

/***/ })

});