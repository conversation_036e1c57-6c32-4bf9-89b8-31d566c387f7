import React from 'react';

interface AuraIndicatorProps {
  intimacyLevel: number;
  className?: string;
  showLabel?: boolean;
}

const AuraIndicator: React.FC<AuraIndicatorProps> = ({ 
  intimacyLevel, 
  className = '',
  showLabel = false 
}) => {
  // Mappatura dei livelli di intimità con colori e descrizioni
  const getAuraInfo = (level: number) => {
    const auraMap = {
      0: {
        color: 'gray-blue',
        bgColor: 'bg-gradient-to-r from-gray-400 to-blue-400',
        shadowColor: 'shadow-blue-500/30',
        glowColor: 'drop-shadow-[0_0_20px_rgba(59,130,246,0.3)]',
        label: 'Primo Incontro',
        description: 'Conoscenza iniziale'
      },
      1: {
        color: 'gray-blue',
        bgColor: 'bg-gradient-to-r from-slate-400 to-blue-500',
        shadowColor: 'shadow-blue-500/40',
        glowColor: 'drop-shadow-[0_0_25px_rgba(59,130,246,0.4)]',
        label: 'Apertura',
        description: 'Iniziando ad aprirsi'
      },
      2: {
        color: 'teal',
        bgColor: 'bg-gradient-to-r from-teal-400 to-cyan-500',
        shadowColor: 'shadow-teal-500/40',
        glowColor: 'drop-shadow-[0_0_25px_rgba(20,184,166,0.4)]',
        label: 'Connessione',
        description: 'Connessione emotiva in crescita'
      },
      3: {
        color: 'yellow',
        bgColor: 'bg-gradient-to-r from-yellow-400 to-amber-500',
        shadowColor: 'shadow-yellow-500/40',
        glowColor: 'drop-shadow-[0_0_25px_rgba(245,158,11,0.4)]',
        label: 'Condivisione',
        description: 'Scambio personale attivo'
      },
      4: {
        color: 'red',
        bgColor: 'bg-gradient-to-r from-red-400 to-pink-500',
        shadowColor: 'shadow-red-500/30',
        glowColor: 'drop-shadow-[0_0_25px_rgba(239,68,68,0.3)]',
        label: 'Profondità',
        description: 'Discussioni delicate'
      },
      5: {
        color: 'purple-pink',
        bgColor: 'bg-gradient-to-r from-purple-500 to-pink-500',
        shadowColor: 'shadow-purple-500/40',
        glowColor: 'drop-shadow-[0_0_30px_rgba(139,92,246,0.4)]',
        label: 'Affinità',
        description: 'Profonda connessione'
      }
    };

    return auraMap[level as keyof typeof auraMap] || auraMap[0];
  };

  const auraInfo = getAuraInfo(intimacyLevel);

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Indicatore visuale dell'aura */}
      <div className="relative">
        {/* Glow esterno */}
        <div 
          className={`
            absolute inset-0 rounded-full
            ${auraInfo.shadowColor}
            ${auraInfo.glowColor}
            animate-pulse-slow
            blur-sm
          `}
          style={{
            width: '24px',
            height: '24px',
            transform: 'scale(1.5)',
          }}
        />
        
        {/* Cerchio principale */}
        <div 
          className={`
            relative w-6 h-6 rounded-full
            ${auraInfo.bgColor}
            ${auraInfo.shadowColor}
            shadow-lg
            transition-all duration-500
          `}
        />
        
        {/* Pulsazione interna */}
        <div 
          className={`
            absolute inset-1 rounded-full
            bg-white/20
            animate-ping
          `}
          style={{
            animationDuration: '2s',
            animationIterationCount: 'infinite',
          }}
        />
      </div>

      {/* Label e descrizione (opzionale) */}
      {showLabel && (
        <div className="flex flex-col">
          <span className="text-sm font-medium text-white">
            {auraInfo.label}
          </span>
          <span className="text-xs text-white/60">
            {auraInfo.description}
          </span>
        </div>
      )}
    </div>
  );
};

// Componente per mostrare la progressione dell'aura
export const AuraProgression: React.FC<{ intimacyLevel: number }> = ({ intimacyLevel }) => {
  const levels = [0, 1, 2, 3, 4, 5];
  
  return (
    <div className="flex items-center space-x-2">
      {levels.map((level) => (
        <div key={level} className="relative">
          <AuraIndicator 
            intimacyLevel={level}
            className={`
              transition-all duration-300
              ${level <= intimacyLevel ? 'opacity-100 scale-100' : 'opacity-30 scale-75'}
            `}
          />
          {level === intimacyLevel && (
            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
              <div className="w-2 h-2 bg-white rounded-full animate-bounce" />
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Hook per ottenere informazioni sull'aura
export const useAuraInfo = (intimacyLevel: number) => {
  const getAuraColor = () => {
    const colorMap = {
      0: 'gray-blue',
      1: 'gray-blue', 
      2: 'teal',
      3: 'yellow',
      4: 'red',
      5: 'purple-pink'
    };
    return colorMap[intimacyLevel as keyof typeof colorMap] || 'gray-blue';
  };

  const getAuraDescription = () => {
    const descriptions = {
      0: 'Primo incontro - Conoscenza iniziale',
      1: 'Apertura - Iniziando ad aprirsi',
      2: 'Connessione - Connessione emotiva in crescita',
      3: 'Condivisione - Scambio personale attivo',
      4: 'Profondità - Discussioni delicate',
      5: 'Affinità - Profonda connessione'
    };
    return descriptions[intimacyLevel as keyof typeof descriptions] || descriptions[0];
  };

  const getNextLevelDescription = () => {
    if (intimacyLevel >= 5) return 'Livello massimo raggiunto';
    
    const nextDescriptions = {
      0: 'Continua a parlare per approfondire la conoscenza',
      1: 'Condividi qualcosa di personale per crescere insieme',
      2: 'Apri il cuore per rafforzare la connessione',
      3: 'Esplora argomenti più profondi',
      4: 'Condividi i tuoi pensieri più intimi',
    };
    return nextDescriptions[intimacyLevel as keyof typeof nextDescriptions] || '';
  };

  return {
    color: getAuraColor(),
    description: getAuraDescription(),
    nextLevelDescription: getNextLevelDescription(),
    isMaxLevel: intimacyLevel >= 5,
    progress: (intimacyLevel / 5) * 100
  };
};

export default AuraIndicator;
