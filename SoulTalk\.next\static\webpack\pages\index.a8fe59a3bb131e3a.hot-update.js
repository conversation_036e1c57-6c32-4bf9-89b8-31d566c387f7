"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _components_AuthForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuthForm */ \"(pages-dir-browser)/./src/components/AuthForm.tsx\");\n/* harmony import */ var _components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/OnboardingChat */ \"(pages-dir-browser)/./src/components/OnboardingChat.tsx\");\n/* harmony import */ var _components_ProfileCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ProfileCard */ \"(pages-dir-browser)/./src/components/ProfileCard.tsx\");\n/* harmony import */ var _components_HomePage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/HomePage */ \"(pages-dir-browser)/./src/components/HomePage.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n/* harmony import */ var _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/useSupabaseMemory */ \"(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoadingAI, setIsLoadingAI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showProfile, setShowProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHomePage, setShowHomePage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Usa il hook Supabase per gestire i dati\n    const { userId, messages, userMemory, isLoading: isLoadingData, isSaving, isAuthenticated, needsOnboarding, addMessage, updateUserMemory, resetMemory, authenticateUser, logout, completeOnboardingWithData } = (0,_hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_11__.useSupabaseMemory)();\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    // Funzione per determinare il colore dell'aura basato sul livello di intimità\n    const getAuraColor = ()=>{\n        const colors = [\n            '#6366f1',\n            '#8b5cf6',\n            '#a855f7',\n            '#ec4899',\n            '#f43f5e' // Rose - Intimo\n        ];\n        return colors[userMemory.intimacyLevel] || colors[0];\n    };\n    // Funzione per generare lo sfondo dinamico basato sul livello di intimità\n    const getDynamicBackground = ()=>{\n        const intimacyLevel = userMemory.intimacyLevel;\n        const backgroundConfigs = [\n            // Livello 0 - Sconosciuto: Blu freddi e neutri\n            {\n                colors: [\n                    'rgba(99, 102, 241, 0.25)',\n                    'rgba(139, 92, 246, 0.2)',\n                    'rgba(168, 85, 247, 0.15)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'\n            },\n            // Livello 1 - Conoscente: Viola più caldi\n            {\n                colors: [\n                    'rgba(139, 92, 246, 0.3)',\n                    'rgba(168, 85, 247, 0.25)',\n                    'rgba(236, 72, 153, 0.2)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e3a8a 100%)'\n            },\n            // Livello 2 - Amico: Mix viola-rosa\n            {\n                colors: [\n                    'rgba(168, 85, 247, 0.35)',\n                    'rgba(236, 72, 153, 0.3)',\n                    'rgba(244, 63, 94, 0.25)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #581c87 0%, #7c2d92 50%, #be185d 100%)'\n            },\n            // Livello 3 - Amico stretto: Rosa intensi\n            {\n                colors: [\n                    'rgba(236, 72, 153, 0.4)',\n                    'rgba(244, 63, 94, 0.35)',\n                    'rgba(251, 113, 133, 0.3)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%)'\n            },\n            // Livello 4 - Intimo: Rosa caldi e dorati\n            {\n                colors: [\n                    'rgba(244, 63, 94, 0.45)',\n                    'rgba(251, 113, 133, 0.4)',\n                    'rgba(252, 165, 165, 0.35)'\n                ],\n                baseGradient: 'linear-gradient(135deg, #e11d48 0%, #f43f5e 50%, #fb7185 100%)'\n            }\n        ];\n        const config = backgroundConfigs[intimacyLevel] || backgroundConfigs[0];\n        return {\n            background: \"\\n        radial-gradient(circle at 20% 80%, \".concat(config.colors[0], \" 0%, transparent 50%),\\n        radial-gradient(circle at 80% 20%, \").concat(config.colors[1], \" 0%, transparent 50%),\\n        radial-gradient(circle at 40% 40%, \").concat(config.colors[2], \" 0%, transparent 50%),\\n        \").concat(config.baseGradient, \"\\n      \"),\n            backgroundSize: '200% 200%, 200% 200%, 200% 200%, 100% 100%',\n            backgroundAttachment: 'fixed',\n            animation: 'liquidMove 30s cubic-bezier(0.4, 0, 0.2, 1) infinite',\n            transition: 'all 2s cubic-bezier(0.4, 0, 0.2, 1)'\n        };\n    };\n    // Analizza i messaggi e aggiorna la memoria utente\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        const lowerUserMessage = userMessage.toLowerCase();\n        const updates = {};\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !userMemory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updates.name = name;\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !userMemory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updates.age = age;\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                if (!userMemory.emotions.includes(emotion)) {\n                    updates.emotions = [\n                        ...userMemory.emotions,\n                        emotion\n                    ];\n                }\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore') || lowerUserMessage.includes('problema') || lowerUserMessage.includes('difficoltà') || lowerUserMessage.includes('paura') || lowerUserMessage.includes('sogno')) {\n            const newLevel = Math.min(userMemory.intimacyLevel + 1, 4);\n            // Se il livello è cambiato, mostra un feedback visivo\n            if (newLevel > userMemory.intimacyLevel) {\n                console.log(\"\\uD83C\\uDF1F Livello di connessione aumentato: \".concat(newLevel, \"/4\"));\n                updates.intimacyLevel = newLevel;\n            }\n        }\n        // Applica tutti gli aggiornamenti\n        if (Object.keys(updates).length > 0) {\n            updateUserMemory(updates);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoadingAI\n    ]);\n    // Nascondi il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (messages.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        messages\n    ]);\n    // Aggiorna lo sfondo dinamicamente in base al livello di intimità\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const backgroundStyle = getDynamicBackground();\n            const body = document.body;\n            // Applica le proprietà di sfondo con transizione fluida\n            Object.assign(body.style, backgroundStyle);\n            // Aggiungi una classe per indicare il livello corrente\n            body.className = \"intimacy-level-\".concat(userMemory.intimacyLevel);\n            // Cleanup function per ripristinare lo sfondo originale se necessario\n            return ({\n                \"Home.useEffect\": ()=>{\n                // Non facciamo cleanup per mantenere l'effetto\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        userMemory.intimacyLevel\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoadingAI) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoadingAI(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = await addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API (usa i messaggi attuali + il nuovo)\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_10__.sendChatMessage)(userMessage, conversationHistory, {\n                name: userMemory.name || undefined,\n                age: userMemory.age || undefined,\n                traits: userMemory.traits,\n                emotions: userMemory.emotions,\n                intimacyLevel: userMemory.intimacyLevel\n            });\n            // Aggiungi la risposta dell'AI\n            await addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n            // Analizza il messaggio per aggiornare la memoria\n            await analyzeAndUpdateMemory(userMessage, response);\n        } catch (error) {\n            console.error('Error:', error);\n            await addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoadingAI(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = async ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi e messaggi andranno persi dal database.')) {\n            await resetMemory();\n            setShowWelcome(true);\n        }\n    };\n    // Se mostra la HomePage, mostrala prima del login\n    if (showHomePage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - By Agtechdesigne\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"SoulTalk - L' AI che ti conosce davvero. Connessione autentica, memoria emotiva, crescita insieme.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomePage__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    onGetStarted: ()=>setShowHomePage(false)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se non è autenticato, mostra il form di login\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Accedi\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Accedi a SoulTalk per continuare la tua conversazione con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onAuthSuccess: authenticateUser\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Mostra loading solo quando stiamo caricando i dati dopo l'autenticazione\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Caricamento...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.9)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'white',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Caricando i tuoi dati...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // Se ha bisogno di onboarding, mostra la chat di configurazione\n    if (needsOnboarding) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - Configurazione\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Configura il tuo profilo per iniziare con Soul\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OnboardingChat__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    userId: userId,\n                    onComplete: completeOnboardingWithData\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    (isLoadingData || !userId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'absolute',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0,0,0,0.8)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            zIndex: 1000\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: 'white',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: !userId ? 'Inizializzando...' : 'Caricando i tuoi ricordi...'\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowProfile(true),\n                        style: {\n                            position: 'absolute',\n                            top: '0.4rem',\n                            right: '1rem',\n                            zIndex: 10,\n                            width: '45px',\n                            height: '45px',\n                            borderRadius: '50%',\n                            background: 'rgba(255,255,255,0.1)',\n                            backdropFilter: 'blur(10px)',\n                            border: '1px solid rgba(255,255,255,0.2)',\n                            color: 'white',\n                            fontSize: '1.1rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            transition: 'all 0.3s ease',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.background = 'rgba(255,255,255,0.2)';\n                            e.currentTarget.style.transform = 'scale(1.05)';\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.background = 'rgba(255,255,255,0.1)';\n                            e.currentTarget.style.transform = 'scale(1)';\n                        },\n                        children: userMemory.name ? userMemory.name.charAt(0).toUpperCase() : '👤'\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: '38px',\n                                                    height: '38px',\n                                                    borderRadius: '50%',\n                                                    background: \"radial-gradient(circle, \".concat(getAuraColor(), \" 0%, transparent 70%)\"),\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '1.3rem',\n                                                    animation: 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'\n                                                },\n                                                children: \"\\uD83C\\uDF1F\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        style: {\n                                                            color: 'white',\n                                                            fontSize: '1.3rem',\n                                                            fontWeight: 'bold',\n                                                            margin: 0\n                                                        },\n                                                        children: \"Soul\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.7)',\n                                                            fontSize: '0.65rem',\n                                                            margin: 0\n                                                        },\n                                                        children: \"BY Agtechdesigne.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_5__.AuraProgression, {\n                                        intimacyLevel: userMemory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '1rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        style: {\n                                            color: 'rgba(255,255,255,0.8)',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleReset,\n                                        style: {\n                                            color: 'white',\n                                            background: 'none',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            fontSize: '0.875rem'\n                                        },\n                                        children: \"Reset\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '0 1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"h-full flex flex-col\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: chatContainerRef,\n                                className: \"chat-messages\",\n                                style: {\n                                    flex: 1,\n                                    overflowY: 'auto',\n                                    padding: '1rem'\n                                },\n                                children: [\n                                    showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: 'center',\n                                            padding: '1.5rem 0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                maxWidth: '22rem',\n                                                margin: '0 auto',\n                                                padding: '1.25rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255, 255, 255, 0.2)'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    style: {\n                                                        color: 'white',\n                                                        fontSize: '1.125rem',\n                                                        fontWeight: '600',\n                                                        marginBottom: '0.625rem',\n                                                        margin: 0\n                                                    },\n                                                    children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.8)',\n                                                        fontSize: '0.75rem',\n                                                        lineHeight: '1.4',\n                                                        margin: 0\n                                                    },\n                                                    children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 17\n                                    }, this),\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            message: message.content,\n                                            isUser: message.isUser\n                                        }, message.id, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)),\n                                    isLoadingAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'flex-start'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect\",\n                                            style: {\n                                                padding: '0.625rem 0.875rem',\n                                                borderRadius: '0.875rem',\n                                                background: 'rgba(255, 255, 255, 0.1)',\n                                                backdropFilter: 'blur(10px)'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '0.375rem',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            gap: '0.2rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.1s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '5px',\n                                                                    height: '5px',\n                                                                    backgroundColor: 'white',\n                                                                    borderRadius: '50%',\n                                                                    animation: 'bounce 1s infinite',\n                                                                    animationDelay: '0.2s',\n                                                                    opacity: 0.6\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.8)',\n                                                            fontSize: '0.75rem'\n                                                        },\n                                                        children: \"Soul sta scrivendo...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: userMemory.name ? \"Cosa vuoi condividere con Soul, \".concat(userMemory.name, \"?\") : \"Dimmi qualcosa di te...\",\n                                className: \"input-field\",\n                                disabled: isLoadingAI\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoadingAI,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProfileCard__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showProfile,\n                onClose: ()=>setShowProfile(false),\n                userName: userMemory.name || 'Utente',\n                intimacyLevel: userMemory.intimacyLevel,\n                messageCount: messages.length,\n                userId: userId,\n                onLogout: logout,\n                onReset: handleReset\n            }, void 0, false, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 582,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"JYJCDn9aTKPGvk/VWz1CCv+ED+k=\", false, function() {\n    return [\n        _hooks_useSupabaseMemory__WEBPACK_IMPORTED_MODULE_11__.useSupabaseMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});