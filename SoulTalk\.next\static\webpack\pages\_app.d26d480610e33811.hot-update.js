"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);\\n  background-attachment: fixed;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.5rem; }\\n.mb-4 { margin-bottom: 1rem; }\\n.mb-6 { margin-bottom: 1.5rem; }\\n.space-y-3 > * + * { margin-top: 0.75rem; }\\n.space-y-4 > * + * { margin-top: 1rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }\\n.text-xl { font-size: 1.25rem; line-height: 1.75rem; }\\n.text-sm { font-size: 0.875rem; line-height: 1.25rem; }\\n.text-xs { font-size: 0.75rem; line-height: 1rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 1rem;\\n  border-radius: 1rem;\\n  max-width: 20rem;\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.8);\\n  margin-left: auto;\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.1);\\n  margin-right: auto;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1rem;\\n  padding: 0.75rem 1rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 48px;\\n  max-height: 128px;\\n}\\n\\n.input-field::-moz-placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar nascosta ma funzionale */\\n.chat-container {\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n}\\n\\n.chat-container::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 1rem;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 1rem;\\n  min-height: 0; /* Importante per il flex */\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 1rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Responsive Design */\\n.h-screen { height: 100vh; }\\n.h-full { height: 100%; }\\n.min-h-0 { min-height: 0; }\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.75rem; }\\n  .chat-messages { padding: 0 0.75rem; }\\n  .chat-input-area { padding: 0.75rem; }\\n  .text-3xl { font-size: 1.5rem; line-height: 2rem; }\\n  .text-xl { font-size: 1.125rem; line-height: 1.5rem; }\\n  .p-6 { padding: 1rem; }\\n  .mb-6 { margin-bottom: 1rem; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.5rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1.25rem; line-height: 1.75rem; }\\n  .space-y-3 > * + * { margin-top: 0.5rem; }\\n  .space-y-4 > * + * { margin-top: 0.75rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n/* Responsive */\\n@media (max-width: 768px) {\\n  .max-w-4xl { max-width: 100%; }\\n  .p-4 { padding: 0.5rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ,0EAA0E;EAC1E,4BAA4B;AAC9B;;AAEA,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,mBAAmB,EAAE;AAC7B,QAAQ,qBAAqB,EAAE;AAC/B,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,gBAAgB,EAAE;AACvC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,mBAAmB,EAAE,oBAAoB,EAAE;AACvD,WAAW,kBAAkB,EAAE,oBAAoB,EAAE;AACrD,WAAW,mBAAmB,EAAE,oBAAoB,EAAE;AACtD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,YAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;;AAEzB,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,iDAAiD;EACjD,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,gBAAgB;EAChB,+CAA+C;EAC/C,0CAA0C;EAC1C,qBAAqB;EACrB,yBAAyB;AAC3B;;AAEA;EACE,kCAAkC;EAClC,iBAAiB;AACnB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,mBAAmB;EACnB,qBAAqB;EACrB,YAAY;EACZ,yBAAyB;EACzB,WAAW;EACX,YAAY;EACZ,gBAAgB;EAChB,iBAAiB;AACnB;;AAEA;EACE,+BAA+B;AACjC;;AAFA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,mCAA2B;UAA3B,2BAA2B;EAC3B,oCAAoC;EACpC,0CAA0C;EAC1C,sBAAsB;EACtB,gBAAgB;EAChB,YAAY;EACZ,eAAe;EACf,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,qCAAqC;AACrC;EACE,qBAAqB,EAAE,YAAY;EACnC,wBAAwB,EAAE,0BAA0B;AACtD;;AAEA;EACE,QAAQ;EACR,SAAS;EACT,aAAa,EAAE,0BAA0B;AAC3C;;AAEA,+BAA+B;AAC/B;EACE,aAAa;EACb,aAAa;EACb,sBAAsB;EACtB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,aAAa;AACf;;AAEA;EACE,OAAO;EACP,gBAAgB;EAChB,eAAe;EACf,aAAa,EAAE,2BAA2B;AAC5C;;AAEA;EACE,cAAc;EACd,aAAa;EACb,8BAA8B;EAC9B,mCAA2B;UAA3B,2BAA2B;EAC3B,8CAA8C;AAChD;;AAEA,sBAAsB;AACtB,YAAY,aAAa,EAAE;AAC3B,UAAU,YAAY,EAAE;AACxB,WAAW,aAAa,EAAE;;AAE1B,4BAA4B;AAC5B;EACE,eAAe,gBAAgB,EAAE;EACjC,iBAAiB,kBAAkB,EAAE;EACrC,mBAAmB,gBAAgB,EAAE;EACrC,YAAY,iBAAiB,EAAE,iBAAiB,EAAE;EAClD,WAAW,mBAAmB,EAAE,mBAAmB,EAAE;EACrD,OAAO,aAAa,EAAE;EACtB,QAAQ,mBAAmB,EAAE;AAC/B;;AAEA;EACE,eAAe,eAAe,EAAE;EAChC,iBAAiB,iBAAiB,EAAE;EACpC,mBAAmB,eAAe,EAAE;EACpC,YAAY,kBAAkB,EAAE,oBAAoB,EAAE;EACtD,qBAAqB,kBAAkB,EAAE;EACzC,qBAAqB,mBAAmB,EAAE;AAC5C;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,6BAA6B;AAC7B;EACE,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA,eAAe;AACf;EACE,aAAa,eAAe,EAAE;EAC9B,OAAO,eAAe,EAAE;AAC1B;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA,6BAA6B;AAC7B;EACE,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);\\n  background-attachment: fixed;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.5rem; }\\n.mb-4 { margin-bottom: 1rem; }\\n.mb-6 { margin-bottom: 1.5rem; }\\n.space-y-3 > * + * { margin-top: 0.75rem; }\\n.space-y-4 > * + * { margin-top: 1rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }\\n.text-xl { font-size: 1.25rem; line-height: 1.75rem; }\\n.text-sm { font-size: 0.875rem; line-height: 1.25rem; }\\n.text-xs { font-size: 0.75rem; line-height: 1rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\\n  border-radius: 1rem;\\n}\\n\\n.chat-bubble {\\n  padding: 1rem;\\n  border-radius: 1rem;\\n  max-width: 20rem;\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n  animation: slideInFromBottom 0.3s ease-out;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n}\\n\\n.chat-bubble-user {\\n  background: rgba(37, 99, 235, 0.8);\\n  margin-left: auto;\\n}\\n\\n.chat-bubble-ai {\\n  background: rgba(255, 255, 255, 0.1);\\n  margin-right: auto;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1rem;\\n  padding: 0.75rem 1rem;\\n  color: white;\\n  transition: all 0.3s ease;\\n  width: 100%;\\n  resize: none;\\n  min-height: 48px;\\n  max-height: 128px;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.btn {\\n  backdrop-filter: blur(12px);\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 0.75rem;\\n  padding: 0.75rem;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: none;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar nascosta ma funzionale */\\n.chat-container {\\n  scrollbar-width: none; /* Firefox */\\n  -ms-overflow-style: none; /* Internet Explorer 10+ */\\n}\\n\\n.chat-container::-webkit-scrollbar {\\n  width: 0;\\n  height: 0;\\n  display: none; /* Chrome, Safari, Opera */\\n}\\n\\n/* Layout responsive per chat */\\n.chat-layout {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.chat-header {\\n  flex-shrink: 0;\\n  padding: 1rem;\\n}\\n\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0 1rem;\\n  min-height: 0; /* Importante per il flex */\\n}\\n\\n.chat-input-area {\\n  flex-shrink: 0;\\n  padding: 1rem;\\n  background: rgba(0, 0, 0, 0.1);\\n  backdrop-filter: blur(10px);\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n/* Responsive Design */\\n.h-screen { height: 100vh; }\\n.h-full { height: 100%; }\\n.min-h-0 { min-height: 0; }\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.75rem; }\\n  .chat-messages { padding: 0 0.75rem; }\\n  .chat-input-area { padding: 0.75rem; }\\n  .text-3xl { font-size: 1.5rem; line-height: 2rem; }\\n  .text-xl { font-size: 1.125rem; line-height: 1.5rem; }\\n  .p-6 { padding: 1rem; }\\n  .mb-6 { margin-bottom: 1rem; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.5rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1.25rem; line-height: 1.75rem; }\\n  .space-y-3 > * + * { margin-top: 0.5rem; }\\n  .space-y-4 > * + * { margin-top: 0.75rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n/* Responsive */\\n@media (max-width: 768px) {\\n  .max-w-4xl { max-width: 100%; }\\n  .p-4 { padding: 0.5rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.5;\\n  }\\n}\\n\\n/* Scrollbar personalizzata */\\n::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ })

});