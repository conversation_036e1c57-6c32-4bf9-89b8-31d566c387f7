@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply min-h-screen text-white;
    background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);
    background-attachment: fixed;
  }
}

@layer components {
  .glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-radius: 1rem;
  }
  
  .chat-bubble {
    padding: 1rem;
    border-radius: 1rem;
    max-width: 20rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    animation: slideInFromBottom 0.3s ease-out;
  }

  .chat-bubble-user {
    background: rgba(37, 99, 235, 0.8);
    margin-left: auto;
  }

  .chat-bubble-ai {
    background: rgba(255, 255, 255, 0.1);
    margin-right: auto;
  }
  
  .aura-animation {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    filter: blur(1px);
  }

  .input-field {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    padding: 0.75rem 1rem;
    color: white;
    transition: all 0.3s ease;
  }

  .input-field::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  .input-field:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
}

@layer utilities {
  .animate-in {
    animation-fill-mode: both;
  }
  
  .slide-in-from-bottom-2 {
    animation-name: slideInFromBottom;
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar personalizzata */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
