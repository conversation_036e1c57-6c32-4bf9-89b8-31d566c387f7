@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900;
    @apply min-h-screen text-white;
    background-attachment: fixed;
  }
}

@layer components {
  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
    @apply shadow-xl rounded-2xl;
  }
  
  .chat-bubble {
    @apply p-4 rounded-2xl max-w-xs shadow-lg;
    @apply animate-in slide-in-from-bottom-2 duration-300;
  }
  
  .chat-bubble-user {
    @apply bg-blue-600/80 ml-auto;
  }
  
  .chat-bubble-ai {
    @apply bg-white/10 mr-auto;
  }
  
  .aura-animation {
    @apply animate-pulse-slow;
    filter: blur(1px);
  }
  
  .input-field {
    @apply glass-effect px-4 py-3 text-white placeholder-white/60;
    @apply focus:outline-none focus:ring-2 focus:ring-blue-400/50;
    @apply transition-all duration-300;
  }
}

@layer utilities {
  .animate-in {
    animation-fill-mode: both;
  }
  
  .slide-in-from-bottom-2 {
    animation-name: slideInFromBottom;
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar personalizzata */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
