import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://igybrqsuvkbeivyhrrvd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlneWJycXN1dmtiZWl2eWhycnZkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMjkzNzIsImV4cCI6MjA2ODYwNTM3Mn0.PfgqZs9fX5xEkiLiMArGR70PK2c6-Z9yaMiU7u8vqnM'

export const supabase = createClient(supabaseUrl, supabaseKey)

// Tipi per il database
export interface UserProfile {
  id: string
  name: string
  age?: number
  traits: string[]
  emotions: string[]
  intimacy_level: number
  key_moments: string[]
  created_at: string
  updated_at: string
}

export interface ChatMessage {
  id: string
  user_id: string
  content: string
  is_user: boolean
  timestamp: string
  created_at: string
}

export interface ChatSession {
  id: string
  user_id: string
  session_name?: string
  created_at: string
  updated_at: string
}
