import { useState, useEffect, useCallback } from 'react'
import {
  getUserProfile,
  createOrUpdateUserProfile,
  getChatMessages,
  saveChatMessage,
  deleteUserData,
  UserProfile,
  ChatMessage
} from '../lib/database'
import {
  getCurrentUser,
  saveUserSession,
  logoutUser,
  checkOnboardingStatus
} from '../lib/auth'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface UserMemory {
  name: string
  age: number
  traits: string[]
  emotions: string[]
  intimacyLevel: number
  keyMoments: string[]
}

export const useSupabaseMemory = () => {
  const [userId, setUserId] = useState<string>('')
  const [messages, setMessages] = useState<Message[]>([])
  const [userMemory, setUserMemory] = useState<UserMemory>({
    name: '',
    age: 0,
    traits: [],
    emotions: [],
    intimacyLevel: 0,
    keyMoments: []
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [needsOnboarding, setNeedsOnboarding] = useState(false)

  // Inizializza solo lato client
  useEffect(() => {
    setIsClient(true)
    // Controlla se l'utente è già autenticato
    if (typeof window !== 'undefined') {
      const currentUserId = getCurrentUser()
      if (currentUserId) {
        setUserId(currentUserId)
        setIsAuthenticated(true)
      }
    }
  }, [])

  // Funzione per autenticare l'utente
  const authenticateUser = useCallback((newUserId: string) => {
    setUserId(newUserId)
    setIsAuthenticated(true)
    saveUserSession(newUserId)
  }, [])

  // Funzione per logout
  const logout = useCallback(() => {
    logoutUser()
    setUserId('')
    setIsAuthenticated(false)
    setMessages([])
    setUserMemory({
      name: '',
      age: 0,
      traits: [],
      emotions: [],
      intimacyLevel: 0,
      keyMoments: []
    })
    setNeedsOnboarding(false)
  }, [])

  // Carica i dati iniziali solo quando siamo lato client, autenticati e abbiamo un userId
  useEffect(() => {
    if (!isClient || !userId || !isAuthenticated) return

    const loadData = async () => {
      setIsLoading(true)

      try {
        // Controlla se l'utente ha completato l'onboarding
        const isOnboarded = await checkOnboardingStatus(userId)
        setNeedsOnboarding(!isOnboarded)

        // Carica profilo utente
        const profile = await getUserProfile(userId)
        if (profile) {
          setUserMemory({
            name: profile.name || '',
            age: profile.age || 0,
            traits: profile.traits || [],
            emotions: profile.emotions || [],
            intimacyLevel: profile.intimacy_level || 0,
            keyMoments: profile.key_moments || []
          })
        }

        // Carica messaggi solo se l'onboarding è completato
        if (isOnboarded) {
          const chatMessages = await getChatMessages(userId)
          const formattedMessages: Message[] = chatMessages.map(msg => ({
            id: msg.id,
            content: msg.content,
            isUser: msg.is_user,
            timestamp: new Date(msg.timestamp)
          }))
          setMessages(formattedMessages)
        }

      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [isClient, userId, isAuthenticated])

  // Salva il profilo utente quando cambia
  useEffect(() => {
    if (!isClient || !userId || isLoading || !userMemory.name) return

    const saveProfile = async () => {
      const profile: Partial<UserProfile> = {
        id: userId,
        name: userMemory.name,
        age: userMemory.age || undefined,
        traits: userMemory.traits,
        emotions: userMemory.emotions,
        intimacy_level: userMemory.intimacyLevel,
        key_moments: userMemory.keyMoments,
        created_at: new Date().toISOString()
      }

      await createOrUpdateUserProfile(profile)
    }

    saveProfile()
  }, [isClient, userId, userMemory, isLoading])

  const addMessage = useCallback(async (content: string, isUser: boolean): Promise<Message> => {
    const newMessage: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      isUser,
      timestamp: new Date()
    }

    // Aggiungi immediatamente al state locale
    setMessages(prev => [...prev, newMessage])

    // Salva nel database in background solo se siamo lato client e abbiamo userId
    if (isClient && userId) {
      setIsSaving(true)
      try {
        const chatMessage: Omit<ChatMessage, 'created_at'> = {
          id: newMessage.id,
          user_id: userId,
          content: newMessage.content,
          is_user: newMessage.isUser,
          timestamp: newMessage.timestamp.toISOString()
        }

        await saveChatMessage(chatMessage)
      } catch (error) {
        console.error('Error saving message:', error)
      } finally {
        setIsSaving(false)
      }
    }

    return newMessage
  }, [isClient, userId])

  const updateUserMemory = useCallback((updates: Partial<UserMemory>) => {
    setUserMemory(prev => ({ ...prev, ...updates }))
  }, [])

  const resetMemory = useCallback(async () => {
    if (!isClient || !userId) return

    setIsLoading(true)

    try {
      await deleteUserData(userId)
      logout()

      if (typeof window !== 'undefined') {
        window.location.reload()
      }

    } catch (error) {
      console.error('Error resetting memory:', error)
    } finally {
      setIsLoading(false)
    }
  }, [isClient, userId, logout])

  // Completa l'onboarding con i dati dell'utente
  const completeOnboardingWithData = useCallback(async (userData: { name: string; age: number }) => {
    if (!userId) return

    try {
      // Aggiorna il profilo utente con i dati dell'onboarding
      await createOrUpdateUserProfile({
        id: userId,
        name: userData.name,
        age: userData.age,
        is_onboarded: true
      })

      // Aggiorna lo stato locale
      setUserMemory(prev => ({
        ...prev,
        name: userData.name,
        age: userData.age
      }))

      // Marca l'onboarding come completato
      setNeedsOnboarding(false)

      // Forza un refresh per caricare la chat normale
      setTimeout(() => {
        window.location.reload()
      }, 1000)

    } catch (error) {
      console.error('Error completing onboarding:', error)
    }
  }, [userId])

  return {
    userId,
    messages,
    userMemory,
    isLoading,
    isSaving,
    isAuthenticated,
    needsOnboarding,
    addMessage,
    updateUserMemory,
    resetMemory,
    authenticateUser,
    logout,
    completeOnboardingWithData
  }
}
