import { useState, useEffect, useCallback } from 'react'
import { 
  generateUserId, 
  getUserProfile, 
  createOrUpdateUserProfile,
  getChatMessages,
  saveChatMessage,
  deleteUserData,
  UserProfile,
  ChatMessage
} from '../lib/database'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface UserMemory {
  name: string
  age: number
  traits: string[]
  emotions: string[]
  intimacyLevel: number
  keyMoments: string[]
}

export const useSupabaseMemory = () => {
  const [userId] = useState(() => generateUserId())
  const [messages, setMessages] = useState<Message[]>([])
  const [userMemory, setUserMemory] = useState<UserMemory>({
    name: '',
    age: 0,
    traits: [],
    emotions: [],
    intimacyLevel: 0,
    keyMoments: []
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  // Carica i dati iniziali
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      
      try {
        // Carica profilo utente
        const profile = await getUserProfile(userId)
        if (profile) {
          setUserMemory({
            name: profile.name || '',
            age: profile.age || 0,
            traits: profile.traits || [],
            emotions: profile.emotions || [],
            intimacyLevel: profile.intimacy_level || 0,
            keyMoments: profile.key_moments || []
          })
        }

        // Carica messaggi
        const chatMessages = await getChatMessages(userId)
        const formattedMessages: Message[] = chatMessages.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.is_user,
          timestamp: new Date(msg.timestamp)
        }))
        setMessages(formattedMessages)
        
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [userId])

  // Salva il profilo utente quando cambia
  useEffect(() => {
    if (!isLoading && userMemory.name) {
      const saveProfile = async () => {
        const profile: Partial<UserProfile> = {
          id: userId,
          name: userMemory.name,
          age: userMemory.age || undefined,
          traits: userMemory.traits,
          emotions: userMemory.emotions,
          intimacy_level: userMemory.intimacyLevel,
          key_moments: userMemory.keyMoments,
          created_at: new Date().toISOString()
        }
        
        await createOrUpdateUserProfile(profile)
      }
      
      saveProfile()
    }
  }, [userId, userMemory, isLoading])

  const addMessage = useCallback(async (content: string, isUser: boolean): Promise<Message> => {
    const newMessage: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      isUser,
      timestamp: new Date()
    }

    // Aggiungi immediatamente al state locale
    setMessages(prev => [...prev, newMessage])

    // Salva nel database in background
    setIsSaving(true)
    try {
      const chatMessage: Omit<ChatMessage, 'created_at'> = {
        id: newMessage.id,
        user_id: userId,
        content: newMessage.content,
        is_user: newMessage.isUser,
        timestamp: newMessage.timestamp.toISOString()
      }
      
      await saveChatMessage(chatMessage)
    } catch (error) {
      console.error('Error saving message:', error)
    } finally {
      setIsSaving(false)
    }

    return newMessage
  }, [userId])

  const updateUserMemory = useCallback((updates: Partial<UserMemory>) => {
    setUserMemory(prev => ({ ...prev, ...updates }))
  }, [])

  const resetMemory = useCallback(async () => {
    setIsLoading(true)
    
    try {
      await deleteUserData(userId)
      
      // Reset local state
      setMessages([])
      setUserMemory({
        name: '',
        age: 0,
        traits: [],
        emotions: [],
        intimacyLevel: 0,
        keyMoments: []
      })
      
      // Genera nuovo user ID
      localStorage.removeItem('soul_user_id')
      window.location.reload()
      
    } catch (error) {
      console.error('Error resetting memory:', error)
    } finally {
      setIsLoading(false)
    }
  }, [userId])

  return {
    userId,
    messages,
    userMemory,
    isLoading,
    isSaving,
    addMessage,
    updateUserMemory,
    resetMemory
  }
}
