"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-browser)/./src/lib/database.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [needsOnboarding, setNeedsOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Inizializza solo lato client\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            setIsClient(true);\n            // Controlla se l'utente è già autenticato\n            if (true) {\n                const currentUserId = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n                if (currentUserId) {\n                    setUserId(currentUserId);\n                    setIsAuthenticated(true);\n                }\n            }\n        }\n    }[\"useSupabaseMemory.useEffect\"], []);\n    // Funzione per autenticare l'utente\n    const authenticateUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[authenticateUser]\": (newUserId)=>{\n            setUserId(newUserId);\n            setIsAuthenticated(true);\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveUserSession)(newUserId);\n        }\n    }[\"useSupabaseMemory.useCallback[authenticateUser]\"], []);\n    // Funzione per logout\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logoutUser)();\n            setUserId('');\n            setIsAuthenticated(false);\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setNeedsOnboarding(false);\n        }\n    }[\"useSupabaseMemory.useCallback[logout]\"], []);\n    // Carica i dati iniziali solo quando siamo lato client, autenticati e abbiamo un userId\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || !isAuthenticated) {\n                console.log('Skipping data load:', {\n                    isClient,\n                    userId: !!userId,\n                    isAuthenticated\n                });\n                return;\n            }\n            const loadData = {\n                \"useSupabaseMemory.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    console.log('Loading data for user:', userId);\n                    try {\n                        // Controlla se l'utente ha completato l'onboarding\n                        const isOnboarded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.checkOnboardingStatus)(userId);\n                        console.log('Onboarding status:', isOnboarded);\n                        setNeedsOnboarding(!isOnboarded);\n                        // Carica profilo utente\n                        const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(userId);\n                        console.log('User profile:', profile);\n                        if (profile) {\n                            setUserMemory({\n                                name: profile.name || '',\n                                age: profile.age || 0,\n                                traits: profile.traits || [],\n                                emotions: profile.emotions || [],\n                                intimacyLevel: profile.intimacy_level || 0,\n                                keyMoments: profile.key_moments || []\n                            });\n                        }\n                        // Carica messaggi solo se l'onboarding è completato\n                        if (isOnboarded) {\n                            const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(userId);\n                            const formattedMessages = chatMessages.map({\n                                \"useSupabaseMemory.useEffect.loadData.formattedMessages\": (msg)=>({\n                                        id: msg.id,\n                                        content: msg.content,\n                                        isUser: msg.is_user,\n                                        timestamp: new Date(msg.timestamp)\n                                    })\n                            }[\"useSupabaseMemory.useEffect.loadData.formattedMessages\"]);\n                            setMessages(formattedMessages);\n                            console.log('Loaded messages:', formattedMessages.length);\n                        }\n                    } catch (error) {\n                        console.error('Error loading data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseMemory.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        isAuthenticated\n    ]);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || isLoading || !userMemory.name) return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: \"msg_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se siamo lato client e abbiamo userId\n            if (isClient && userId) {\n                setIsSaving(true);\n                try {\n                    const chatMessage = {\n                        id: newMessage.id,\n                        user_id: userId,\n                        content: newMessage.content,\n                        is_user: newMessage.isUser,\n                        timestamp: newMessage.timestamp.toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.saveChatMessage)(chatMessage);\n                } catch (error) {\n                    console.error('Error saving message:', error);\n                } finally{\n                    setIsSaving(false);\n                }\n            }\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        isClient,\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!isClient || !userId) return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                logout();\n                if (true) {\n                    window.location.reload();\n                }\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        isClient,\n        userId,\n        logout\n    ]);\n    // Completa l'onboarding con i dati dell'utente\n    const completeOnboardingWithData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": async (userData)=>{\n            if (!userId) return;\n            try {\n                // Aggiorna il profilo utente con i dati dell'onboarding\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)({\n                    id: userId,\n                    name: userData.name,\n                    age: userData.age,\n                    is_onboarded: true\n                });\n                // Aggiorna lo stato locale\n                setUserMemory({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": (prev)=>({\n                            ...prev,\n                            name: userData.name,\n                            age: userData.age\n                        })\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"]);\n                setNeedsOnboarding(false);\n            } catch (error) {\n                console.error('Error completing onboarding:', error);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], [\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        isAuthenticated,\n        needsOnboarding,\n        addMessage,\n        updateUserMemory,\n        resetMemory,\n        authenticateUser,\n        logout,\n        completeOnboardingWithData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\n"));

/***/ })

});