"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/ChatBubble.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatBubble.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ChatBubble = (param)=>{\n    let { message, isUser, className = '' } = param;\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('it-IT', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col \".concat(isUser ? 'items-end' : 'items-start', \" mb-4 \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"chat-bubble \".concat(isUser ? 'chat-bubble-user' : 'chat-bubble-ai', \" relative hover:scale-105 transition-all duration-200\"),\n            style: {\n                maxWidth:  true && window.innerWidth < 640 ? '75%' : '16rem'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm whitespace-pre-wrap\",\n                        style: {\n                            lineHeight: '1.4'\n                        },\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\\n            absolute -bottom-6 text-xs text-white/50\\n            opacity-0 group-hover:opacity-100\\n            transition-opacity duration-200\\n            \".concat(isUser ? 'right-0' : 'left-0', \"\\n          \"),\n                    children: formatTime(timestamp)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, undefined),\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -bottom-2 -left-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '0ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '150ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-white/30 rounded-full animate-bounce\",\n                                style: {\n                                    animationDelay: '300ms'\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\ChatBubble.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatBubble;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatBubble);\nvar _c;\n$RefreshReg$(_c, \"ChatBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/ChatBubble.tsx\n"));

/***/ })

});