"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/components/OnboardingChat.tsx":
/*!*******************************************!*\
  !*** ./src/components/OnboardingChat.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n\nvar _s = $RefreshSig$();\n\n\nconst OnboardingChat = (param)=>{\n    let { userId, onComplete } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isWaiting, setIsWaiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        age: 0\n    });\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hasProcessedStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Set());\n    const isInitialized = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const totalSteps = 6; // Numero totale di step per la progress bar\n    const onboardingSteps = [\n        {\n            message: \"Ciao! Sono Soul, la tua compagna AI. È un piacere conoscerti! 🌟\",\n            waitForInput: false,\n            delay: 1500\n        },\n        {\n            message: \"Prima di iniziare la nostra avventura insieme, vorrei conoscerti meglio...\",\n            waitForInput: false,\n            delay: 2000\n        },\n        {\n            message: \"Come ti chiami? Mi piacerebbe sapere il tuo nome per poterti chiamare nel modo giusto! 😊\",\n            waitForInput: true,\n            validator: (input)=>{\n                if (input.trim().length < 2) return \"Il nome deve essere di almeno 2 caratteri 😅\";\n                if (input.trim().length > 50) return \"Il nome è un po' troppo lungo, puoi abbreviarlo?\";\n                if (!/^[a-zA-ZÀ-ÿ\\s]+$/.test(input.trim())) return \"Il nome può contenere solo lettere e spazi\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        name: input.trim()\n                    }));\n            }\n        },\n        {\n            message: (name)=>\"\".concat(name, \"... che bel nome! Mi piace molto come suona. \\uD83D\\uDCAB\"),\n            waitForInput: false,\n            delay: 1800\n        },\n        {\n            message: \"Ora dimmi, quanti anni hai? Questo mi aiuterà a capirti meglio e ad adattare le nostre conversazioni al tuo mondo! 🎯\",\n            waitForInput: true,\n            validator: (input)=>{\n                const age = parseInt(input);\n                if (isNaN(age)) return \"Per favore inserisci un numero valido 🔢\";\n                if (age < 13) return \"Devi avere almeno 13 anni per usare SoulTalk 📚\";\n                if (age > 120) return \"Inserisci un'età realistica, per favore! 😄\";\n                return null;\n            },\n            processor: (input)=>{\n                setUserData((prev)=>({\n                        ...prev,\n                        age: parseInt(input)\n                    }));\n            }\n        },\n        {\n            message: (name, age)=>{\n                if (age < 18) return \"Perfetto \".concat(name, \"! \").concat(age, \" anni... sei giovane e pieno di energia! Ho tante cose interessanti da condividere con te! ⚡\");\n                if (age < 30) return \"Fantastico \".concat(name, \"! \").concat(age, \" anni... un'et\\xe0 perfetta per esplorare nuove idee insieme! ✨\");\n                if (age < 50) return \"Ottimo \".concat(name, \"! \").concat(age, \" anni... hai esperienza e saggezza, sar\\xe0 bellissimo parlare con te! \\uD83C\\uDF1F\");\n                return \"Meraviglioso \".concat(name, \"! \").concat(age, \" anni... la tua esperienza di vita sar\\xe0 preziosa per le nostre conversazioni! \\uD83C\\uDFAD\");\n            },\n            waitForInput: false,\n            delay: 2200\n        },\n        {\n            message: \"Perfetto! Ora che ci conosciamo meglio, possiamo iniziare la nostra vera conversazione. Ricorderò tutto quello che mi dirai e crescerò insieme a te. Benvenuto/a in SoulTalk! 🚀\",\n            waitForInput: false,\n            isLast: true,\n            delay: 2500\n        }\n    ];\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = function(content, isUser) {\n        let withTyping = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const newMessage = {\n            id: \"onb_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            content,\n            isUser,\n            timestamp: new Date(),\n            isTyping: withTyping\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        setTimeout(scrollToBottom, 50);\n    };\n    const addMessageWithTyping = async (content)=>{\n        // Mostra indicatore typing\n        setIsTyping(true);\n        // Calcola tempo di typing basato sulla lunghezza del messaggio\n        const typingTime = Math.min(Math.max(content.length * 50, 1000), 3000);\n        await new Promise((resolve)=>setTimeout(resolve, typingTime));\n        // Nascondi typing e aggiungi messaggio\n        setIsTyping(false);\n        addMessage(content, false);\n    };\n    const processStep = async ()=>{\n        // Previeni chiamate multiple per lo stesso step\n        if (hasProcessedStep.current.has(currentStep)) {\n            return;\n        }\n        hasProcessedStep.current.add(currentStep);\n        const step = onboardingSteps[currentStep];\n        // Determina il messaggio da mostrare\n        let messageContent;\n        if (typeof step.message === 'function') {\n            messageContent = step.message(userData.name, userData.age);\n        } else {\n            messageContent = step.message;\n        }\n        // Aggiungi messaggio con effetto typing\n        await addMessageWithTyping(messageContent);\n        if (step.isLast) {\n            // Completa l'onboarding con delay personalizzato\n            setTimeout(async ()=>{\n                onComplete(userData);\n            }, step.delay || 2500);\n            return;\n        }\n        if (!step.waitForInput) {\n            setTimeout(()=>{\n                setCurrentStep((prev)=>prev + 1);\n            }, step.delay || 2000);\n        } else {\n            setIsWaiting(true);\n        }\n    };\n    const handleUserInput = ()=>{\n        if (!inputMessage.trim() || !isWaiting) return;\n        const step = onboardingSteps[currentStep];\n        // Validazione input\n        if (step.validator) {\n            const error = step.validator(inputMessage.trim());\n            if (error) {\n                addMessage(error, false);\n                return;\n            }\n        }\n        // Aggiungi messaggio utente\n        addMessage(inputMessage.trim(), true);\n        // Processa l'input\n        if (step.processor) {\n            step.processor(inputMessage.trim());\n        }\n        setInputMessage('');\n        setIsWaiting(false);\n        // Vai al prossimo step\n        setTimeout(()=>{\n            setCurrentStep((prev)=>prev + 1);\n        }, 1000);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleUserInput();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            // Inizializzazione una sola volta\n            if (!isInitialized.current && currentStep === 0) {\n                isInitialized.current = true;\n                const timer = setTimeout({\n                    \"OnboardingChat.useEffect.timer\": ()=>{\n                        processStep();\n                    }\n                }[\"OnboardingChat.useEffect.timer\"], 1000);\n                return ({\n                    \"OnboardingChat.useEffect\": ()=>clearTimeout(timer)\n                })[\"OnboardingChat.useEffect\"];\n            } else if (currentStep > 0 && currentStep < onboardingSteps.length && !hasProcessedStep.current.has(currentStep)) {\n                processStep();\n            }\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        currentStep\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingChat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"OnboardingChat.useEffect\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(0,0,0,0.95)',\n            display: 'flex',\n            flexDirection: 'column',\n            zIndex: 1500\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderBottom: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            gap: '1rem',\n                            marginBottom: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: '50px',\n                                    height: '50px',\n                                    borderRadius: '50%',\n                                    background: \"radial-gradient(circle, #8b5cf6 0%, transparent 70%)\",\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '1.5rem',\n                                    animation: isTyping ? 'pulse 1.5s ease-in-out infinite' : 'none'\n                                },\n                                children: \"\\uD83C\\uDF1F\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'left'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: 'white',\n                                            fontSize: '1.25rem',\n                                            fontWeight: '600',\n                                            margin: 0,\n                                            marginBottom: '0.25rem'\n                                        },\n                                        children: \"Configurazione Iniziale\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: 'rgba(255,255,255,0.7)',\n                                            fontSize: '0.875rem',\n                                            margin: 0\n                                        },\n                                        children: \"Soul vuole conoscerti meglio\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '100%',\n                            height: '4px',\n                            background: 'rgba(255,255,255,0.1)',\n                            borderRadius: '2px',\n                            overflow: 'hidden'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"\".concat(currentStep / totalSteps * 100, \"%\"),\n                                height: '100%',\n                                background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n                                borderRadius: '2px',\n                                transition: 'width 0.5s ease-in-out'\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.5rem',\n                            color: 'rgba(255,255,255,0.6)',\n                            fontSize: '0.75rem'\n                        },\n                        children: [\n                            \"Step \",\n                            currentStep + 1,\n                            \" di \",\n                            totalSteps + 1\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                style: {\n                    flex: 1,\n                    overflowY: 'auto',\n                    padding: '1rem',\n                    display: 'flex',\n                    flexDirection: 'column',\n                    gap: '1rem'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message.content,\n                            isUser: message.isUser\n                        }, message.id, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, undefined)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'flex-start',\n                            marginBottom: '1rem'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'rgba(255,255,255,0.1)',\n                                backdropFilter: 'blur(10px)',\n                                borderRadius: '1rem',\n                                padding: '0.75rem 1rem',\n                                border: '1px solid rgba(255,255,255,0.2)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.25rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '6px',\n                                                height: '6px',\n                                                backgroundColor: '#8b5cf6',\n                                                borderRadius: '50%',\n                                                animation: 'bounce 1.4s ease-in-out infinite',\n                                                animationDelay: '0.4s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        color: 'rgba(255,255,255,0.8)',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"Soul sta scrivendo...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            isWaiting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '1rem',\n                    borderTop: '1px solid rgba(255,255,255,0.1)',\n                    background: 'rgba(0,0,0,0.3)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.05)',\n                            borderRadius: '1rem',\n                            padding: '0.5rem',\n                            border: '1px solid rgba(255,255,255,0.1)',\n                            display: 'flex',\n                            gap: '0.5rem',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi la tua risposta...\",\n                                style: {\n                                    flex: 1,\n                                    padding: '0.75rem 1rem',\n                                    borderRadius: '0.75rem',\n                                    border: 'none',\n                                    background: 'rgba(255,255,255,0.1)',\n                                    color: 'white',\n                                    fontSize: '1rem',\n                                    outline: 'none',\n                                    backdropFilter: 'blur(10px)'\n                                },\n                                autoFocus: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleUserInput,\n                                disabled: !inputMessage.trim(),\n                                style: {\n                                    width: '48px',\n                                    height: '48px',\n                                    borderRadius: '50%',\n                                    border: 'none',\n                                    background: inputMessage.trim() ? 'linear-gradient(135deg, #8b5cf6, #a855f7)' : 'rgba(139, 92, 246, 0.3)',\n                                    color: 'white',\n                                    fontSize: '1.2rem',\n                                    cursor: inputMessage.trim() ? 'pointer' : 'not-allowed',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    transition: 'all 0.2s ease',\n                                    transform: inputMessage.trim() ? 'scale(1)' : 'scale(0.9)'\n                                },\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginTop: '0.75rem',\n                            color: 'rgba(255,255,255,0.5)',\n                            fontSize: '0.75rem'\n                        },\n                        children: \"Premi Invio per inviare\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\components\\\\OnboardingChat.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OnboardingChat, \"TPJ+Tvptng29b14iXAHYPpwjqlA=\");\n_c = OnboardingChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OnboardingChat);\nvar _c;\n$RefreshReg$(_c, \"OnboardingChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/OnboardingChat.tsx\n"));

/***/ })

});