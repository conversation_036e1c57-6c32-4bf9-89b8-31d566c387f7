import React from 'react';

interface ProfileCardProps {
  isOpen: boolean;
  onClose: () => void;
  userName: string;
  intimacyLevel: number;
  messageCount: number;
  userId: string;
  onLogout: () => void;
  onReset: () => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({
  isOpen,
  onClose,
  userName,
  intimacyLevel,
  messageCount,
  userId,
  onLogout,
  onReset
}) => {
  if (!isOpen) return null;

  const getIntimacyLabel = (level: number) => {
    const labels = ['Sconosciuto', 'Conoscente', 'Amico', 'Amico Stretto', 'Intimo'];
    return labels[level] || 'Sconosciuto';
  };

  const getIntimacyColor = (level: number) => {
    const colors = ['#6366f1', '#8b5cf6', '#a855f7', '#ec4899', '#f43f5e'];
    return colors[level] || colors[0];
  };

  return (
    <>
      {/* Overlay */}
      <div 
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          zIndex: 1000,
          backdropFilter: 'blur(5px)'
        }}
        onClick={onClose}
      />
      
      {/* Profile Card */}
      <div style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1.5rem',
        padding: '2rem',
        minWidth: '320px',
        maxWidth: '400px',
        border: '1px solid rgba(255,255,255,0.2)',
        zIndex: 1001,
        color: 'white'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1.5rem'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            margin: 0,
            background: `linear-gradient(135deg, ${getIntimacyColor(intimacyLevel)}, #a855f7)`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Il Tuo Profilo
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              color: 'rgba(255,255,255,0.7)',
              fontSize: '1.5rem',
              cursor: 'pointer',
              padding: '0.25rem',
              borderRadius: '0.5rem',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'none';
              e.currentTarget.style.color = 'rgba(255,255,255,0.7)';
            }}
          >
            ×
          </button>
        </div>

        {/* User Info */}
        <div style={{ marginBottom: '2rem' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem',
            marginBottom: '1rem'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              background: `linear-gradient(135deg, ${getIntimacyColor(intimacyLevel)}, #a855f7)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.5rem',
              fontWeight: '600'
            }}>
              {userName.charAt(0).toUpperCase()}
            </div>
            <div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                margin: 0,
                marginBottom: '0.25rem'
              }}>
                Ciao {userName}!
              </h3>
              <p style={{
                fontSize: '0.875rem',
                color: 'rgba(255,255,255,0.7)',
                margin: 0
              }}>
                ID: {userId.slice(-6)}
              </p>
            </div>
          </div>

          {/* Intimacy Level */}
          <div style={{
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '1rem',
            padding: '1rem',
            marginBottom: '1rem'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '0.5rem'
            }}>
              <span style={{ fontSize: '0.875rem', color: 'rgba(255,255,255,0.8)' }}>
                Livello di Connessione
              </span>
              <span style={{
                fontSize: '0.875rem',
                fontWeight: '600',
                color: getIntimacyColor(intimacyLevel)
              }}>
                {intimacyLevel}/4
              </span>
            </div>
            
            {/* Progress Bar */}
            <div style={{
              width: '100%',
              height: '8px',
              background: 'rgba(255,255,255,0.1)',
              borderRadius: '4px',
              overflow: 'hidden',
              marginBottom: '0.5rem'
            }}>
              <div style={{
                width: `${(intimacyLevel / 4) * 100}%`,
                height: '100%',
                background: `linear-gradient(90deg, ${getIntimacyColor(intimacyLevel)}, #a855f7)`,
                borderRadius: '4px',
                transition: 'width 0.5s ease'
              }} />
            </div>
            
            <p style={{
              fontSize: '0.75rem',
              color: 'rgba(255,255,255,0.6)',
              margin: 0,
              textAlign: 'center'
            }}>
              {getIntimacyLabel(intimacyLevel)}
            </p>
          </div>

          {/* Stats */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '1rem'
          }}>
            <div style={{
              background: 'rgba(255,255,255,0.05)',
              borderRadius: '0.75rem',
              padding: '0.75rem',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: getIntimacyColor(intimacyLevel),
                marginBottom: '0.25rem'
              }}>
                {messageCount}
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: 'rgba(255,255,255,0.7)'
              }}>
                Messaggi
              </div>
            </div>
            
            <div style={{
              background: 'rgba(255,255,255,0.05)',
              borderRadius: '0.75rem',
              padding: '0.75rem',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#8b5cf6',
                marginBottom: '0.25rem'
              }}>
                🌟
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: 'rgba(255,255,255,0.7)'
              }}>
                Soul
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div style={{
          display: 'flex',
          gap: '0.75rem',
          flexDirection: 'column'
        }}>
          <button
            onClick={onLogout}
            style={{
              padding: '0.75rem 1rem',
              borderRadius: '0.75rem',
              border: '1px solid rgba(255,255,255,0.2)',
              background: 'rgba(255,255,255,0.05)',
              color: 'white',
              fontSize: '0.875rem',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255,255,255,0.05)';
            }}
          >
            🚪 Logout
          </button>
          
          <button
            onClick={() => {
              if (confirm('Sei sicuro di voler eliminare tutti i dati? Questa azione non può essere annullata.')) {
                onReset();
              }
            }}
            style={{
              padding: '0.75rem 1rem',
              borderRadius: '0.75rem',
              border: '1px solid rgba(239, 68, 68, 0.3)',
              background: 'rgba(239, 68, 68, 0.1)',
              color: '#fca5a5',
              fontSize: '0.875rem',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.2)';
              e.currentTarget.style.color = '#f87171';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';
              e.currentTarget.style.color = '#fca5a5';
            }}
          >
            🗑️ Reset Completo
          </button>
        </div>
      </div>
    </>
  );
};

export default ProfileCard;
