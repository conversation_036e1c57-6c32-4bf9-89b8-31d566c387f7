"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts":
/*!****************************************!*\
  !*** ./src/hooks/useSupabaseMemory.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSupabaseMemory: () => (/* binding */ useSupabaseMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/database */ \"(pages-dir-browser)/./src/lib/database.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/auth */ \"(pages-dir-browser)/./src/lib/auth.ts\");\n\n\n\nconst useSupabaseMemory = ()=>{\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [userMemory, setUserMemory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        age: 0,\n        traits: [],\n        emotions: [],\n        intimacyLevel: 0,\n        keyMoments: []\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [needsOnboarding, setNeedsOnboarding] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Inizializza solo lato client\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            setIsClient(true);\n            // Non controlliamo sessioni esistenti - sempre login prima\n            setIsLoading(false);\n        }\n    }[\"useSupabaseMemory.useEffect\"], []);\n    // Funzione per autenticare l'utente\n    const authenticateUser = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[authenticateUser]\": (newUserId)=>{\n            setUserId(newUserId);\n            setIsAuthenticated(true);\n            setIsLoading(true); // Inizia il caricamento dati\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.saveUserSession)(newUserId);\n        }\n    }[\"useSupabaseMemory.useCallback[authenticateUser]\"], []);\n    // Funzione per logout\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[logout]\": ()=>{\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.logoutUser)();\n            setUserId('');\n            setIsAuthenticated(false);\n            setMessages([]);\n            setUserMemory({\n                name: '',\n                age: 0,\n                traits: [],\n                emotions: [],\n                intimacyLevel: 0,\n                keyMoments: []\n            });\n            setNeedsOnboarding(false);\n        }\n    }[\"useSupabaseMemory.useCallback[logout]\"], []);\n    // Carica i dati iniziali solo quando siamo lato client, autenticati e abbiamo un userId\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || !isAuthenticated) return;\n            const loadData = {\n                \"useSupabaseMemory.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Controlla se l'utente ha completato l'onboarding\n                        const isOnboarded = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.checkOnboardingStatus)(userId);\n                        setNeedsOnboarding(!isOnboarded);\n                        // Carica profilo utente\n                        const profile = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserProfile)(userId);\n                        if (profile) {\n                            setUserMemory({\n                                name: profile.name || '',\n                                age: profile.age || 0,\n                                traits: profile.traits || [],\n                                emotions: profile.emotions || [],\n                                intimacyLevel: profile.intimacy_level || 0,\n                                keyMoments: profile.key_moments || []\n                            });\n                        }\n                        // Carica messaggi solo se l'onboarding è completato\n                        if (isOnboarded) {\n                            const chatMessages = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getChatMessages)(userId);\n                            const formattedMessages = chatMessages.map({\n                                \"useSupabaseMemory.useEffect.loadData.formattedMessages\": (msg)=>({\n                                        id: msg.id,\n                                        content: msg.content,\n                                        isUser: msg.is_user,\n                                        timestamp: new Date(msg.timestamp)\n                                    })\n                            }[\"useSupabaseMemory.useEffect.loadData.formattedMessages\"]);\n                            setMessages(formattedMessages);\n                        }\n                    } catch (error) {\n                        console.error('Error loading data:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseMemory.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        isAuthenticated\n    ]);\n    // Salva il profilo utente quando cambia\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSupabaseMemory.useEffect\": ()=>{\n            if (!isClient || !userId || isLoading || !userMemory.name) return;\n            const saveProfile = {\n                \"useSupabaseMemory.useEffect.saveProfile\": async ()=>{\n                    const profile = {\n                        id: userId,\n                        name: userMemory.name,\n                        age: userMemory.age || undefined,\n                        traits: userMemory.traits,\n                        emotions: userMemory.emotions,\n                        intimacy_level: userMemory.intimacyLevel,\n                        key_moments: userMemory.keyMoments,\n                        created_at: new Date().toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)(profile);\n                }\n            }[\"useSupabaseMemory.useEffect.saveProfile\"];\n            saveProfile();\n        }\n    }[\"useSupabaseMemory.useEffect\"], [\n        isClient,\n        userId,\n        userMemory,\n        isLoading\n    ]);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[addMessage]\": async (content, isUser)=>{\n            const newMessage = {\n                id: \"msg_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n                content,\n                isUser,\n                timestamp: new Date()\n            };\n            // Aggiungi immediatamente al state locale\n            setMessages({\n                \"useSupabaseMemory.useCallback[addMessage]\": (prev)=>[\n                        ...prev,\n                        newMessage\n                    ]\n            }[\"useSupabaseMemory.useCallback[addMessage]\"]);\n            // Salva nel database in background solo se siamo lato client e abbiamo userId\n            if (isClient && userId) {\n                setIsSaving(true);\n                try {\n                    const chatMessage = {\n                        id: newMessage.id,\n                        user_id: userId,\n                        content: newMessage.content,\n                        is_user: newMessage.isUser,\n                        timestamp: newMessage.timestamp.toISOString()\n                    };\n                    await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.saveChatMessage)(chatMessage);\n                } catch (error) {\n                    console.error('Error saving message:', error);\n                } finally{\n                    setIsSaving(false);\n                }\n            }\n            return newMessage;\n        }\n    }[\"useSupabaseMemory.useCallback[addMessage]\"], [\n        isClient,\n        userId\n    ]);\n    const updateUserMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[updateUserMemory]\": (updates)=>{\n            setUserMemory({\n                \"useSupabaseMemory.useCallback[updateUserMemory]\": (prev)=>({\n                        ...prev,\n                        ...updates\n                    })\n            }[\"useSupabaseMemory.useCallback[updateUserMemory]\"]);\n        }\n    }[\"useSupabaseMemory.useCallback[updateUserMemory]\"], []);\n    const resetMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[resetMemory]\": async ()=>{\n            if (!isClient || !userId) return;\n            setIsLoading(true);\n            try {\n                await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.deleteUserData)(userId);\n                logout();\n                if (true) {\n                    window.location.reload();\n                }\n            } catch (error) {\n                console.error('Error resetting memory:', error);\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[resetMemory]\"], [\n        isClient,\n        userId,\n        logout\n    ]);\n    // Completa l'onboarding con i dati dell'utente\n    const completeOnboardingWithData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": async (userData)=>{\n            if (!userId) return;\n            try {\n                console.log('Completing onboarding for user:', userId, 'with data:', userData);\n                // Aggiorna il profilo utente con i dati dell'onboarding\n                const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createOrUpdateUserProfile)({\n                    id: userId,\n                    name: userData.name,\n                    age: userData.age,\n                    is_onboarded: true\n                });\n                console.log('Profile update result:', result);\n                // Aggiorna lo stato locale\n                setUserMemory({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": (prev)=>({\n                            ...prev,\n                            name: userData.name,\n                            age: userData.age\n                        })\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"]);\n                // Marca l'onboarding come completato\n                setNeedsOnboarding(false);\n                // Forza un refresh per caricare la chat normale\n                setTimeout({\n                    \"useSupabaseMemory.useCallback[completeOnboardingWithData]\": ()=>{\n                        console.log('Reloading page after onboarding completion');\n                        window.location.reload();\n                    }\n                }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], 1000);\n            } catch (error) {\n                console.error('Error completing onboarding:', error);\n            }\n        }\n    }[\"useSupabaseMemory.useCallback[completeOnboardingWithData]\"], [\n        userId\n    ]);\n    return {\n        userId,\n        messages,\n        userMemory,\n        isLoading,\n        isSaving,\n        isAuthenticated,\n        needsOnboarding,\n        addMessage,\n        updateUserMemory,\n        resetMemory,\n        authenticateUser,\n        logout,\n        completeOnboardingWithData\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/hooks/useSupabaseMemory.ts\n"));

/***/ })

});