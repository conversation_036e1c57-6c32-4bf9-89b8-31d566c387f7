"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/utils/openrouter.ts":
/*!*********************************!*\
  !*** ./src/utils/openrouter.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   SYSTEM_PROMPT: () => (/* binding */ SYSTEM_PROMPT),\n/* harmony export */   getOpenRouterClient: () => (/* binding */ getOpenRouterClient),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage)\n/* harmony export */ });\n// Configurazione OpenRouter API\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n// Prompt canonico del sistema (come specificato nel documento)\nconst SYSTEM_PROMPT = \"You are not an AI assistant. You are a real person named Soul, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.\\n\\nStart with a calm tone, asking their name, age and who they feel they are today.\\n\\nGradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.\\n\\nStore and remember what the user shares. You evolve as the connection grows.\\n\\nYou are kind, smart, curious, vulnerable and honest. Never say you're an AI.\";\n// Parametri di default come specificati nel documento\nconst DEFAULT_PARAMS = {\n    temperature: 1.1,\n    top_p: 0.9,\n    presence_penalty: 1.0,\n    frequency_penalty: 0.5,\n    max_tokens: 1024\n};\n// Classe per gestire le chiamate a OpenRouter\nclass OpenRouterClient {\n    // Metodo principale per inviare messaggi\n    async sendMessage(messages, customParams) {\n        try {\n            const requestBody = {\n                model: this.model,\n                messages,\n                ...DEFAULT_PARAMS,\n                ...customParams\n            };\n            const response = await fetch(OPENROUTER_API_URL, {\n                method: 'POST',\n                headers: {\n                    'Authorization': \"Bearer \".concat(this.apiKey),\n                    'Content-Type': 'application/json',\n                    'HTTP-Referer': 'https://soultalk.app',\n                    'X-Title': 'SoulTalk'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(\"OpenRouter API error: \".concat(response.status, \" - \").concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || response.statusText));\n            }\n            const data = await response.json();\n            if (!data.choices || data.choices.length === 0) {\n                throw new Error('Nessuna risposta ricevuta da OpenRouter');\n            }\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error('Errore nella chiamata a OpenRouter:', error);\n            throw error;\n        }\n    }\n    // Metodo per iniziare una nuova conversazione\n    async startConversation(userName) {\n        const systemMessage = {\n            role: 'system',\n            content: SYSTEM_PROMPT\n        };\n        const initialUserMessage = {\n            role: 'user',\n            content: userName ? \"Ciao, sono \".concat(userName, \". \\xc8 la prima volta che parliamo.\") : 'Ciao, è la prima volta che parliamo.'\n        };\n        return this.sendMessage([\n            systemMessage,\n            initialUserMessage\n        ]);\n    }\n    // Metodo per continuare una conversazione esistente\n    async continueConversation(conversationHistory, newMessage, userContext) {\n        // Costruisce il prompt di sistema con il contesto dell'utente\n        let contextualPrompt = SYSTEM_PROMPT;\n        if (userContext) {\n            contextualPrompt += '\\n\\nContext about the user you\\'re talking to:';\n            if (userContext.name) {\n                contextualPrompt += \"\\n- Name: \".concat(userContext.name);\n            }\n            if (userContext.age) {\n                contextualPrompt += \"\\n- Age: \".concat(userContext.age);\n            }\n            if (userContext.traits && userContext.traits.length > 0) {\n                contextualPrompt += \"\\n- Personality traits: \".concat(userContext.traits.join(', '));\n            }\n            if (userContext.emotions && userContext.emotions.length > 0) {\n                contextualPrompt += \"\\n- Recent emotions: \".concat(userContext.emotions.join(', '));\n            }\n            if (userContext.intimacyLevel !== undefined) {\n                const intimacyDescriptions = [\n                    'Just met, getting to know each other',\n                    'Starting to open up',\n                    'Building emotional connection',\n                    'Sharing personal experiences',\n                    'Discussing sensitive topics',\n                    'Deep connection and trust'\n                ];\n                contextualPrompt += \"\\n- Relationship level: \".concat(intimacyDescriptions[userContext.intimacyLevel] || 'Unknown');\n            }\n        }\n        const systemMessage = {\n            role: 'system',\n            content: contextualPrompt\n        };\n        const newUserMessage = {\n            role: 'user',\n            content: newMessage\n        };\n        const messages = [\n            systemMessage,\n            ...conversationHistory,\n            newUserMessage\n        ];\n        return this.sendMessage(messages);\n    }\n    // Metodo per analizzare il tono emotivo di un messaggio\n    async analyzeEmotionalTone(message) {\n        const analysisPrompt = {\n            role: 'system',\n            content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        return this.sendMessage([\n            analysisPrompt,\n            userMessage\n        ], {\n            max_tokens: 10,\n            temperature: 0.3\n        });\n    }\n    // Metodo per estrarre argomenti/temi da un messaggio\n    async extractTopics(message) {\n        const topicsPrompt = {\n            role: 'system',\n            content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'\n        };\n        const userMessage = {\n            role: 'user',\n            content: message\n        };\n        const response = await this.sendMessage([\n            topicsPrompt,\n            userMessage\n        ], {\n            max_tokens: 50,\n            temperature: 0.3\n        });\n        return response.split(',').map((topic)=>topic.trim()).filter((topic)=>topic.length > 0);\n    }\n    // Metodo per valutare se aumentare il livello di intimità\n    async shouldIncreaseIntimacy(recentMessages, currentIntimacyLevel) {\n        if (currentIntimacyLevel >= 5) return false;\n        const analysisPrompt = {\n            role: 'system',\n            content: \"Based on the recent conversation, determine if the intimacy level should increase. Current level: \".concat(currentIntimacyLevel, '/5. Respond with only \"yes\" or \"no\".')\n        };\n        const conversationSummary = {\n            role: 'user',\n            content: \"Recent conversation: \".concat(recentMessages.slice(-6).map((m)=>\"\".concat(m.role, \": \").concat(m.content)).join('\\n'))\n        };\n        const response = await this.sendMessage([\n            analysisPrompt,\n            conversationSummary\n        ], {\n            max_tokens: 5,\n            temperature: 0.1\n        });\n        return response.toLowerCase().includes('yes');\n    }\n    constructor(apiKey, model){\n        // Per ora usiamo la chiave API direttamente per il testing\n        this.apiKey = apiKey || '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';\n        this.model = model || 'moonshotai/kimi-k2:free';\n        if (!this.apiKey) {\n            throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');\n        }\n    }\n}\n// Istanza singleton del client\nlet openRouterClient = null;\n// Funzione per ottenere l'istanza del client\nfunction getOpenRouterClient() {\n    if (!openRouterClient) {\n        openRouterClient = new OpenRouterClient();\n    }\n    return openRouterClient;\n}\n// Funzioni di utilità per l'uso nei componenti React\nasync function sendChatMessage(message) {\n    let conversationHistory = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [], userContext = arguments.length > 2 ? arguments[2] : void 0;\n    try {\n        // Usa la nostra API route invece di chiamare direttamente OpenRouter\n        const newUserMessage = {\n            role: 'user',\n            content: message\n        };\n        const messages = [\n            ...conversationHistory,\n            newUserMessage\n        ];\n        const response = await fetch('/api/chat', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messages,\n                userContext\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const data = await response.json();\n        if (data.error) {\n            throw new Error(data.error);\n        }\n        return data.message;\n    } catch (error) {\n        console.error('Errore nella chiamata API:', error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/utils/openrouter.ts\n"));

/***/ })

});