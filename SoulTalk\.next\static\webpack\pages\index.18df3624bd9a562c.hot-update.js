"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ChatInput */ \"(pages-dir-browser)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { memory, isLoaded, addMessage, updateBasicInfo, addTrait, addEmotion, updateIntimacyLevel, getAuraColor, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll automatico smooth verso il basso quando arrivano nuovi messaggi\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTo({\n                    top: chatContainerRef.current.scrollHeight,\n                    behavior: 'smooth'\n                });\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Nasconde il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (memory.conversationHistory.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Converte la cronologia della memoria in formato OpenRouter\n    const getOpenRouterHistory = ()=>{\n        return memory.conversationHistory.map((msg)=>({\n                role: msg.isUser ? 'user' : 'assistant',\n                content: msg.content\n            }));\n    };\n    // Gestisce l'invio di un nuovo messaggio\n    const handleSendMessage = async (message)=>{\n        if (isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Aggiunge il messaggio dell'utente alla cronologia\n            addMessage(message, true);\n            // Prepara il contesto utente per l'AI\n            const userContext = {\n                name: memory.name || undefined,\n                age: memory.age || undefined,\n                traits: memory.traits,\n                emotions: memory.emotions,\n                intimacyLevel: memory.intimacyLevel\n            };\n            // Invia il messaggio all'AI\n            const aiResponse = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(message, getOpenRouterHistory(), userContext);\n            // Aggiunge la risposta dell'AI alla cronologia\n            addMessage(aiResponse, false);\n            // Analizza il messaggio per estrarre informazioni (simulato per ora)\n            await analyzeAndUpdateMemory(message, aiResponse);\n        } catch (err) {\n            console.error('Errore nell\\'invio del messaggio:', err);\n            // Messaggio di fallback più empatico\n            const fallbackResponse = \"Scusa, sto avendo qualche difficoltà tecnica in questo momento, ma sono qui con te! 💙 Anche se non riesco a rispondere come vorrei, la nostra conversazione è importante per me. Riprova tra qualche secondo - nel frattempo, continua pure a scrivermi quello che hai in mente.\";\n            // Aggiungi comunque una risposta di fallback alla conversazione\n            addMessage(fallbackResponse, false);\n            setError(null); // Non mostrare errore, ma la risposta di fallback\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Analizza i messaggi e aggiorna la memoria (versione semplificata)\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        // Analisi semplificata basata su parole chiave\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !memory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updateBasicInfo({\n                    name\n                });\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !memory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updateBasicInfo({\n                    age\n                });\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                addEmotion(emotion);\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità basato sulla lunghezza e contenuto\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            const currentLevel = memory.intimacyLevel;\n            if (currentLevel < 5) {\n                updateIntimacyLevel(currentLevel + 1);\n            }\n        }\n    };\n    // Gestisce il reset della memoria\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            resetMemory();\n            setShowWelcome(true);\n            setError(null);\n        }\n    };\n    // Mostra loading durante l'idratazione\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - La tua amicizia AI\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Caricamento SoulTalk...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"chat-layout\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    intimacyLevel: memory.intimacyLevel,\n                                    showLabel: false,\n                                    className: \"justify-center\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__.AuraProgression, {\n                                    intimacyLevel: memory.intimacyLevel\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"flex-1 flex flex-col min-h-0 mx-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatContainerRef,\n                                    className: \"chat-container chat-messages flex-1 space-y-4\",\n                                    children: [\n                                        showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            style: {\n                                                padding: '1rem 0'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect rounded-2xl mx-auto\",\n                                                style: {\n                                                    maxWidth: '20rem',\n                                                    padding: '1rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs opacity-80\",\n                                                        style: {\n                                                            lineHeight: '1.4'\n                                                        },\n                                                        children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                message: message.content,\n                                                isUser: message.isUser,\n                                                timestamp: message.timestamp\n                                            }, message.id, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)),\n                                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect rounded-xl\",\n                                                style: {\n                                                    padding: '0.75rem'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '6px',\n                                                                height: '6px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '6px',\n                                                                height: '6px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%',\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '6px',\n                                                                height: '6px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%',\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-200 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"chat-input-area\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        onSendMessage: handleSendMessage,\n                                        disabled: isLoading,\n                                        placeholder: memory.name ? \"Cosa vuoi condividere con Soul, \".concat(memory.name, \"?\") : \"Dimmi qualcosa di te...\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-xs opacity-60\",\n                        style: {\n                            padding: '0.5rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: memory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Ciao \",\n                                        memory.name,\n                                        \"! Livello: \",\n                                        memory.intimacyLevel,\n                                        \"/5\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                className: \"transition-all duration-200 text-xs\",\n                                style: {\n                                    cursor: 'pointer'\n                                },\n                                onMouseOver: (e)=>e.target.style.opacity = '1',\n                                onMouseOut: (e)=>e.target.style.opacity = '0.6',\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"FML9iZAdtm0rOHu/Dtj/snyE0xQ=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});