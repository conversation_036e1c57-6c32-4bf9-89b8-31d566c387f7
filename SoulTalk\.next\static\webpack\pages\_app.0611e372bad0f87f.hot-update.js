"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  /* Lo sfondo sarà gestito dinamicamente da JavaScript */\\n  transition: background 2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n/* Classi per i diversi livelli di intimità */\\n.intimacy-level-0 { /* Sconosciuto - Blu freddi */ }\\n.intimacy-level-1 { /* Conoscente - Viola */ }\\n.intimacy-level-2 { /* Amico - Viola-Rosa */ }\\n.intimacy-level-3 { /* Amico stretto - Rosa */ }\\n.intimacy-level-4 { /* Intimo - Rosa caldi */ }\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n.h-full { height: 100%; }\\n.flex-col { flex-direction: column; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n/* Chat Messages */\\n.message-container {\\n  margin-bottom: 0.875rem;\\n  display: flex;\\n}\\n\\n.message-container.user {\\n  justify-content: flex-end;\\n}\\n\\n.message-container.assistant {\\n  justify-content: flex-start;\\n}\\n\\n.message {\\n  max-width: 70%;\\n  padding: 0.625rem 0.875rem;\\n  border-radius: 0.875rem;\\n  word-wrap: break-word;\\n  font-size: 0.8rem;\\n  line-height: 1.3;\\n}\\n\\n.user-message {\\n  background: #007AFF;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.assistant-message {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom-left-radius: 0.25rem;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  flex: 1 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1.375rem;\\n  padding: 0.625rem 0.875rem;\\n  color: white;\\n  font-size: 0.875rem;\\n  resize: none;\\n  height: 44px;\\n  outline: none;\\n  width: 100%;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-button {\\n  background: #007AFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n  font-size: 0.875rem;\\n}\\n\\n.send-button:hover {\\n  background: #0056CC;\\n}\\n\\n.send-button:disabled {\\n  background: rgba(255, 255, 255, 0.2);\\n  cursor: not-allowed;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Chat Container - Full Screen */\\n.chat-container {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  background: rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n\\n/* Header */\\n.chat-header {\\n  height: 56px;\\n  padding: 0.875rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n}\\n\\n/* Messages Area */\\n.chat-messages {\\n  flex: 1 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: calc(100vh - 140px); /* 60px header + 80px input */\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n/* Input Area */\\n.chat-input-area {\\n  height: 76px;\\n  padding: 0.875rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.125rem; height: 52px; }\\n  .chat-messages { padding: 0 0.375rem; height: calc(100vh - 120px); }\\n  .chat-input-area { padding: 0.375rem; height: 68px; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1.125rem; }\\n  .text-xl { font-size: 0.75rem; line-height: 0.875rem; }\\n  .p-6 { padding: 0.625rem; }\\n  .mb-6 { margin-bottom: 0.375rem; }\\n  .message {\\n    max-width: 85%;\\n    padding: 0.5rem 0.75rem;\\n    font-size: 0.75rem;\\n    line-height: 1.2;\\n  }\\n  .input-field { height: 40px; font-size: 0.75rem; padding: 0.5rem 0.75rem; }\\n  .send-button { width: 40px; height: 40px; font-size: 0.75rem; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.0625rem; height: 48px; }\\n  .chat-messages { padding: 0 0.25rem; height: calc(100vh - 112px); }\\n  .chat-input-area { padding: 0.25rem; height: 64px; }\\n  .text-3xl { font-size: 0.75rem; line-height: 0.875rem; }\\n  .space-y-3 > * + * { margin-top: 0.125rem; }\\n  .space-y-4 > * + * { margin-top: 0.375rem; }\\n  .message {\\n    max-width: 90%;\\n    padding: 0.375rem 0.625rem;\\n    font-size: 0.65rem;\\n    line-height: 1.1;\\n  }\\n  .input-field { height: 36px; font-size: 0.65rem; padding: 0.25rem 0.5rem; }\\n  .send-button { width: 36px; height: 36px; font-size: 0.65rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Animazione onda per il bordo dell'input */\\n@keyframes gradientWave {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n\\n@keyframes liquidMove {\\n  0% {\\n    background-position: 0% 50%, 100% 50%, 50% 0%, 0% 0%;\\n  }\\n  25% {\\n    background-position: 25% 75%, 75% 25%, 75% 25%, 0% 0%;\\n  }\\n  50% {\\n    background-position: 50% 100%, 50% 0%, 100% 50%, 0% 0%;\\n  }\\n  75% {\\n    background-position: 75% 25%, 25% 75%, 25% 75%, 0% 0%;\\n  }\\n  100% {\\n    background-position: 100% 50%, 0% 50%, 50% 100%, 0% 0%;\\n  }\\n}\\n\\n/* Effetto glow per il bordo input */\\n.input-border-container {\\n  position: relative;\\n}\\n\\n.input-border-container::before {\\n  content: '';\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: inherit;\\n  border-radius: inherit;\\n  filter: blur(8px);\\n  opacity: 0.3;\\n  z-index: -1;\\n  animation: inherit;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/global.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ,uDAAuD;EACvD,sDAAsD;AACxD;;AAEA,6CAA6C;AAC7C,oBAAoB,6BAA6B,EAAE;AACnD,oBAAoB,uBAAuB,EAAE;AAC7C,oBAAoB,uBAAuB,EAAE;AAC7C,oBAAoB,yBAAyB,EAAE;AAC/C,oBAAoB,wBAAwB,EAAE;;AAE9C,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,mBAAmB,kBAAkB,EAAE;AACvC,uBAAuB,qBAAqB,EAAE;AAC9C,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,sBAAsB,EAAE;AAChC,qBAAqB,kBAAkB,EAAE;AACzC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,kBAAkB,EAAE,mBAAmB,EAAE;AACrD,WAAW,eAAe,EAAE,oBAAoB,EAAE;AAClD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,WAAW,mBAAmB,EAAE,qBAAqB,EAAE;AACvD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,SAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;AACzB,UAAU,YAAY,EAAE;AACxB,YAAY,sBAAsB,EAAE;;AAEpC,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,kDAA0C;UAA1C,0CAA0C;EAC1C,qCAAqC;EACrC,2CAA2C;EAC3C;;;4CAG0C;EAC1C,mBAAmB;AACrB;;AAEA,kBAAkB;AAClB;EACE,uBAAuB;EACvB,aAAa;AACf;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,2BAA2B;AAC7B;;AAEA;EACE,cAAc;EACd,0BAA0B;EAC1B,uBAAuB;EACvB,qBAAqB;EACrB,iBAAiB;EACjB,gBAAgB;AAClB;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,mCAAmC;AACrC;;AAEA;EACE,oCAAoC;EACpC,YAAY;EACZ,0CAA0C;EAC1C,mCAA2B;UAA3B,2BAA2B;EAC3B,kCAAkC;AACpC;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,SAAO;EACP,oCAAoC;EACpC,0CAA0C;EAC1C,uBAAuB;EACvB,0BAA0B;EAC1B,YAAY;EACZ,mBAAmB;EACnB,YAAY;EACZ,YAAY;EACZ,aAAa;EACb,WAAW;AACb;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,yBAAyB;EACzB,cAAc;EACd,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;EACpC,mBAAmB;AACrB;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,8BAA8B;EAC9B,mCAA2B;UAA3B,2BAA2B;AAC7B;;AAEA,WAAW;AACX;EACE,YAAY;EACZ,iBAAiB;EACjB,iDAAiD;EACjD,8BAA8B;EAC9B,aAAa;EACb,mBAAmB;AACrB;;AAEA,kBAAkB;AAClB;EACE,SAAO;EACP,gBAAgB;EAChB,aAAa;EACb,qBAAqB;EACrB,wBAAwB;EACxB,2BAA2B,EAAE,6BAA6B;AAC5D;;AAEA;EACE,aAAa;AACf;;AAEA,eAAe;AACf;EACE,YAAY;EACZ,iBAAiB;EACjB,8CAA8C;EAC9C,8BAA8B;EAC9B,aAAa;EACb,mBAAmB;EACnB,aAAa;AACf;;AAEA,4BAA4B;AAC5B;EACE,eAAe,iBAAiB,EAAE,YAAY,EAAE;EAChD,iBAAiB,mBAAmB,EAAE,2BAA2B,EAAE;EACnE,mBAAmB,iBAAiB,EAAE,YAAY,EAAE;EACpD,YAAY,mBAAmB,EAAE,qBAAqB,EAAE;EACxD,WAAW,kBAAkB,EAAE,qBAAqB,EAAE;EACtD,OAAO,iBAAiB,EAAE;EAC1B,QAAQ,uBAAuB,EAAE;EACjC;IACE,cAAc;IACd,uBAAuB;IACvB,kBAAkB;IAClB,gBAAgB;EAClB;EACA,eAAe,YAAY,EAAE,kBAAkB,EAAE,uBAAuB,EAAE;EAC1E,eAAe,WAAW,EAAE,YAAY,EAAE,kBAAkB,EAAE;AAChE;;AAEA;EACE,eAAe,kBAAkB,EAAE,YAAY,EAAE;EACjD,iBAAiB,kBAAkB,EAAE,2BAA2B,EAAE;EAClE,mBAAmB,gBAAgB,EAAE,YAAY,EAAE;EACnD,YAAY,kBAAkB,EAAE,qBAAqB,EAAE;EACvD,qBAAqB,oBAAoB,EAAE;EAC3C,qBAAqB,oBAAoB,EAAE;EAC3C;IACE,cAAc;IACd,0BAA0B;IAC1B,kBAAkB;IAClB,gBAAgB;EAClB;EACA,eAAe,YAAY,EAAE,kBAAkB,EAAE,uBAAuB,EAAE;EAC1E,eAAe,WAAW,EAAE,YAAY,EAAE,kBAAkB,EAAE;AAChE;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA,4CAA4C;AAC5C;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA;EACE;IACE,oDAAoD;EACtD;EACA;IACE,qDAAqD;EACvD;EACA;IACE,sDAAsD;EACxD;EACA;IACE,qDAAqD;EACvD;EACA;IACE,sDAAsD;EACxD;AACF;;AAEA,oCAAoC;AACpC;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,WAAW;EACX,YAAY;EACZ,mBAAmB;EACnB,sBAAsB;EACtB,iBAAiB;EACjB,YAAY;EACZ,WAAW;EACX,kBAAkB;AACpB\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  /* Lo sfondo sarà gestito dinamicamente da JavaScript */\\n  transition: background 2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n/* Classi per i diversi livelli di intimità */\\n.intimacy-level-0 { /* Sconosciuto - Blu freddi */ }\\n.intimacy-level-1 { /* Conoscente - Viola */ }\\n.intimacy-level-2 { /* Amico - Viola-Rosa */ }\\n.intimacy-level-3 { /* Amico stretto - Rosa */ }\\n.intimacy-level-4 { /* Intimo - Rosa caldi */ }\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n.h-full { height: 100%; }\\n.flex-col { flex-direction: column; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n/* Chat Messages */\\n.message-container {\\n  margin-bottom: 0.875rem;\\n  display: flex;\\n}\\n\\n.message-container.user {\\n  justify-content: flex-end;\\n}\\n\\n.message-container.assistant {\\n  justify-content: flex-start;\\n}\\n\\n.message {\\n  max-width: 70%;\\n  padding: 0.625rem 0.875rem;\\n  border-radius: 0.875rem;\\n  word-wrap: break-word;\\n  font-size: 0.8rem;\\n  line-height: 1.3;\\n}\\n\\n.user-message {\\n  background: #007AFF;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.assistant-message {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  backdrop-filter: blur(10px);\\n  border-bottom-left-radius: 0.25rem;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1.375rem;\\n  padding: 0.625rem 0.875rem;\\n  color: white;\\n  font-size: 0.875rem;\\n  resize: none;\\n  height: 44px;\\n  outline: none;\\n  width: 100%;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-button {\\n  background: #007AFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n  font-size: 0.875rem;\\n}\\n\\n.send-button:hover {\\n  background: #0056CC;\\n}\\n\\n.send-button:disabled {\\n  background: rgba(255, 255, 255, 0.2);\\n  cursor: not-allowed;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Chat Container - Full Screen */\\n.chat-container {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  background: rgba(0, 0, 0, 0.1);\\n  backdrop-filter: blur(20px);\\n}\\n\\n/* Header */\\n.chat-header {\\n  height: 56px;\\n  padding: 0.875rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n}\\n\\n/* Messages Area */\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: calc(100vh - 140px); /* 60px header + 80px input */\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n/* Input Area */\\n.chat-input-area {\\n  height: 76px;\\n  padding: 0.875rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.125rem; height: 52px; }\\n  .chat-messages { padding: 0 0.375rem; height: calc(100vh - 120px); }\\n  .chat-input-area { padding: 0.375rem; height: 68px; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1.125rem; }\\n  .text-xl { font-size: 0.75rem; line-height: 0.875rem; }\\n  .p-6 { padding: 0.625rem; }\\n  .mb-6 { margin-bottom: 0.375rem; }\\n  .message {\\n    max-width: 85%;\\n    padding: 0.5rem 0.75rem;\\n    font-size: 0.75rem;\\n    line-height: 1.2;\\n  }\\n  .input-field { height: 40px; font-size: 0.75rem; padding: 0.5rem 0.75rem; }\\n  .send-button { width: 40px; height: 40px; font-size: 0.75rem; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.0625rem; height: 48px; }\\n  .chat-messages { padding: 0 0.25rem; height: calc(100vh - 112px); }\\n  .chat-input-area { padding: 0.25rem; height: 64px; }\\n  .text-3xl { font-size: 0.75rem; line-height: 0.875rem; }\\n  .space-y-3 > * + * { margin-top: 0.125rem; }\\n  .space-y-4 > * + * { margin-top: 0.375rem; }\\n  .message {\\n    max-width: 90%;\\n    padding: 0.375rem 0.625rem;\\n    font-size: 0.65rem;\\n    line-height: 1.1;\\n  }\\n  .input-field { height: 36px; font-size: 0.65rem; padding: 0.25rem 0.5rem; }\\n  .send-button { width: 36px; height: 36px; font-size: 0.65rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n/* Animazione onda per il bordo dell'input */\\n@keyframes gradientWave {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n\\n@keyframes liquidMove {\\n  0% {\\n    background-position: 0% 50%, 100% 50%, 50% 0%, 0% 0%;\\n  }\\n  25% {\\n    background-position: 25% 75%, 75% 25%, 75% 25%, 0% 0%;\\n  }\\n  50% {\\n    background-position: 50% 100%, 50% 0%, 100% 50%, 0% 0%;\\n  }\\n  75% {\\n    background-position: 75% 25%, 25% 75%, 25% 75%, 0% 0%;\\n  }\\n  100% {\\n    background-position: 100% 50%, 0% 50%, 50% 100%, 0% 0%;\\n  }\\n}\\n\\n/* Effetto glow per il bordo input */\\n.input-border-container {\\n  position: relative;\\n}\\n\\n.input-border-container::before {\\n  content: '';\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: inherit;\\n  border-radius: inherit;\\n  filter: blur(8px);\\n  opacity: 0.3;\\n  z-index: -1;\\n  animation: inherit;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css\n"));

/***/ })

});