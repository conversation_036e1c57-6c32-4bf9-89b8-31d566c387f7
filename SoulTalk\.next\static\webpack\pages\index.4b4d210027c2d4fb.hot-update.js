"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        if (chatContainerRef.current) {\n            setTimeout(()=>{\n                if (chatContainerRef.current) {\n                    chatContainerRef.current.scrollTo({\n                        top: chatContainerRef.current.scrollHeight,\n                        behavior: 'smooth'\n                    });\n                }\n            }, 100);\n        }\n    };\n    const addMessage = (content, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString() + Math.random(),\n            content,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Home.useEffect\"], [\n        messages,\n        isLoading\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = inputMessage.trim();\n        setInputMessage('');\n        setIsLoading(true);\n        // Aggiungi immediatamente il messaggio dell'utente\n        const userMsg = addMessage(userMessage, true);\n        console.log('Messaggio utente aggiunto, totale messaggi:', messages.length + 1);\n        // Scroll dopo aver aggiunto il messaggio utente\n        setTimeout(scrollToBottom, 50);\n        try {\n            // Prepara la cronologia per l'API\n            const conversationHistory = [\n                ...messages,\n                userMsg\n            ].map((msg)=>({\n                    role: msg.isUser ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_4__.sendChatMessage)(userMessage, conversationHistory, {\n                name: '',\n                intimacyLevel: 0,\n                traits: [],\n                emotions: []\n            });\n            // Aggiungi la risposta dell'AI\n            addMessage(response, false);\n            console.log('Risposta AI aggiunta, totale messaggi:', messages.length + 2);\n        } catch (error) {\n            console.error('Error:', error);\n            addMessage(\"Scusa, ho avuto un problema tecnico. Riprova!\", false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    const handleReset = ()=>{\n        if (confirm('Ricominciare da capo?')) {\n            resetMemory();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Soul Chat\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: 'white',\n                                    fontSize: '1.5rem',\n                                    fontWeight: 'bold',\n                                    margin: 0\n                                },\n                                children: \"Soul Chat\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                style: {\n                                    color: 'white',\n                                    background: 'none',\n                                    border: 'none',\n                                    cursor: 'pointer'\n                                },\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chatContainerRef,\n                        className: \"chat-messages\",\n                        children: [\n                            memory.conversationHistory.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '2rem',\n                                    color: 'white'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Dimmi, come ti chiami e come ti senti oggi?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    message: message.content,\n                                    isUser: message.isUser\n                                }, message.id, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: '1rem',\n                                    color: 'white'\n                                },\n                                children: \"Soul sta scrivendo...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-input-area\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: inputMessage,\n                                onChange: (e)=>setInputMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Scrivi il tuo messaggio...\",\n                                className: \"input-field\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputMessage.trim() || isLoading,\n                                className: \"send-button\",\n                                children: \"→\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"TgJ61XQ3yyv4U/I9iZlpf3qApTU=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});