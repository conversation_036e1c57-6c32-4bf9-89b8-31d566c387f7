"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GlassFrame */ \"(pages-dir-browser)/./src/components/GlassFrame.tsx\");\n/* harmony import */ var _components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ChatBubble */ \"(pages-dir-browser)/./src/components/ChatBubble.tsx\");\n/* harmony import */ var _components_ChatInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/ChatInput */ \"(pages-dir-browser)/./src/components/ChatInput.tsx\");\n/* harmony import */ var _components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AuraIndicator */ \"(pages-dir-browser)/./src/components/AuraIndicator.tsx\");\n/* harmony import */ var _state_userMemory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/userMemory */ \"(pages-dir-browser)/./src/state/userMemory.ts\");\n/* harmony import */ var _utils_openrouter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/openrouter */ \"(pages-dir-browser)/./src/utils/openrouter.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { memory, isLoaded, addMessage, updateBasicInfo, addTrait, addEmotion, updateIntimacyLevel, getAuraColor, resetMemory } = (0,_state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showWelcome, setShowWelcome] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll automatico verso il basso quando arrivano nuovi messaggi\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (chatContainerRef.current) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Nasconde il messaggio di benvenuto dopo il primo messaggio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (memory.conversationHistory.length > 0) {\n                setShowWelcome(false);\n            }\n        }\n    }[\"Home.useEffect\"], [\n        memory.conversationHistory\n    ]);\n    // Converte la cronologia della memoria in formato OpenRouter\n    const getOpenRouterHistory = ()=>{\n        return memory.conversationHistory.map((msg)=>({\n                role: msg.isUser ? 'user' : 'assistant',\n                content: msg.content\n            }));\n    };\n    // Gestisce l'invio di un nuovo messaggio\n    const handleSendMessage = async (message)=>{\n        if (isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Aggiunge il messaggio dell'utente alla cronologia\n            addMessage(message, true);\n            // Prepara il contesto utente per l'AI\n            const userContext = {\n                name: memory.name || undefined,\n                age: memory.age || undefined,\n                traits: memory.traits,\n                emotions: memory.emotions,\n                intimacyLevel: memory.intimacyLevel\n            };\n            // Invia il messaggio all'AI\n            const aiResponse = await (0,_utils_openrouter__WEBPACK_IMPORTED_MODULE_8__.sendChatMessage)(message, getOpenRouterHistory(), userContext);\n            // Aggiunge la risposta dell'AI alla cronologia\n            addMessage(aiResponse, false);\n            // Analizza il messaggio per estrarre informazioni (simulato per ora)\n            await analyzeAndUpdateMemory(message, aiResponse);\n        } catch (err) {\n            console.error('Errore nell\\'invio del messaggio:', err);\n            setError('Errore nella comunicazione. Riprova tra poco.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Analizza i messaggi e aggiorna la memoria (versione semplificata)\n    const analyzeAndUpdateMemory = async (userMessage, aiResponse)=>{\n        // Analisi semplificata basata su parole chiave\n        const lowerUserMessage = userMessage.toLowerCase();\n        // Estrae informazioni base\n        if (lowerUserMessage.includes('mi chiamo') || lowerUserMessage.includes('sono ')) {\n            const nameMatch = userMessage.match(/mi chiamo (\\w+)|sono (\\w+)/i);\n            if (nameMatch && !memory.name) {\n                const name = nameMatch[1] || nameMatch[2];\n                updateBasicInfo({\n                    name\n                });\n            }\n        }\n        // Estrae età\n        const ageMatch = userMessage.match(/ho (\\d+) anni|(\\d+) anni/);\n        if (ageMatch && !memory.age) {\n            const age = parseInt(ageMatch[1] || ageMatch[2]);\n            if (age > 0 && age < 120) {\n                updateBasicInfo({\n                    age\n                });\n            }\n        }\n        // Rileva emozioni\n        const emotions = {\n            'felice': [\n                'felice',\n                'contento',\n                'gioioso',\n                'allegro'\n            ],\n            'triste': [\n                'triste',\n                'depresso',\n                'giù',\n                'male'\n            ],\n            'arrabbiato': [\n                'arrabbiato',\n                'furioso',\n                'incazzato'\n            ],\n            'preoccupato': [\n                'preoccupato',\n                'ansioso',\n                'nervoso'\n            ],\n            'eccitato': [\n                'eccitato',\n                'entusiasta',\n                'carico'\n            ],\n            'calmo': [\n                'calmo',\n                'tranquillo',\n                'sereno'\n            ]\n        };\n        for (const [emotion, keywords] of Object.entries(emotions)){\n            if (keywords.some((keyword)=>lowerUserMessage.includes(keyword))) {\n                addEmotion(emotion);\n                break;\n            }\n        }\n        // Aggiorna il livello di intimità basato sulla lunghezza e contenuto\n        if (userMessage.length > 100 || lowerUserMessage.includes('personale') || lowerUserMessage.includes('segreto') || lowerUserMessage.includes('famiglia') || lowerUserMessage.includes('amore')) {\n            const currentLevel = memory.intimacyLevel;\n            if (currentLevel < 5) {\n                updateIntimacyLevel(currentLevel + 1);\n            }\n        }\n    };\n    // Gestisce il reset della memoria\n    const handleReset = ()=>{\n        if (confirm('Sei sicuro di voler ricominciare da capo? Tutti i ricordi andranno persi.')) {\n            resetMemory();\n            setShowWelcome(true);\n            setError(null);\n        }\n    };\n    // Mostra loading durante l'idratazione\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"SoulTalk - La tua amicizia AI\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Caricamento SoulTalk...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"SoulTalk - La tua amicizia AI\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Una piattaforma AI che si comporta come un'amicizia da costruire\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"chat-layout\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-header text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"SoulTalk\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"opacity-60 mb-4\",\n                                children: \"La tua amicizia AI che cresce con te\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        intimacyLevel: memory.intimacyLevel,\n                                        showLabel: true,\n                                        className: \"justify-center\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuraIndicator__WEBPACK_IMPORTED_MODULE_6__.AuraProgression, {\n                                        intimacyLevel: memory.intimacyLevel\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlassFrame__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            auraColor: getAuraColor(),\n                            className: \"flex-1 flex flex-col min-h-0 mx-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatContainerRef,\n                                    className: \"chat-container chat-messages flex-1 space-y-4\",\n                                    children: [\n                                        showWelcome && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            style: {\n                                                padding: '2rem 0'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-6 rounded-2xl mx-auto\",\n                                                style: {\n                                                    maxWidth: '28rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-3\",\n                                                        children: \"Ciao, sono Soul \\uD83D\\uDC4B\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm opacity-80\",\n                                                        style: {\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"\\xc8 la prima volta che ci parliamo. Sono qui per conoscerti davvero, non solo per aiutarti. Dimmi, come ti chiami e come ti senti oggi?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        memory.conversationHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatBubble__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                message: message.content,\n                                                isUser: message.isUser,\n                                                timestamp: message.timestamp\n                                            }, message.id, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)),\n                                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '8px',\n                                                                height: '8px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '8px',\n                                                                height: '8px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%',\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-bounce opacity-60\",\n                                                            style: {\n                                                                width: '8px',\n                                                                height: '8px',\n                                                                backgroundColor: 'white',\n                                                                borderRadius: '50%',\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glass-effect p-4 rounded-2xl bg-red-500/20 border-red-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-200 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"chat-input-area\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInput__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        onSendMessage: handleSendMessage,\n                                        disabled: isLoading,\n                                        placeholder: memory.name ? \"Cosa vuoi condividere con Soul, \".concat(memory.name, \"?\") : \"Dimmi qualcosa di te...\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-sm opacity-60 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: memory.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Ciao \",\n                                        memory.name,\n                                        \"! Livello di connessione: \",\n                                        memory.intimacyLevel,\n                                        \"/5\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleReset,\n                                className: \"transition-all duration-200\",\n                                style: {\n                                    cursor: 'pointer'\n                                },\n                                onMouseOver: (e)=>e.target.style.opacity = '1',\n                                onMouseOut: (e)=>e.target.style.opacity = '0.6',\n                                children: \"Ricomincia da capo\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Friends\\\\SoulTalk\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"FML9iZAdtm0rOHu/Dtj/snyE0xQ=\", false, function() {\n    return [\n        _state_userMemory__WEBPACK_IMPORTED_MODULE_7__.useUserMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/pages/index.tsx\n"));

/***/ })

});