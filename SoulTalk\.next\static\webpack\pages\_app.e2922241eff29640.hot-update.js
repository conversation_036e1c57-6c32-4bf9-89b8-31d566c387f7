"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  background-attachment: fixed;\\n  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;\\n  animation: liquidMove 20s ease-in-out infinite;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n.h-full { height: 100%; }\\n.flex-col { flex-direction: column; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  -webkit-backdrop-filter: blur(20px) saturate(180%);\\n          backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n/* Chat Messages */\\n.message-container {\\n  margin-bottom: 0.875rem;\\n  display: flex;\\n}\\n\\n.message-container.user {\\n  justify-content: flex-end;\\n}\\n\\n.message-container.assistant {\\n  justify-content: flex-start;\\n}\\n\\n.message {\\n  max-width: 70%;\\n  padding: 0.625rem 0.875rem;\\n  border-radius: 0.875rem;\\n  word-wrap: break-word;\\n  font-size: 0.8rem;\\n  line-height: 1.3;\\n}\\n\\n.user-message {\\n  background: #007AFF;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.assistant-message {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-bottom-left-radius: 0.25rem;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  flex: 1 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1.375rem;\\n  padding: 0.625rem 0.875rem;\\n  color: white;\\n  font-size: 0.875rem;\\n  resize: none;\\n  height: 44px;\\n  outline: none;\\n  width: 100%;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-button {\\n  background: #007AFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n  font-size: 0.875rem;\\n}\\n\\n.send-button:hover {\\n  background: #0056CC;\\n}\\n\\n.send-button:disabled {\\n  background: rgba(255, 255, 255, 0.2);\\n  cursor: not-allowed;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Chat Container - Full Screen */\\n.chat-container {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  background: rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n\\n/* Header */\\n.chat-header {\\n  height: 56px;\\n  padding: 0.875rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n}\\n\\n/* Messages Area */\\n.chat-messages {\\n  flex: 1 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: calc(100vh - 140px); /* 60px header + 80px input */\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n/* Input Area */\\n.chat-input-area {\\n  height: 80px;\\n  padding: 1rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 85%;\\n    min-width: 6rem;\\n    padding: 0.75rem 1rem;\\n    font-size: 0.875rem;\\n    line-height: 1.3;\\n  }\\n  .chat-main-container { max-width: 100%; margin: 0; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 90%;\\n    min-width: 5rem;\\n    padding: 0.625rem 0.875rem;\\n    font-size: 0.8rem;\\n    line-height: 1.2;\\n  }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n@keyframes liquidMove {\\n  0%, 100% {\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  25% {\\n    background:\\n      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  50% {\\n    background:\\n      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  75% {\\n    background:\\n      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/global.css\"],\"names\":[],\"mappings\":\"AAAA,wBAAwB;AACxB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,iBAAiB;EACjB,YAAY;EACZ;;;;kEAIgE;EAChE,4BAA4B;EAC5B,2DAA2D;EAC3D,8CAA8C;AAChD;;AAEA,oBAAoB;AACpB,QAAQ,aAAa,EAAE;AACvB,YAAY,sBAAsB,EAAE;AACpC,gBAAgB,mBAAmB,EAAE;AACrC,aAAa,qBAAqB,EAAE;AACpC,kBAAkB,uBAAuB,EAAE;AAC3C,mBAAmB,8BAA8B,EAAE;AACnD,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,gBAAgB,iBAAiB,EAAE;AACnC,UAAU,WAAW,EAAE;AACvB,UAAU,YAAY,EAAE;AACxB,mBAAmB,kBAAkB,EAAE;AACvC,uBAAuB,qBAAqB,EAAE;AAC9C,aAAa,gBAAgB,EAAE;AAC/B,WAAW,iBAAiB,EAAE,kBAAkB,EAAE;AAClD,OAAO,aAAa,EAAE;AACtB,OAAO,eAAe,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,sBAAsB,EAAE;AAChC,qBAAqB,kBAAkB,EAAE;AACzC,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,mBAAmB,EAAE;AAC1C,qBAAqB,oBAAoB,EAAE;AAC3C,YAAY,kBAAkB,EAAE,mBAAmB,EAAE;AACrD,WAAW,eAAe,EAAE,oBAAoB,EAAE;AAClD,WAAW,kBAAkB,EAAE,iBAAiB,EAAE;AAClD,WAAW,mBAAmB,EAAE,qBAAqB,EAAE;AACvD,aAAa,gBAAgB,EAAE;AAC/B,iBAAiB,gBAAgB,EAAE;AACnC,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,cAAc,YAAY,EAAE;AAC5B,eAAe,mBAAmB,EAAE;AACpC,cAAc,sBAAsB,EAAE;AACtC,aAAa,mFAAmF,EAAE;AAClG,kBAAkB,0BAA0B,EAAE;AAC9C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,gBAAgB,0BAA0B,EAAE;AAC5C,0BAA0B,sBAAsB,EAAE;AAClD,gBAAgB,kCAAkC,EAAE;AACpD,kBAAkB,6BAA6B,EAAE;AACjD,iBAAiB,yDAAyD,EAAE;AAC5E,UAAU,SAAY,EAAE;AACxB,mBAAmB,gBAAgB,EAAE;AACrC,YAAY,kBAAkB,EAAE;AAChC,YAAY,kBAAkB,EAAE;AAChC,WAAW,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE;AACjD,YAAY,qBAAqB,EAAE;AACnC,UAAU,aAAa,EAAE;AACzB,SAAS,cAAc,EAAE;AACzB,UAAU,YAAY,EAAE;AACxB,YAAY,sBAAsB,EAAE;;AAEpC,0BAA0B;AAC1B;EACE,aAAa,cAAc,EAAE;EAC7B,cAAc,aAAa,EAAE;AAC/B;;AAEA,sBAAsB;AACtB;EACE,kDAA0C;UAA1C,0CAA0C;EAC1C,qCAAqC;EACrC,2CAA2C;EAC3C;;;4CAG0C;EAC1C,mBAAmB;AACrB;;AAEA,kBAAkB;AAClB;EACE,uBAAuB;EACvB,aAAa;AACf;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,2BAA2B;AAC7B;;AAEA;EACE,cAAc;EACd,0BAA0B;EAC1B,uBAAuB;EACvB,qBAAqB;EACrB,iBAAiB;EACjB,gBAAgB;AAClB;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,mCAAmC;AACrC;;AAEA;EACE,oCAAoC;EACpC,YAAY;EACZ,0CAA0C;EAC1C,mCAA2B;UAA3B,2BAA2B;EAC3B,kCAAkC;AACpC;;AAEA;EACE,yDAAyD;EACzD,iBAAiB;AACnB;;AAEA;EACE,SAAO;EACP,oCAAoC;EACpC,0CAA0C;EAC1C,uBAAuB;EACvB,0BAA0B;EAC1B,YAAY;EACZ,mBAAmB;EACnB,YAAY;EACZ,YAAY;EACZ,aAAa;EACb,WAAW;AACb;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,6CAA6C;AAC/C;;AAEA;EACE,mBAAmB;EACnB,YAAY;EACZ,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,yBAAyB;EACzB,cAAc;EACd,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;EACpC,mBAAmB;AACrB;;AAEA;EACE,sBAAsB;EACtB,+CAA+C;AACjD;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA,iCAAiC;AACjC;EACE,aAAa;EACb,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,8BAA8B;EAC9B,mCAA2B;UAA3B,2BAA2B;AAC7B;;AAEA,WAAW;AACX;EACE,YAAY;EACZ,iBAAiB;EACjB,iDAAiD;EACjD,8BAA8B;EAC9B,aAAa;EACb,mBAAmB;AACrB;;AAEA,kBAAkB;AAClB;EACE,SAAO;EACP,gBAAgB;EAChB,aAAa;EACb,qBAAqB;EACrB,wBAAwB;EACxB,2BAA2B,EAAE,6BAA6B;AAC5D;;AAEA;EACE,aAAa;AACf;;AAEA,eAAe;AACf;EACE,YAAY;EACZ,aAAa;EACb,8CAA8C;EAC9C,8BAA8B;EAC9B,aAAa;EACb,mBAAmB;EACnB,WAAW;AACb;;AAEA,4BAA4B;AAC5B;EACE,eAAe,gBAAgB,EAAE;EACjC,iBAAiB,iBAAiB,EAAE;EACpC,mBAAmB,eAAe,EAAE;EACpC,YAAY,eAAe,EAAE,oBAAoB,EAAE;EACnD,WAAW,mBAAmB,EAAE,iBAAiB,EAAE;EACnD,OAAO,gBAAgB,EAAE;EACzB,QAAQ,qBAAqB,EAAE;EAC/B;IACE,cAAc;IACd,eAAe;IACf,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;EAClB;EACA,uBAAuB,eAAe,EAAE,SAAS,EAAE;AACrD;;AAEA;EACE,eAAe,iBAAiB,EAAE;EAClC,iBAAiB,kBAAkB,EAAE;EACrC,mBAAmB,gBAAgB,EAAE;EACrC,YAAY,mBAAmB,EAAE,iBAAiB,EAAE;EACpD,qBAAqB,mBAAmB,EAAE;EAC1C,qBAAqB,kBAAkB,EAAE;EACzC;IACE,cAAc;IACd,eAAe;IACf,0BAA0B;IAC1B,iBAAiB;IACjB,gBAAgB;EAClB;EACA,eAAe,gBAAgB,EAAE,kBAAkB,EAAE,wBAAwB,EAAE;AACjF;;AAEA;EACE,eAAe,cAAc,EAAE,cAAc,EAAE;AACjD;;AAEA;EACE,iBAAiB,eAAe,EAAE;EAClC,mBAAmB,oBAAoB,EAAE;AAC3C;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,0BAA0B;EAC5B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE,OAAO,uBAAuB,EAAE;EAChC,KAAK,yBAAyB,EAAE;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,qDAAqD;EACvD;EACA;IACE,wBAAwB;IACxB,qDAAqD;EACvD;AACF;;AAEA;EACE,WAAW,UAAU,EAAE;EACvB,MAAM,YAAY,EAAE;AACtB;;AAEA;EACE;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;EACA;IACE;;;;oEAIgE;EAClE;AACF\",\"sourcesContent\":[\"/* Reset e base styles */\\n* {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\nhtml {\\n  font-family: 'Inter', system-ui, sans-serif;\\n}\\n\\nbody {\\n  min-height: 100vh;\\n  color: white;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n    linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  background-attachment: fixed;\\n  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;\\n  animation: liquidMove 20s ease-in-out infinite;\\n}\\n\\n/* Utility Classes */\\n.flex { display: flex; }\\n.flex-col { flex-direction: column; }\\n.items-center { align-items: center; }\\n.items-end { align-items: flex-end; }\\n.justify-center { justify-content: center; }\\n.justify-between { justify-content: space-between; }\\n.text-center { text-align: center; }\\n.text-left { text-align: left; }\\n.min-h-screen { min-height: 100vh; }\\n.w-full { width: 100%; }\\n.h-full { height: 100%; }\\n.leading-relaxed { line-height: 1.625; }\\n.whitespace-pre-wrap { white-space: pre-wrap; }\\n.max-w-4xl { max-width: 56rem; }\\n.mx-auto { margin-left: auto; margin-right: auto; }\\n.p-4 { padding: 1rem; }\\n.p-6 { padding: 1.5rem; }\\n.mb-2 { margin-bottom: 0.25rem; }\\n.mb-4 { margin-bottom: 0.5rem; }\\n.mb-6 { margin-bottom: 0.75rem; }\\n.space-y-3 > * + * { margin-top: 0.5rem; }\\n.space-y-4 > * + * { margin-top: 0.75rem; }\\n.space-x-2 > * + * { margin-left: 0.5rem; }\\n.space-x-3 > * + * { margin-left: 0.75rem; }\\n.text-3xl { font-size: 1.25rem; line-height: 1.5rem; }\\n.text-xl { font-size: 1rem; line-height: 1.25rem; }\\n.text-sm { font-size: 0.75rem; line-height: 1rem; }\\n.text-xs { font-size: 0.625rem; line-height: 0.875rem; }\\n.font-bold { font-weight: 700; }\\n.font-semibold { font-weight: 600; }\\n.text-white { color: white; }\\n.opacity-50 { opacity: 0.5; }\\n.opacity-60 { opacity: 0.6; }\\n.opacity-80 { opacity: 0.8; }\\n.rounded-2xl { border-radius: 1rem; }\\n.rounded-xl { border-radius: 0.75rem; }\\n.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }\\n.transition-all { transition: all 0.15s ease; }\\n.duration-200 { transition-duration: 200ms; }\\n.duration-300 { transition-duration: 300ms; }\\n.duration-500 { transition-duration: 500ms; }\\n.hover\\\\:scale-105:hover { transform: scale(1.05); }\\n.animate-spin { animation: spin 1s linear infinite; }\\n.animate-bounce { animation: bounce 1s infinite; }\\n.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }\\n.flex-1 { flex: 1 1 0%; }\\n.overflow-y-auto { overflow-y: auto; }\\n.relative { position: relative; }\\n.absolute { position: absolute; }\\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\\n.border-t { border-top-width: 1px; }\\n.hidden { display: none; }\\n.block { display: block; }\\n.h-full { height: 100%; }\\n.flex-col { flex-direction: column; }\\n\\n/* Responsive visibility */\\n@media (min-width: 640px) {\\n  .sm\\\\:block { display: block; }\\n  .sm\\\\:hidden { display: none; }\\n}\\n\\n/* Component Classes */\\n.glass-effect {\\n  backdrop-filter: blur(20px) saturate(180%);\\n  background: rgba(255, 255, 255, 0.08);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow:\\n    0 8px 32px rgba(0, 0, 0, 0.12),\\n    inset 0 1px 0 rgba(255, 255, 255, 0.2),\\n    inset 0 -1px 0 rgba(255, 255, 255, 0.05);\\n  border-radius: 1rem;\\n}\\n\\n/* Chat Messages */\\n.message-container {\\n  margin-bottom: 0.875rem;\\n  display: flex;\\n}\\n\\n.message-container.user {\\n  justify-content: flex-end;\\n}\\n\\n.message-container.assistant {\\n  justify-content: flex-start;\\n}\\n\\n.message {\\n  max-width: 70%;\\n  padding: 0.625rem 0.875rem;\\n  border-radius: 0.875rem;\\n  word-wrap: break-word;\\n  font-size: 0.8rem;\\n  line-height: 1.3;\\n}\\n\\n.user-message {\\n  background: #007AFF;\\n  color: white;\\n  border-bottom-right-radius: 0.25rem;\\n}\\n\\n.assistant-message {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  backdrop-filter: blur(10px);\\n  border-bottom-left-radius: 0.25rem;\\n}\\n\\n.aura-animation {\\n  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n  filter: blur(1px);\\n}\\n\\n.input-field {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 1.375rem;\\n  padding: 0.625rem 0.875rem;\\n  color: white;\\n  font-size: 0.875rem;\\n  resize: none;\\n  height: 44px;\\n  outline: none;\\n  width: 100%;\\n}\\n\\n.input-field::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.input-field:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-button {\\n  background: #007AFF;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n  font-size: 0.875rem;\\n}\\n\\n.send-button:hover {\\n  background: #0056CC;\\n}\\n\\n.send-button:disabled {\\n  background: rgba(255, 255, 255, 0.2);\\n  cursor: not-allowed;\\n}\\n\\n.btn:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn:active {\\n  transform: scale(0.95);\\n}\\n\\n.btn:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.btn:disabled:hover {\\n  transform: none;\\n}\\n\\n/* Chat Container - Full Screen */\\n.chat-container {\\n  height: 100vh;\\n  width: 100vw;\\n  display: flex;\\n  flex-direction: column;\\n  background: rgba(0, 0, 0, 0.1);\\n  backdrop-filter: blur(20px);\\n}\\n\\n/* Header */\\n.chat-header {\\n  height: 56px;\\n  padding: 0.875rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n}\\n\\n/* Messages Area */\\n.chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1rem;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  height: calc(100vh - 140px); /* 60px header + 80px input */\\n}\\n\\n.chat-messages::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n/* Input Area */\\n.chat-input-area {\\n  height: 80px;\\n  padding: 1rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n  background: rgba(0, 0, 0, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n/* Mobile First Responsive */\\n@media (max-width: 640px) {\\n  .chat-header { padding: 0.25rem; }\\n  .chat-messages { padding: 0 0.5rem; }\\n  .chat-input-area { padding: 0.5rem; }\\n  .text-3xl { font-size: 1rem; line-height: 1.25rem; }\\n  .text-xl { font-size: 0.875rem; line-height: 1rem; }\\n  .p-6 { padding: 0.75rem; }\\n  .mb-6 { margin-bottom: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 85%;\\n    min-width: 6rem;\\n    padding: 0.75rem 1rem;\\n    font-size: 0.875rem;\\n    line-height: 1.3;\\n  }\\n  .chat-main-container { max-width: 100%; margin: 0; }\\n}\\n\\n@media (max-width: 480px) {\\n  .chat-header { padding: 0.125rem; }\\n  .chat-messages { padding: 0 0.25rem; }\\n  .chat-input-area { padding: 0.25rem; }\\n  .text-3xl { font-size: 0.875rem; line-height: 1rem; }\\n  .space-y-3 > * + * { margin-top: 0.25rem; }\\n  .space-y-4 > * + * { margin-top: 0.5rem; }\\n  .chat-bubble {\\n    max-width: 90%;\\n    min-width: 5rem;\\n    padding: 0.625rem 0.875rem;\\n    font-size: 0.8rem;\\n    line-height: 1.2;\\n  }\\n  .input-field { min-height: 32px; font-size: 0.75rem; padding: 0.375rem 0.5rem; }\\n}\\n\\n@media (min-width: 768px) {\\n  .chat-layout { max-width: 4xl; margin: 0 auto; }\\n}\\n\\n@media (min-width: 1024px) {\\n  .chat-messages { padding: 0 2rem; }\\n  .chat-input-area { padding: 1.5rem 2rem; }\\n}\\n\\n/* Keyframe Animations */\\n@keyframes slideInFromBottom {\\n  from {\\n    opacity: 0;\\n    transform: translateY(8px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n@keyframes bounce {\\n  0%, 100% {\\n    transform: translateY(-25%);\\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n  }\\n  50% {\\n    transform: translateY(0);\\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n  }\\n}\\n\\n@keyframes pulse {\\n  0%, 100% { opacity: 1; }\\n  50% { opacity: 0.5; }\\n}\\n\\n@keyframes liquidMove {\\n  0%, 100% {\\n    background:\\n      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  25% {\\n    background:\\n      radial-gradient(circle at 60% 30%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 60%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  50% {\\n    background:\\n      radial-gradient(circle at 80% 60%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 20% 40%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n      radial-gradient(circle at 60% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n  75% {\\n    background:\\n      radial-gradient(circle at 40% 70%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 70% 30%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),\\n      radial-gradient(circle at 30% 20%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),\\n      linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzEzXS51c2VbMV0hLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbN10ub25lT2ZbMTNdLnVzZVsyXSEuL3NyYy9zdHlsZXMvZ2xvYmFsLmNzcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMySDtBQUMzSCw4QkFBOEIsa0hBQTJCO0FBQ3pEO0FBQ0Esd0VBQXdFLGNBQWMsZUFBZSwyQkFBMkIsR0FBRyxVQUFVLGdEQUFnRCxHQUFHLFVBQVUsc0JBQXNCLGlCQUFpQix5VkFBeVYsaUNBQWlDLGdFQUFnRSxtREFBbUQsR0FBRyxtQ0FBbUMsZ0JBQWdCLGNBQWMseUJBQXlCLGtCQUFrQixzQkFBc0IsZUFBZSx3QkFBd0Isb0JBQW9CLDBCQUEwQixxQkFBcUIsaUNBQWlDLGlCQUFpQixxQkFBcUIsZUFBZSxtQkFBbUIsa0JBQWtCLG9CQUFvQixZQUFZLGNBQWMsWUFBWSxlQUFlLHFCQUFxQixxQkFBcUIseUJBQXlCLHdCQUF3QixlQUFlLG1CQUFtQixhQUFhLG1CQUFtQixxQkFBcUIsU0FBUyxnQkFBZ0IsU0FBUyxrQkFBa0IsVUFBVSx5QkFBeUIsVUFBVSx3QkFBd0IsVUFBVSx5QkFBeUIsdUJBQXVCLHFCQUFxQix1QkFBdUIsc0JBQXNCLHVCQUF1QixzQkFBc0IsdUJBQXVCLHVCQUF1QixjQUFjLG9CQUFvQixzQkFBc0IsYUFBYSxpQkFBaUIsdUJBQXVCLGFBQWEsb0JBQW9CLG9CQUFvQixhQUFhLHFCQUFxQix3QkFBd0IsZUFBZSxtQkFBbUIsbUJBQW1CLG1CQUFtQixnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxpQkFBaUIsc0JBQXNCLGdCQUFnQix5QkFBeUIsZUFBZSxzRkFBc0Ysb0JBQW9CLDZCQUE2QixrQkFBa0IsNkJBQTZCLGtCQUFrQiw2QkFBNkIsa0JBQWtCLDZCQUE2Qiw2QkFBNkIseUJBQXlCLGtCQUFrQixxQ0FBcUMsb0JBQW9CLGdDQUFnQyxtQkFBbUIsNERBQTRELFlBQVksWUFBWSxxQkFBcUIsbUJBQW1CLGNBQWMscUJBQXFCLGNBQWMscUJBQXFCLGFBQWEsUUFBUSxVQUFVLFdBQVcsVUFBVSxjQUFjLHdCQUF3QixZQUFZLGdCQUFnQixXQUFXLGlCQUFpQixZQUFZLGVBQWUsY0FBYyx5QkFBeUIsNERBQTRELGtCQUFrQixpQkFBaUIsbUJBQW1CLGdCQUFnQixHQUFHLDRDQUE0Qyx1REFBdUQsdURBQXVELDBDQUEwQyxnREFBZ0QsZ0pBQWdKLHdCQUF3QixHQUFHLDZDQUE2Qyw0QkFBNEIsa0JBQWtCLEdBQUcsNkJBQTZCLDhCQUE4QixHQUFHLGtDQUFrQyxnQ0FBZ0MsR0FBRyxjQUFjLG1CQUFtQiwrQkFBK0IsNEJBQTRCLDBCQUEwQixzQkFBc0IscUJBQXFCLEdBQUcsbUJBQW1CLHdCQUF3QixpQkFBaUIsd0NBQXdDLEdBQUcsd0JBQXdCLHlDQUF5QyxpQkFBaUIsK0NBQStDLHdDQUF3Qyx3Q0FBd0MsdUNBQXVDLEdBQUcscUJBQXFCLDhEQUE4RCxzQkFBc0IsR0FBRyxrQkFBa0IsY0FBYyx5Q0FBeUMsK0NBQStDLDRCQUE0QiwrQkFBK0IsaUJBQWlCLHdCQUF3QixpQkFBaUIsaUJBQWlCLGtCQUFrQixnQkFBZ0IsR0FBRywrQkFBK0Isb0NBQW9DLEdBQUcsd0JBQXdCLGtCQUFrQixrREFBa0QsR0FBRyxrQkFBa0Isd0JBQXdCLGlCQUFpQix1QkFBdUIsZ0JBQWdCLGlCQUFpQixpQkFBaUIsb0JBQW9CLGtCQUFrQix3QkFBd0IsNEJBQTRCLDhCQUE4QixtQkFBbUIsd0JBQXdCLEdBQUcsd0JBQXdCLHdCQUF3QixHQUFHLDJCQUEyQix5Q0FBeUMsd0JBQXdCLEdBQUcsZ0JBQWdCLDJCQUEyQixvREFBb0QsR0FBRyxpQkFBaUIsMkJBQTJCLEdBQUcsbUJBQW1CLGlCQUFpQix3QkFBd0IsR0FBRyx5QkFBeUIsb0JBQW9CLEdBQUcseURBQXlELGtCQUFrQixpQkFBaUIsa0JBQWtCLDJCQUEyQixtQ0FBbUMsd0NBQXdDLHdDQUF3QyxHQUFHLGdDQUFnQyxpQkFBaUIsc0JBQXNCLHNEQUFzRCxtQ0FBbUMsa0JBQWtCLHdCQUF3QixHQUFHLHlDQUF5QyxjQUFjLHFCQUFxQixrQkFBa0IsMEJBQTBCLDZCQUE2QixpQ0FBaUMsaUNBQWlDLHVDQUF1QyxrQkFBa0IsR0FBRyx3Q0FBd0MsaUJBQWlCLGtCQUFrQixtREFBbUQsbUNBQW1DLGtCQUFrQix3QkFBd0IsZ0JBQWdCLEdBQUcsOERBQThELG1CQUFtQixtQkFBbUIscUJBQXFCLG9CQUFvQix1QkFBdUIsa0JBQWtCLGdCQUFnQixpQkFBaUIsdUJBQXVCLGVBQWUscUJBQXFCLG9CQUFvQixXQUFXLG1CQUFtQixZQUFZLHdCQUF3QixrQkFBa0IscUJBQXFCLHNCQUFzQiw0QkFBNEIsMEJBQTBCLHVCQUF1QixLQUFLLDJCQUEyQixpQkFBaUIsWUFBWSxHQUFHLCtCQUErQixtQkFBbUIsb0JBQW9CLHFCQUFxQixxQkFBcUIsdUJBQXVCLG1CQUFtQixnQkFBZ0IscUJBQXFCLG9CQUFvQix5QkFBeUIsc0JBQXNCLHlCQUF5QixxQkFBcUIsa0JBQWtCLHFCQUFxQixzQkFBc0IsaUNBQWlDLHdCQUF3Qix1QkFBdUIsS0FBSyxtQkFBbUIsa0JBQWtCLG9CQUFvQiwyQkFBMkIsR0FBRywrQkFBK0IsbUJBQW1CLGdCQUFnQixpQkFBaUIsR0FBRyxnQ0FBZ0MscUJBQXFCLGtCQUFrQix1QkFBdUIsdUJBQXVCLEdBQUcsNkRBQTZELFVBQVUsaUJBQWlCLGlDQUFpQyxLQUFLLFFBQVEsaUJBQWlCLCtCQUErQixLQUFLLEdBQUcscUJBQXFCLFdBQVcsMEJBQTBCLFNBQVMsNEJBQTRCLEdBQUcsdUJBQXVCLGNBQWMsa0NBQWtDLDREQUE0RCxLQUFLLFNBQVMsK0JBQStCLDREQUE0RCxLQUFLLEdBQUcsc0JBQXNCLGVBQWUsYUFBYSxVQUFVLGVBQWUsR0FBRywyQkFBMkIsY0FBYyxtV0FBbVcsS0FBSyxTQUFTLG1XQUFtVyxLQUFLLFNBQVMsbVdBQW1XLEtBQUssU0FBUyxtV0FBbVcsS0FBSyxHQUFHLFNBQVMsNkZBQTZGLE1BQU0sVUFBVSxVQUFVLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksV0FBVyxRQUFRLE9BQU8sYUFBYSxhQUFhLGFBQWEsT0FBTyxZQUFZLHFCQUFxQix1QkFBdUIseUJBQXlCLHVCQUF1Qix5QkFBeUIseUJBQXlCLHVCQUF1Qix1QkFBdUIseUJBQXlCLHFCQUFxQixxQkFBcUIseUJBQXlCLHlCQUF5Qix1QkFBdUIsbUNBQW1DLHFCQUFxQixxQkFBcUIsdUJBQXVCLHVCQUF1Qix1QkFBdUIseUJBQXlCLHlCQUF5Qix5QkFBeUIseUJBQXlCLG1DQUFtQyxpQ0FBaUMsbUNBQW1DLG1DQUFtQyx1QkFBdUIseUJBQXlCLHFCQUFxQixxQkFBcUIscUJBQXFCLHFCQUFxQix1QkFBdUIsdUJBQXVCLHVCQUF1Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix5QkFBeUIseUJBQXlCLHFCQUFxQix5QkFBeUIsdUJBQXVCLHVCQUF1QixtREFBbUQsdUJBQXVCLHFCQUFxQixxQkFBcUIscUJBQXFCLHdCQUF3QixhQUFhLE1BQU0sb0JBQW9CLHFCQUFxQixPQUFPLFlBQVksTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLFFBQVEsT0FBTyxhQUFhLE9BQU8sWUFBWSxNQUFNLFlBQVksV0FBVyxNQUFNLEtBQUssWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLEtBQUssVUFBVSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsT0FBTyxLQUFLLFlBQVksV0FBVyxZQUFZLE9BQU8sS0FBSyxZQUFZLFdBQVcsWUFBWSxhQUFhLGFBQWEsYUFBYSxPQUFPLEtBQUssWUFBWSxhQUFhLE9BQU8sS0FBSyxVQUFVLFlBQVksYUFBYSxhQUFhLGFBQWEsV0FBVyxZQUFZLFdBQVcsVUFBVSxVQUFVLFVBQVUsTUFBTSxLQUFLLFlBQVksT0FBTyxLQUFLLFVBQVUsWUFBWSxPQUFPLEtBQUssWUFBWSxXQUFXLFlBQVksV0FBVyxVQUFVLFVBQVUsVUFBVSxVQUFVLFlBQVksYUFBYSxhQUFhLFdBQVcsWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLEtBQUssWUFBWSxhQUFhLE9BQU8sS0FBSyxZQUFZLGFBQWEsT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFVBQVUsWUFBWSxPQUFPLEtBQUssVUFBVSxPQUFPLFlBQVksTUFBTSxVQUFVLFVBQVUsVUFBVSxZQUFZLGFBQWEsYUFBYSxhQUFhLE9BQU8sVUFBVSxLQUFLLFVBQVUsWUFBWSxhQUFhLGFBQWEsV0FBVyxZQUFZLE9BQU8sWUFBWSxNQUFNLFVBQVUsWUFBWSxXQUFXLFlBQVksYUFBYSx5QkFBeUIsT0FBTyxLQUFLLFVBQVUsTUFBTSxVQUFVLEtBQUssVUFBVSxVQUFVLFlBQVksYUFBYSxXQUFXLFlBQVksV0FBVyxNQUFNLFlBQVksTUFBTSxzQkFBc0IseUJBQXlCLHVCQUF1QixpQ0FBaUMsbUNBQW1DLHVCQUF1Qix1QkFBdUIsTUFBTSxVQUFVLFVBQVUsWUFBWSxhQUFhLGFBQWEsTUFBTSxnQ0FBZ0MsT0FBTyxLQUFLLHNCQUFzQix5QkFBeUIseUJBQXlCLG1DQUFtQyx5QkFBeUIseUJBQXlCLE1BQU0sVUFBVSxVQUFVLFlBQVksYUFBYSxhQUFhLE1BQU0sOENBQThDLE9BQU8sS0FBSyw4QkFBOEIsT0FBTyxLQUFLLHNCQUFzQix5QkFBeUIsT0FBTyxZQUFZLE1BQU0sS0FBSyxVQUFVLFlBQVksTUFBTSxLQUFLLFVBQVUsWUFBWSxNQUFNLE1BQU0sS0FBSyxzQkFBc0IsdUJBQXVCLE9BQU8sS0FBSyxLQUFLLFlBQVksYUFBYSxNQUFNLEtBQUssWUFBWSxhQUFhLE1BQU0sTUFBTSxLQUFLLG9CQUFvQixxQkFBcUIsT0FBTyxLQUFLLEtBQUssUUFBUSxPQUFPLE1BQU0sS0FBSyxRQUFRLE9BQU8sTUFBTSxLQUFLLFFBQVEsT0FBTyxNQUFNLEtBQUssUUFBUSxPQUFPLE1BQU0sdURBQXVELGNBQWMsZUFBZSwyQkFBMkIsR0FBRyxVQUFVLGdEQUFnRCxHQUFHLFVBQVUsc0JBQXNCLGlCQUFpQix5VkFBeVYsaUNBQWlDLGdFQUFnRSxtREFBbUQsR0FBRyxtQ0FBbUMsZ0JBQWdCLGNBQWMseUJBQXlCLGtCQUFrQixzQkFBc0IsZUFBZSx3QkFBd0Isb0JBQW9CLDBCQUEwQixxQkFBcUIsaUNBQWlDLGlCQUFpQixxQkFBcUIsZUFBZSxtQkFBbUIsa0JBQWtCLG9CQUFvQixZQUFZLGNBQWMsWUFBWSxlQUFlLHFCQUFxQixxQkFBcUIseUJBQXlCLHdCQUF3QixlQUFlLG1CQUFtQixhQUFhLG1CQUFtQixxQkFBcUIsU0FBUyxnQkFBZ0IsU0FBUyxrQkFBa0IsVUFBVSx5QkFBeUIsVUFBVSx3QkFBd0IsVUFBVSx5QkFBeUIsdUJBQXVCLHFCQUFxQix1QkFBdUIsc0JBQXNCLHVCQUF1QixzQkFBc0IsdUJBQXVCLHVCQUF1QixjQUFjLG9CQUFvQixzQkFBc0IsYUFBYSxpQkFBaUIsdUJBQXVCLGFBQWEsb0JBQW9CLG9CQUFvQixhQUFhLHFCQUFxQix3QkFBd0IsZUFBZSxtQkFBbUIsbUJBQW1CLG1CQUFtQixnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxnQkFBZ0IsZUFBZSxpQkFBaUIsc0JBQXNCLGdCQUFnQix5QkFBeUIsZUFBZSxzRkFBc0Ysb0JBQW9CLDZCQUE2QixrQkFBa0IsNkJBQTZCLGtCQUFrQiw2QkFBNkIsa0JBQWtCLDZCQUE2Qiw2QkFBNkIseUJBQXlCLGtCQUFrQixxQ0FBcUMsb0JBQW9CLGdDQUFnQyxtQkFBbUIsNERBQTRELFlBQVksZUFBZSxxQkFBcUIsbUJBQW1CLGNBQWMscUJBQXFCLGNBQWMscUJBQXFCLGFBQWEsUUFBUSxVQUFVLFdBQVcsVUFBVSxjQUFjLHdCQUF3QixZQUFZLGdCQUFnQixXQUFXLGlCQUFpQixZQUFZLGVBQWUsY0FBYyx5QkFBeUIsNERBQTRELGtCQUFrQixpQkFBaUIsbUJBQW1CLGdCQUFnQixHQUFHLDRDQUE0QywrQ0FBK0MsMENBQTBDLGdEQUFnRCxnSkFBZ0osd0JBQXdCLEdBQUcsNkNBQTZDLDRCQUE0QixrQkFBa0IsR0FBRyw2QkFBNkIsOEJBQThCLEdBQUcsa0NBQWtDLGdDQUFnQyxHQUFHLGNBQWMsbUJBQW1CLCtCQUErQiw0QkFBNEIsMEJBQTBCLHNCQUFzQixxQkFBcUIsR0FBRyxtQkFBbUIsd0JBQXdCLGlCQUFpQix3Q0FBd0MsR0FBRyx3QkFBd0IseUNBQXlDLGlCQUFpQiwrQ0FBK0MsZ0NBQWdDLHVDQUF1QyxHQUFHLHFCQUFxQiw4REFBOEQsc0JBQXNCLEdBQUcsa0JBQWtCLFlBQVkseUNBQXlDLCtDQUErQyw0QkFBNEIsK0JBQStCLGlCQUFpQix3QkFBd0IsaUJBQWlCLGlCQUFpQixrQkFBa0IsZ0JBQWdCLEdBQUcsK0JBQStCLG9DQUFvQyxHQUFHLHdCQUF3QixrQkFBa0Isa0RBQWtELEdBQUcsa0JBQWtCLHdCQUF3QixpQkFBaUIsdUJBQXVCLGdCQUFnQixpQkFBaUIsaUJBQWlCLG9CQUFvQixrQkFBa0Isd0JBQXdCLDRCQUE0Qiw4QkFBOEIsbUJBQW1CLHdCQUF3QixHQUFHLHdCQUF3Qix3QkFBd0IsR0FBRywyQkFBMkIseUNBQXlDLHdCQUF3QixHQUFHLGdCQUFnQiwyQkFBMkIsb0RBQW9ELEdBQUcsaUJBQWlCLDJCQUEyQixHQUFHLG1CQUFtQixpQkFBaUIsd0JBQXdCLEdBQUcseUJBQXlCLG9CQUFvQixHQUFHLHlEQUF5RCxrQkFBa0IsaUJBQWlCLGtCQUFrQiwyQkFBMkIsbUNBQW1DLGdDQUFnQyxHQUFHLGdDQUFnQyxpQkFBaUIsc0JBQXNCLHNEQUFzRCxtQ0FBbUMsa0JBQWtCLHdCQUF3QixHQUFHLHlDQUF5QyxZQUFZLHFCQUFxQixrQkFBa0IsMEJBQTBCLDZCQUE2QixpQ0FBaUMsaUNBQWlDLHVDQUF1QyxrQkFBa0IsR0FBRyx3Q0FBd0MsaUJBQWlCLGtCQUFrQixtREFBbUQsbUNBQW1DLGtCQUFrQix3QkFBd0IsZ0JBQWdCLEdBQUcsOERBQThELG1CQUFtQixtQkFBbUIscUJBQXFCLG9CQUFvQix1QkFBdUIsa0JBQWtCLGdCQUFnQixpQkFBaUIsdUJBQXVCLGVBQWUscUJBQXFCLG9CQUFvQixXQUFXLG1CQUFtQixZQUFZLHdCQUF3QixrQkFBa0IscUJBQXFCLHNCQUFzQiw0QkFBNEIsMEJBQTBCLHVCQUF1QixLQUFLLDJCQUEyQixpQkFBaUIsWUFBWSxHQUFHLCtCQUErQixtQkFBbUIsb0JBQW9CLHFCQUFxQixxQkFBcUIsdUJBQXVCLG1CQUFtQixnQkFBZ0IscUJBQXFCLG9CQUFvQix5QkFBeUIsc0JBQXNCLHlCQUF5QixxQkFBcUIsa0JBQWtCLHFCQUFxQixzQkFBc0IsaUNBQWlDLHdCQUF3Qix1QkFBdUIsS0FBSyxtQkFBbUIsa0JBQWtCLG9CQUFvQiwyQkFBMkIsR0FBRywrQkFBK0IsbUJBQW1CLGdCQUFnQixpQkFBaUIsR0FBRyxnQ0FBZ0MscUJBQXFCLGtCQUFrQix1QkFBdUIsdUJBQXVCLEdBQUcsNkRBQTZELFVBQVUsaUJBQWlCLGlDQUFpQyxLQUFLLFFBQVEsaUJBQWlCLCtCQUErQixLQUFLLEdBQUcscUJBQXFCLFdBQVcsMEJBQTBCLFNBQVMsNEJBQTRCLEdBQUcsdUJBQXVCLGNBQWMsa0NBQWtDLDREQUE0RCxLQUFLLFNBQVMsK0JBQStCLDREQUE0RCxLQUFLLEdBQUcsc0JBQXNCLGVBQWUsYUFBYSxVQUFVLGVBQWUsR0FBRywyQkFBMkIsY0FBYyxtV0FBbVcsS0FBSyxTQUFTLG1XQUFtVyxLQUFLLFNBQVMsbVdBQW1XLEtBQUssU0FBUyxtV0FBbVcsS0FBSyxHQUFHLHFCQUFxQjtBQUN4K3VCO0FBQ0EsaUVBQWUsdUJBQXVCLEVBQUMiLCJzb3VyY2VzIjpbIkc6XFxGcmllbmRzXFxTb3VsVGFsa1xcc3JjXFxzdHlsZXNcXGdsb2JhbC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSW1wb3J0c1xuaW1wb3J0IF9fX0NTU19MT0FERVJfQVBJX0lNUE9SVF9fXyBmcm9tIFwiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvY3NzLWxvYWRlci9zcmMvcnVudGltZS9hcGkuanNcIjtcbnZhciBfX19DU1NfTE9BREVSX0VYUE9SVF9fXyA9IF9fX0NTU19MT0FERVJfQVBJX0lNUE9SVF9fXyh0cnVlKTtcbi8vIE1vZHVsZVxuX19fQ1NTX0xPQURFUl9FWFBPUlRfX18ucHVzaChbbW9kdWxlLmlkLCBcIi8qIFJlc2V0IGUgYmFzZSBzdHlsZXMgKi9cXG4qIHtcXG4gIG1hcmdpbjogMDtcXG4gIHBhZGRpbmc6IDA7XFxuICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xcbn1cXG5cXG5odG1sIHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxufVxcblxcbmJvZHkge1xcbiAgbWluLWhlaWdodDogMTAwdmg7XFxuICBjb2xvcjogd2hpdGU7XFxuICBiYWNrZ3JvdW5kOlxcbiAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA4MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNDAlIDQwJSwgcmdiYSgxMjAsIDIxOSwgMjU1LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIGJhY2tncm91bmQtYXR0YWNobWVudDogZml4ZWQ7XFxuICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJSwgMTAwJSAxMDAlLCAxMDAlIDEwMCUsIDEwMCUgMTAwJTtcXG4gIGFuaW1hdGlvbjogbGlxdWlkTW92ZSAyMHMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XFxufVxcblxcbi8qIFV0aWxpdHkgQ2xhc3NlcyAqL1xcbi5mbGV4IHsgZGlzcGxheTogZmxleDsgfVxcbi5mbGV4LWNvbCB7IGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47IH1cXG4uaXRlbXMtY2VudGVyIHsgYWxpZ24taXRlbXM6IGNlbnRlcjsgfVxcbi5pdGVtcy1lbmQgeyBhbGlnbi1pdGVtczogZmxleC1lbmQ7IH1cXG4uanVzdGlmeS1jZW50ZXIgeyBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgfVxcbi5qdXN0aWZ5LWJldHdlZW4geyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47IH1cXG4udGV4dC1jZW50ZXIgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IH1cXG4udGV4dC1sZWZ0IHsgdGV4dC1hbGlnbjogbGVmdDsgfVxcbi5taW4taC1zY3JlZW4geyBtaW4taGVpZ2h0OiAxMDB2aDsgfVxcbi53LWZ1bGwgeyB3aWR0aDogMTAwJTsgfVxcbi5oLWZ1bGwgeyBoZWlnaHQ6IDEwMCU7IH1cXG4ubGVhZGluZy1yZWxheGVkIHsgbGluZS1oZWlnaHQ6IDEuNjI1OyB9XFxuLndoaXRlc3BhY2UtcHJlLXdyYXAgeyB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7IH1cXG4ubWF4LXctNHhsIHsgbWF4LXdpZHRoOiA1NnJlbTsgfVxcbi5teC1hdXRvIHsgbWFyZ2luLWxlZnQ6IGF1dG87IG1hcmdpbi1yaWdodDogYXV0bzsgfVxcbi5wLTQgeyBwYWRkaW5nOiAxcmVtOyB9XFxuLnAtNiB7IHBhZGRpbmc6IDEuNXJlbTsgfVxcbi5tYi0yIHsgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTsgfVxcbi5tYi00IHsgbWFyZ2luLWJvdHRvbTogMC41cmVtOyB9XFxuLm1iLTYgeyBtYXJnaW4tYm90dG9tOiAwLjc1cmVtOyB9XFxuLnNwYWNlLXktMyA+ICogKyAqIHsgbWFyZ2luLXRvcDogMC41cmVtOyB9XFxuLnNwYWNlLXktNCA+ICogKyAqIHsgbWFyZ2luLXRvcDogMC43NXJlbTsgfVxcbi5zcGFjZS14LTIgPiAqICsgKiB7IG1hcmdpbi1sZWZ0OiAwLjVyZW07IH1cXG4uc3BhY2UteC0zID4gKiArICogeyBtYXJnaW4tbGVmdDogMC43NXJlbTsgfVxcbi50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMS4yNXJlbTsgbGluZS1oZWlnaHQ6IDEuNXJlbTsgfVxcbi50ZXh0LXhsIHsgZm9udC1zaXplOiAxcmVtOyBsaW5lLWhlaWdodDogMS4yNXJlbTsgfVxcbi50ZXh0LXNtIHsgZm9udC1zaXplOiAwLjc1cmVtOyBsaW5lLWhlaWdodDogMXJlbTsgfVxcbi50ZXh0LXhzIHsgZm9udC1zaXplOiAwLjYyNXJlbTsgbGluZS1oZWlnaHQ6IDAuODc1cmVtOyB9XFxuLmZvbnQtYm9sZCB7IGZvbnQtd2VpZ2h0OiA3MDA7IH1cXG4uZm9udC1zZW1pYm9sZCB7IGZvbnQtd2VpZ2h0OiA2MDA7IH1cXG4udGV4dC13aGl0ZSB7IGNvbG9yOiB3aGl0ZTsgfVxcbi5vcGFjaXR5LTUwIHsgb3BhY2l0eTogMC41OyB9XFxuLm9wYWNpdHktNjAgeyBvcGFjaXR5OiAwLjY7IH1cXG4ub3BhY2l0eS04MCB7IG9wYWNpdHk6IDAuODsgfVxcbi5yb3VuZGVkLTJ4bCB7IGJvcmRlci1yYWRpdXM6IDFyZW07IH1cXG4ucm91bmRlZC14bCB7IGJvcmRlci1yYWRpdXM6IDAuNzVyZW07IH1cXG4uc2hhZG93LWxnIHsgYm94LXNoYWRvdzogMCAxMHB4IDE1cHggLTNweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgNHB4IDZweCAtMnB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7IH1cXG4udHJhbnNpdGlvbi1hbGwgeyB0cmFuc2l0aW9uOiBhbGwgMC4xNXMgZWFzZTsgfVxcbi5kdXJhdGlvbi0yMDAgeyB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtczsgfVxcbi5kdXJhdGlvbi0zMDAgeyB0cmFuc2l0aW9uLWR1cmF0aW9uOiAzMDBtczsgfVxcbi5kdXJhdGlvbi01MDAgeyB0cmFuc2l0aW9uLWR1cmF0aW9uOiA1MDBtczsgfVxcbi5ob3ZlclxcXFw6c2NhbGUtMTA1OmhvdmVyIHsgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsgfVxcbi5hbmltYXRlLXNwaW4geyBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlOyB9XFxuLmFuaW1hdGUtYm91bmNlIHsgYW5pbWF0aW9uOiBib3VuY2UgMXMgaW5maW5pdGU7IH1cXG4uYW5pbWF0ZS1wdWxzZSB7IGFuaW1hdGlvbjogcHVsc2UgMnMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC42LCAxKSBpbmZpbml0ZTsgfVxcbi5mbGV4LTEgeyBmbGV4OiAxIDE7IH1cXG4ub3ZlcmZsb3cteS1hdXRvIHsgb3ZlcmZsb3cteTogYXV0bzsgfVxcbi5yZWxhdGl2ZSB7IHBvc2l0aW9uOiByZWxhdGl2ZTsgfVxcbi5hYnNvbHV0ZSB7IHBvc2l0aW9uOiBhYnNvbHV0ZTsgfVxcbi5pbnNldC0wIHsgdG9wOiAwOyByaWdodDogMDsgYm90dG9tOiAwOyBsZWZ0OiAwOyB9XFxuLmJvcmRlci10IHsgYm9yZGVyLXRvcC13aWR0aDogMXB4OyB9XFxuLmhpZGRlbiB7IGRpc3BsYXk6IG5vbmU7IH1cXG4uYmxvY2sgeyBkaXNwbGF5OiBibG9jazsgfVxcbi5oLWZ1bGwgeyBoZWlnaHQ6IDEwMCU7IH1cXG4uZmxleC1jb2wgeyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyB9XFxuXFxuLyogUmVzcG9uc2l2ZSB2aXNpYmlsaXR5ICovXFxuQG1lZGlhIChtaW4td2lkdGg6IDY0MHB4KSB7XFxuICAuc21cXFxcOmJsb2NrIHsgZGlzcGxheTogYmxvY2s7IH1cXG4gIC5zbVxcXFw6aGlkZGVuIHsgZGlzcGxheTogbm9uZTsgfVxcbn1cXG5cXG4vKiBDb21wb25lbnQgQ2xhc3NlcyAqL1xcbi5nbGFzcy1lZmZlY3Qge1xcbiAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCkgc2F0dXJhdGUoMTgwJSk7XFxuICAgICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpO1xcbiAgYm94LXNoYWRvdzpcXG4gICAgMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMTIpLFxcbiAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSxcXG4gICAgaW5zZXQgMCAtMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcXG4gIGJvcmRlci1yYWRpdXM6IDFyZW07XFxufVxcblxcbi8qIENoYXQgTWVzc2FnZXMgKi9cXG4ubWVzc2FnZS1jb250YWluZXIge1xcbiAgbWFyZ2luLWJvdHRvbTogMC44NzVyZW07XFxuICBkaXNwbGF5OiBmbGV4O1xcbn1cXG5cXG4ubWVzc2FnZS1jb250YWluZXIudXNlciB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xcbn1cXG5cXG4ubWVzc2FnZS1jb250YWluZXIuYXNzaXN0YW50IHtcXG4gIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcXG59XFxuXFxuLm1lc3NhZ2Uge1xcbiAgbWF4LXdpZHRoOiA3MCU7XFxuICBwYWRkaW5nOiAwLjYyNXJlbSAwLjg3NXJlbTtcXG4gIGJvcmRlci1yYWRpdXM6IDAuODc1cmVtO1xcbiAgd29yZC13cmFwOiBicmVhay13b3JkO1xcbiAgZm9udC1zaXplOiAwLjhyZW07XFxuICBsaW5lLWhlaWdodDogMS4zO1xcbn1cXG5cXG4udXNlci1tZXNzYWdlIHtcXG4gIGJhY2tncm91bmQ6ICMwMDdBRkY7XFxuICBjb2xvcjogd2hpdGU7XFxuICBib3JkZXItYm90dG9tLXJpZ2h0LXJhZGl1czogMC4yNXJlbTtcXG59XFxuXFxuLmFzc2lzdGFudC1tZXNzYWdlIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gIGNvbG9yOiB3aGl0ZTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xcbiAgICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XFxuICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAwLjI1cmVtO1xcbn1cXG5cXG4uYXVyYS1hbmltYXRpb24ge1xcbiAgYW5pbWF0aW9uOiBwdWxzZSAzcyBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjYsIDEpIGluZmluaXRlO1xcbiAgZmlsdGVyOiBibHVyKDFweCk7XFxufVxcblxcbi5pbnB1dC1maWVsZCB7XFxuICBmbGV4OiAxIDE7XFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XFxuICBib3JkZXItcmFkaXVzOiAxLjM3NXJlbTtcXG4gIHBhZGRpbmc6IDAuNjI1cmVtIDAuODc1cmVtO1xcbiAgY29sb3I6IHdoaXRlO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIHJlc2l6ZTogbm9uZTtcXG4gIGhlaWdodDogNDRweDtcXG4gIG91dGxpbmU6IG5vbmU7XFxuICB3aWR0aDogMTAwJTtcXG59XFxuXFxuLmlucHV0LWZpZWxkOjpwbGFjZWhvbGRlciB7XFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpO1xcbn1cXG5cXG4uaW5wdXQtZmllbGQ6Zm9jdXMge1xcbiAgb3V0bGluZTogbm9uZTtcXG4gIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC41KTtcXG59XFxuXFxuLnNlbmQtYnV0dG9uIHtcXG4gIGJhY2tncm91bmQ6ICMwMDdBRkY7XFxuICBib3JkZXI6IG5vbmU7XFxuICBib3JkZXItcmFkaXVzOiA1MCU7XFxuICB3aWR0aDogNDRweDtcXG4gIGhlaWdodDogNDRweDtcXG4gIGNvbG9yOiB3aGl0ZTtcXG4gIGN1cnNvcjogcG9pbnRlcjtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xcbiAgZmxleC1zaHJpbms6IDA7XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbn1cXG5cXG4uc2VuZC1idXR0b246aG92ZXIge1xcbiAgYmFja2dyb3VuZDogIzAwNTZDQztcXG59XFxuXFxuLnNlbmQtYnV0dG9uOmRpc2FibGVkIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XFxufVxcblxcbi5idG46aG92ZXIge1xcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcXG4gIGJveC1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xcbn1cXG5cXG4uYnRuOmFjdGl2ZSB7XFxuICB0cmFuc2Zvcm06IHNjYWxlKDAuOTUpO1xcbn1cXG5cXG4uYnRuOmRpc2FibGVkIHtcXG4gIG9wYWNpdHk6IDAuNTtcXG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XFxufVxcblxcbi5idG46ZGlzYWJsZWQ6aG92ZXIge1xcbiAgdHJhbnNmb3JtOiBub25lO1xcbn1cXG5cXG4vKiBDaGF0IENvbnRhaW5lciAtIEZ1bGwgU2NyZWVuICovXFxuLmNoYXQtY29udGFpbmVyIHtcXG4gIGhlaWdodDogMTAwdmg7XFxuICB3aWR0aDogMTAwdnc7XFxuICBkaXNwbGF5OiBmbGV4O1xcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xcbiAgICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XFxufVxcblxcbi8qIEhlYWRlciAqL1xcbi5jaGF0LWhlYWRlciB7XFxuICBoZWlnaHQ6IDU2cHg7XFxuICBwYWRkaW5nOiAwLjg3NXJlbTtcXG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMik7XFxuICBkaXNwbGF5OiBmbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG59XFxuXFxuLyogTWVzc2FnZXMgQXJlYSAqL1xcbi5jaGF0LW1lc3NhZ2VzIHtcXG4gIGZsZXg6IDEgMTtcXG4gIG92ZXJmbG93LXk6IGF1dG87XFxuICBwYWRkaW5nOiAxcmVtO1xcbiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xcbiAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTQwcHgpOyAvKiA2MHB4IGhlYWRlciArIDgwcHggaW5wdXQgKi9cXG59XFxuXFxuLmNoYXQtbWVzc2FnZXM6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcXG4gIGRpc3BsYXk6IG5vbmU7XFxufVxcblxcbi8qIElucHV0IEFyZWEgKi9cXG4uY2hhdC1pbnB1dC1hcmVhIHtcXG4gIGhlaWdodDogODBweDtcXG4gIHBhZGRpbmc6IDFyZW07XFxuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjIpO1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBnYXA6IDAuNXJlbTtcXG59XFxuXFxuLyogTW9iaWxlIEZpcnN0IFJlc3BvbnNpdmUgKi9cXG5AbWVkaWEgKG1heC13aWR0aDogNjQwcHgpIHtcXG4gIC5jaGF0LWhlYWRlciB7IHBhZGRpbmc6IDAuMjVyZW07IH1cXG4gIC5jaGF0LW1lc3NhZ2VzIHsgcGFkZGluZzogMCAwLjVyZW07IH1cXG4gIC5jaGF0LWlucHV0LWFyZWEgeyBwYWRkaW5nOiAwLjVyZW07IH1cXG4gIC50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMXJlbTsgbGluZS1oZWlnaHQ6IDEuMjVyZW07IH1cXG4gIC50ZXh0LXhsIHsgZm9udC1zaXplOiAwLjg3NXJlbTsgbGluZS1oZWlnaHQ6IDFyZW07IH1cXG4gIC5wLTYgeyBwYWRkaW5nOiAwLjc1cmVtOyB9XFxuICAubWItNiB7IG1hcmdpbi1ib3R0b206IDAuNXJlbTsgfVxcbiAgLmNoYXQtYnViYmxlIHtcXG4gICAgbWF4LXdpZHRoOiA4NSU7XFxuICAgIG1pbi13aWR0aDogNnJlbTtcXG4gICAgcGFkZGluZzogMC43NXJlbSAxcmVtO1xcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4zO1xcbiAgfVxcbiAgLmNoYXQtbWFpbi1jb250YWluZXIgeyBtYXgtd2lkdGg6IDEwMCU7IG1hcmdpbjogMDsgfVxcbn1cXG5cXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcXG4gIC5jaGF0LWhlYWRlciB7IHBhZGRpbmc6IDAuMTI1cmVtOyB9XFxuICAuY2hhdC1tZXNzYWdlcyB7IHBhZGRpbmc6IDAgMC4yNXJlbTsgfVxcbiAgLmNoYXQtaW5wdXQtYXJlYSB7IHBhZGRpbmc6IDAuMjVyZW07IH1cXG4gIC50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMC44NzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuICAuc3BhY2UteS0zID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjI1cmVtOyB9XFxuICAuc3BhY2UteS00ID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cXG4gIC5jaGF0LWJ1YmJsZSB7XFxuICAgIG1heC13aWR0aDogOTAlO1xcbiAgICBtaW4td2lkdGg6IDVyZW07XFxuICAgIHBhZGRpbmc6IDAuNjI1cmVtIDAuODc1cmVtO1xcbiAgICBmb250LXNpemU6IDAuOHJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDEuMjtcXG4gIH1cXG4gIC5pbnB1dC1maWVsZCB7IG1pbi1oZWlnaHQ6IDMycHg7IGZvbnQtc2l6ZTogMC43NXJlbTsgcGFkZGluZzogMC4zNzVyZW0gMC41cmVtOyB9XFxufVxcblxcbkBtZWRpYSAobWluLXdpZHRoOiA3NjhweCkge1xcbiAgLmNoYXQtbGF5b3V0IHsgbWF4LXdpZHRoOiA0eGw7IG1hcmdpbjogMCBhdXRvOyB9XFxufVxcblxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG4gIC5jaGF0LW1lc3NhZ2VzIHsgcGFkZGluZzogMCAycmVtOyB9XFxuICAuY2hhdC1pbnB1dC1hcmVhIHsgcGFkZGluZzogMS41cmVtIDJyZW07IH1cXG59XFxuXFxuLyogS2V5ZnJhbWUgQW5pbWF0aW9ucyAqL1xcbkBrZXlmcmFtZXMgc2xpZGVJbkZyb21Cb3R0b20ge1xcbiAgZnJvbSB7XFxuICAgIG9wYWNpdHk6IDA7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSg4cHgpO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgc3BpbiB7XFxuICBmcm9tIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cXG4gIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGJvdW5jZSB7XFxuICAwJSwgMTAwJSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMjUlKTtcXG4gICAgYW5pbWF0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuOCwgMCwgMSwgMSk7XFxuICB9XFxuICA1MCUge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxuICAgIGFuaW1hdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLCAwLCAwLjIsIDEpO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcXG4gIDAlLCAxMDAlIHsgb3BhY2l0eTogMTsgfVxcbiAgNTAlIHsgb3BhY2l0eTogMC41OyB9XFxufVxcblxcbkBrZXlmcmFtZXMgbGlxdWlkTW92ZSB7XFxuICAwJSwgMTAwJSB7XFxuICAgIGJhY2tncm91bmQ6XFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNDAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICB9XFxuICAyNSUge1xcbiAgICBiYWNrZ3JvdW5kOlxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNjAlIDMwJSwgcmdiYSgxMjAsIDExOSwgMTk4LCAwLjQpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMzAlIDcwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjQpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNzAlIDYwJSwgcmdiYSgxMjAsIDIxOSwgMjU1LCAwLjQpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpO1xcbiAgfVxcbiAgNTAlIHtcXG4gICAgYmFja2dyb3VuZDpcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDgwJSA2MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA0MCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDYwJSA4MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIH1cXG4gIDc1JSB7XFxuICAgIGJhY2tncm91bmQ6XFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNzAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA3MCUgMzAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAzMCUgMjAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICB9XFxufVxcblwiLCBcIlwiLHtcInZlcnNpb25cIjozLFwic291cmNlc1wiOltcIndlYnBhY2s6Ly9zcmMvc3R5bGVzL2dsb2JhbC5jc3NcIl0sXCJuYW1lc1wiOltdLFwibWFwcGluZ3NcIjpcIkFBQUEsd0JBQXdCO0FBQ3hCO0VBQ0UsU0FBUztFQUNULFVBQVU7RUFDVixzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSwyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsWUFBWTtFQUNaOzs7O2tFQUlnRTtFQUNoRSw0QkFBNEI7RUFDNUIsMkRBQTJEO0VBQzNELDhDQUE4QztBQUNoRDs7QUFFQSxvQkFBb0I7QUFDcEIsUUFBUSxhQUFhLEVBQUU7QUFDdkIsWUFBWSxzQkFBc0IsRUFBRTtBQUNwQyxnQkFBZ0IsbUJBQW1CLEVBQUU7QUFDckMsYUFBYSxxQkFBcUIsRUFBRTtBQUNwQyxrQkFBa0IsdUJBQXVCLEVBQUU7QUFDM0MsbUJBQW1CLDhCQUE4QixFQUFFO0FBQ25ELGVBQWUsa0JBQWtCLEVBQUU7QUFDbkMsYUFBYSxnQkFBZ0IsRUFBRTtBQUMvQixnQkFBZ0IsaUJBQWlCLEVBQUU7QUFDbkMsVUFBVSxXQUFXLEVBQUU7QUFDdkIsVUFBVSxZQUFZLEVBQUU7QUFDeEIsbUJBQW1CLGtCQUFrQixFQUFFO0FBQ3ZDLHVCQUF1QixxQkFBcUIsRUFBRTtBQUM5QyxhQUFhLGdCQUFnQixFQUFFO0FBQy9CLFdBQVcsaUJBQWlCLEVBQUUsa0JBQWtCLEVBQUU7QUFDbEQsT0FBTyxhQUFhLEVBQUU7QUFDdEIsT0FBTyxlQUFlLEVBQUU7QUFDeEIsUUFBUSxzQkFBc0IsRUFBRTtBQUNoQyxRQUFRLHFCQUFxQixFQUFFO0FBQy9CLFFBQVEsc0JBQXNCLEVBQUU7QUFDaEMscUJBQXFCLGtCQUFrQixFQUFFO0FBQ3pDLHFCQUFxQixtQkFBbUIsRUFBRTtBQUMxQyxxQkFBcUIsbUJBQW1CLEVBQUU7QUFDMUMscUJBQXFCLG9CQUFvQixFQUFFO0FBQzNDLFlBQVksa0JBQWtCLEVBQUUsbUJBQW1CLEVBQUU7QUFDckQsV0FBVyxlQUFlLEVBQUUsb0JBQW9CLEVBQUU7QUFDbEQsV0FBVyxrQkFBa0IsRUFBRSxpQkFBaUIsRUFBRTtBQUNsRCxXQUFXLG1CQUFtQixFQUFFLHFCQUFxQixFQUFFO0FBQ3ZELGFBQWEsZ0JBQWdCLEVBQUU7QUFDL0IsaUJBQWlCLGdCQUFnQixFQUFFO0FBQ25DLGNBQWMsWUFBWSxFQUFFO0FBQzVCLGNBQWMsWUFBWSxFQUFFO0FBQzVCLGNBQWMsWUFBWSxFQUFFO0FBQzVCLGNBQWMsWUFBWSxFQUFFO0FBQzVCLGVBQWUsbUJBQW1CLEVBQUU7QUFDcEMsY0FBYyxzQkFBc0IsRUFBRTtBQUN0QyxhQUFhLG1GQUFtRixFQUFFO0FBQ2xHLGtCQUFrQiwwQkFBMEIsRUFBRTtBQUM5QyxnQkFBZ0IsMEJBQTBCLEVBQUU7QUFDNUMsZ0JBQWdCLDBCQUEwQixFQUFFO0FBQzVDLGdCQUFnQiwwQkFBMEIsRUFBRTtBQUM1QywwQkFBMEIsc0JBQXNCLEVBQUU7QUFDbEQsZ0JBQWdCLGtDQUFrQyxFQUFFO0FBQ3BELGtCQUFrQiw2QkFBNkIsRUFBRTtBQUNqRCxpQkFBaUIseURBQXlELEVBQUU7QUFDNUUsVUFBVSxTQUFZLEVBQUU7QUFDeEIsbUJBQW1CLGdCQUFnQixFQUFFO0FBQ3JDLFlBQVksa0JBQWtCLEVBQUU7QUFDaEMsWUFBWSxrQkFBa0IsRUFBRTtBQUNoQyxXQUFXLE1BQU0sRUFBRSxRQUFRLEVBQUUsU0FBUyxFQUFFLE9BQU8sRUFBRTtBQUNqRCxZQUFZLHFCQUFxQixFQUFFO0FBQ25DLFVBQVUsYUFBYSxFQUFFO0FBQ3pCLFNBQVMsY0FBYyxFQUFFO0FBQ3pCLFVBQVUsWUFBWSxFQUFFO0FBQ3hCLFlBQVksc0JBQXNCLEVBQUU7O0FBRXBDLDBCQUEwQjtBQUMxQjtFQUNFLGFBQWEsY0FBYyxFQUFFO0VBQzdCLGNBQWMsYUFBYSxFQUFFO0FBQy9COztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFLGtEQUEwQztVQUExQywwQ0FBMEM7RUFDMUMscUNBQXFDO0VBQ3JDLDJDQUEyQztFQUMzQzs7OzRDQUcwQztFQUMxQyxtQkFBbUI7QUFDckI7O0FBRUEsa0JBQWtCO0FBQ2xCO0VBQ0UsdUJBQXVCO0VBQ3ZCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLGNBQWM7RUFDZCwwQkFBMEI7RUFDMUIsdUJBQXVCO0VBQ3ZCLHFCQUFxQjtFQUNyQixpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWixtQ0FBbUM7QUFDckM7O0FBRUE7RUFDRSxvQ0FBb0M7RUFDcEMsWUFBWTtFQUNaLDBDQUEwQztFQUMxQyxtQ0FBMkI7VUFBM0IsMkJBQTJCO0VBQzNCLGtDQUFrQztBQUNwQzs7QUFFQTtFQUNFLHlEQUF5RDtFQUN6RCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxTQUFPO0VBQ1Asb0NBQW9DO0VBQ3BDLDBDQUEwQztFQUMxQyx1QkFBdUI7RUFDdkIsMEJBQTBCO0VBQzFCLFlBQVk7RUFDWixtQkFBbUI7RUFDbkIsWUFBWTtFQUNaLFlBQVk7RUFDWixhQUFhO0VBQ2IsV0FBVztBQUNiOztBQUVBO0VBQ0UsK0JBQStCO0FBQ2pDOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDZDQUE2QztBQUMvQzs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxZQUFZO0VBQ1osWUFBWTtFQUNaLGVBQWU7RUFDZixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtFQUN2Qix5QkFBeUI7RUFDekIsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG9DQUFvQztFQUNwQyxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxzQkFBc0I7RUFDdEIsK0NBQStDO0FBQ2pEOztBQUVBO0VBQ0Usc0JBQXNCO0FBQ3hCOztBQUVBO0VBQ0UsWUFBWTtFQUNaLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGVBQWU7QUFDakI7O0FBRUEsaUNBQWlDO0FBQ2pDO0VBQ0UsYUFBYTtFQUNiLFlBQVk7RUFDWixhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLDhCQUE4QjtFQUM5QixtQ0FBMkI7VUFBM0IsMkJBQTJCO0FBQzdCOztBQUVBLFdBQVc7QUFDWDtFQUNFLFlBQVk7RUFDWixpQkFBaUI7RUFDakIsaURBQWlEO0VBQ2pELDhCQUE4QjtFQUM5QixhQUFhO0VBQ2IsbUJBQW1CO0FBQ3JCOztBQUVBLGtCQUFrQjtBQUNsQjtFQUNFLFNBQU87RUFDUCxnQkFBZ0I7RUFDaEIsYUFBYTtFQUNiLHFCQUFxQjtFQUNyQix3QkFBd0I7RUFDeEIsMkJBQTJCLEVBQUUsNkJBQTZCO0FBQzVEOztBQUVBO0VBQ0UsYUFBYTtBQUNmOztBQUVBLGVBQWU7QUFDZjtFQUNFLFlBQVk7RUFDWixhQUFhO0VBQ2IsOENBQThDO0VBQzlDLDhCQUE4QjtFQUM5QixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFdBQVc7QUFDYjs7QUFFQSw0QkFBNEI7QUFDNUI7RUFDRSxlQUFlLGdCQUFnQixFQUFFO0VBQ2pDLGlCQUFpQixpQkFBaUIsRUFBRTtFQUNwQyxtQkFBbUIsZUFBZSxFQUFFO0VBQ3BDLFlBQVksZUFBZSxFQUFFLG9CQUFvQixFQUFFO0VBQ25ELFdBQVcsbUJBQW1CLEVBQUUsaUJBQWlCLEVBQUU7RUFDbkQsT0FBTyxnQkFBZ0IsRUFBRTtFQUN6QixRQUFRLHFCQUFxQixFQUFFO0VBQy9CO0lBQ0UsY0FBYztJQUNkLGVBQWU7SUFDZixxQkFBcUI7SUFDckIsbUJBQW1CO0lBQ25CLGdCQUFnQjtFQUNsQjtFQUNBLHVCQUF1QixlQUFlLEVBQUUsU0FBUyxFQUFFO0FBQ3JEOztBQUVBO0VBQ0UsZUFBZSxpQkFBaUIsRUFBRTtFQUNsQyxpQkFBaUIsa0JBQWtCLEVBQUU7RUFDckMsbUJBQW1CLGdCQUFnQixFQUFFO0VBQ3JDLFlBQVksbUJBQW1CLEVBQUUsaUJBQWlCLEVBQUU7RUFDcEQscUJBQXFCLG1CQUFtQixFQUFFO0VBQzFDLHFCQUFxQixrQkFBa0IsRUFBRTtFQUN6QztJQUNFLGNBQWM7SUFDZCxlQUFlO0lBQ2YsMEJBQTBCO0lBQzFCLGlCQUFpQjtJQUNqQixnQkFBZ0I7RUFDbEI7RUFDQSxlQUFlLGdCQUFnQixFQUFFLGtCQUFrQixFQUFFLHdCQUF3QixFQUFFO0FBQ2pGOztBQUVBO0VBQ0UsZUFBZSxjQUFjLEVBQUUsY0FBYyxFQUFFO0FBQ2pEOztBQUVBO0VBQ0UsaUJBQWlCLGVBQWUsRUFBRTtFQUNsQyxtQkFBbUIsb0JBQW9CLEVBQUU7QUFDM0M7O0FBRUEsd0JBQXdCO0FBQ3hCO0VBQ0U7SUFDRSxVQUFVO0lBQ1YsMEJBQTBCO0VBQzVCO0VBQ0E7SUFDRSxVQUFVO0lBQ1Ysd0JBQXdCO0VBQzFCO0FBQ0Y7O0FBRUE7RUFDRSxPQUFPLHVCQUF1QixFQUFFO0VBQ2hDLEtBQUsseUJBQXlCLEVBQUU7QUFDbEM7O0FBRUE7RUFDRTtJQUNFLDJCQUEyQjtJQUMzQixxREFBcUQ7RUFDdkQ7RUFDQTtJQUNFLHdCQUF3QjtJQUN4QixxREFBcUQ7RUFDdkQ7QUFDRjs7QUFFQTtFQUNFLFdBQVcsVUFBVSxFQUFFO0VBQ3ZCLE1BQU0sWUFBWSxFQUFFO0FBQ3RCOztBQUVBO0VBQ0U7SUFDRTs7OztvRUFJZ0U7RUFDbEU7RUFDQTtJQUNFOzs7O29FQUlnRTtFQUNsRTtFQUNBO0lBQ0U7Ozs7b0VBSWdFO0VBQ2xFO0VBQ0E7SUFDRTs7OztvRUFJZ0U7RUFDbEU7QUFDRlwiLFwic291cmNlc0NvbnRlbnRcIjpbXCIvKiBSZXNldCBlIGJhc2Ugc3R5bGVzICovXFxuKiB7XFxuICBtYXJnaW46IDA7XFxuICBwYWRkaW5nOiAwO1xcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcXG59XFxuXFxuaHRtbCB7XFxuICBmb250LWZhbWlseTogJ0ludGVyJywgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG5ib2R5IHtcXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xcbiAgY29sb3I6IHdoaXRlO1xcbiAgYmFja2dyb3VuZDpcXG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgODAlIDIwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjMpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDQwJSA0MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xcbiAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCUsIDEwMCUgMTAwJSwgMTAwJSAxMDAlLCAxMDAlIDEwMCU7XFxuICBhbmltYXRpb246IGxpcXVpZE1vdmUgMjBzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcbn1cXG5cXG4vKiBVdGlsaXR5IENsYXNzZXMgKi9cXG4uZmxleCB7IGRpc3BsYXk6IGZsZXg7IH1cXG4uZmxleC1jb2wgeyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyB9XFxuLml0ZW1zLWNlbnRlciB7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IH1cXG4uaXRlbXMtZW5kIHsgYWxpZ24taXRlbXM6IGZsZXgtZW5kOyB9XFxuLmp1c3RpZnktY2VudGVyIHsganVzdGlmeS1jb250ZW50OiBjZW50ZXI7IH1cXG4uanVzdGlmeS1iZXR3ZWVuIHsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyB9XFxuLnRleHQtY2VudGVyIHsgdGV4dC1hbGlnbjogY2VudGVyOyB9XFxuLnRleHQtbGVmdCB7IHRleHQtYWxpZ246IGxlZnQ7IH1cXG4ubWluLWgtc2NyZWVuIHsgbWluLWhlaWdodDogMTAwdmg7IH1cXG4udy1mdWxsIHsgd2lkdGg6IDEwMCU7IH1cXG4uaC1mdWxsIHsgaGVpZ2h0OiAxMDAlOyB9XFxuLmxlYWRpbmctcmVsYXhlZCB7IGxpbmUtaGVpZ2h0OiAxLjYyNTsgfVxcbi53aGl0ZXNwYWNlLXByZS13cmFwIHsgd2hpdGUtc3BhY2U6IHByZS13cmFwOyB9XFxuLm1heC13LTR4bCB7IG1heC13aWR0aDogNTZyZW07IH1cXG4ubXgtYXV0byB7IG1hcmdpbi1sZWZ0OiBhdXRvOyBtYXJnaW4tcmlnaHQ6IGF1dG87IH1cXG4ucC00IHsgcGFkZGluZzogMXJlbTsgfVxcbi5wLTYgeyBwYWRkaW5nOiAxLjVyZW07IH1cXG4ubWItMiB7IG1hcmdpbi1ib3R0b206IDAuMjVyZW07IH1cXG4ubWItNCB7IG1hcmdpbi1ib3R0b206IDAuNXJlbTsgfVxcbi5tYi02IHsgbWFyZ2luLWJvdHRvbTogMC43NXJlbTsgfVxcbi5zcGFjZS15LTMgPiAqICsgKiB7IG1hcmdpbi10b3A6IDAuNXJlbTsgfVxcbi5zcGFjZS15LTQgPiAqICsgKiB7IG1hcmdpbi10b3A6IDAuNzVyZW07IH1cXG4uc3BhY2UteC0yID4gKiArICogeyBtYXJnaW4tbGVmdDogMC41cmVtOyB9XFxuLnNwYWNlLXgtMyA+ICogKyAqIHsgbWFyZ2luLWxlZnQ6IDAuNzVyZW07IH1cXG4udGV4dC0zeGwgeyBmb250LXNpemU6IDEuMjVyZW07IGxpbmUtaGVpZ2h0OiAxLjVyZW07IH1cXG4udGV4dC14bCB7IGZvbnQtc2l6ZTogMXJlbTsgbGluZS1oZWlnaHQ6IDEuMjVyZW07IH1cXG4udGV4dC1zbSB7IGZvbnQtc2l6ZTogMC43NXJlbTsgbGluZS1oZWlnaHQ6IDFyZW07IH1cXG4udGV4dC14cyB7IGZvbnQtc2l6ZTogMC42MjVyZW07IGxpbmUtaGVpZ2h0OiAwLjg3NXJlbTsgfVxcbi5mb250LWJvbGQgeyBmb250LXdlaWdodDogNzAwOyB9XFxuLmZvbnQtc2VtaWJvbGQgeyBmb250LXdlaWdodDogNjAwOyB9XFxuLnRleHQtd2hpdGUgeyBjb2xvcjogd2hpdGU7IH1cXG4ub3BhY2l0eS01MCB7IG9wYWNpdHk6IDAuNTsgfVxcbi5vcGFjaXR5LTYwIHsgb3BhY2l0eTogMC42OyB9XFxuLm9wYWNpdHktODAgeyBvcGFjaXR5OiAwLjg7IH1cXG4ucm91bmRlZC0yeGwgeyBib3JkZXItcmFkaXVzOiAxcmVtOyB9XFxuLnJvdW5kZWQteGwgeyBib3JkZXItcmFkaXVzOiAwLjc1cmVtOyB9XFxuLnNoYWRvdy1sZyB7IGJveC1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpOyB9XFxuLnRyYW5zaXRpb24tYWxsIHsgdHJhbnNpdGlvbjogYWxsIDAuMTVzIGVhc2U7IH1cXG4uZHVyYXRpb24tMjAwIHsgdHJhbnNpdGlvbi1kdXJhdGlvbjogMjAwbXM7IH1cXG4uZHVyYXRpb24tMzAwIHsgdHJhbnNpdGlvbi1kdXJhdGlvbjogMzAwbXM7IH1cXG4uZHVyYXRpb24tNTAwIHsgdHJhbnNpdGlvbi1kdXJhdGlvbjogNTAwbXM7IH1cXG4uaG92ZXJcXFxcOnNjYWxlLTEwNTpob3ZlciB7IHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7IH1cXG4uYW5pbWF0ZS1zcGluIHsgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTsgfVxcbi5hbmltYXRlLWJvdW5jZSB7IGFuaW1hdGlvbjogYm91bmNlIDFzIGluZmluaXRlOyB9XFxuLmFuaW1hdGUtcHVsc2UgeyBhbmltYXRpb246IHB1bHNlIDJzIGN1YmljLWJlemllcigwLjQsIDAsIDAuNiwgMSkgaW5maW5pdGU7IH1cXG4uZmxleC0xIHsgZmxleDogMSAxIDAlOyB9XFxuLm92ZXJmbG93LXktYXV0byB7IG92ZXJmbG93LXk6IGF1dG87IH1cXG4ucmVsYXRpdmUgeyBwb3NpdGlvbjogcmVsYXRpdmU7IH1cXG4uYWJzb2x1dGUgeyBwb3NpdGlvbjogYWJzb2x1dGU7IH1cXG4uaW5zZXQtMCB7IHRvcDogMDsgcmlnaHQ6IDA7IGJvdHRvbTogMDsgbGVmdDogMDsgfVxcbi5ib3JkZXItdCB7IGJvcmRlci10b3Atd2lkdGg6IDFweDsgfVxcbi5oaWRkZW4geyBkaXNwbGF5OiBub25lOyB9XFxuLmJsb2NrIHsgZGlzcGxheTogYmxvY2s7IH1cXG4uaC1mdWxsIHsgaGVpZ2h0OiAxMDAlOyB9XFxuLmZsZXgtY29sIHsgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsgfVxcblxcbi8qIFJlc3BvbnNpdmUgdmlzaWJpbGl0eSAqL1xcbkBtZWRpYSAobWluLXdpZHRoOiA2NDBweCkge1xcbiAgLnNtXFxcXDpibG9jayB7IGRpc3BsYXk6IGJsb2NrOyB9XFxuICAuc21cXFxcOmhpZGRlbiB7IGRpc3BsYXk6IG5vbmU7IH1cXG59XFxuXFxuLyogQ29tcG9uZW50IENsYXNzZXMgKi9cXG4uZ2xhc3MtZWZmZWN0IHtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KSBzYXR1cmF0ZSgxODAlKTtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMTUpO1xcbiAgYm94LXNoYWRvdzpcXG4gICAgMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMTIpLFxcbiAgICBpbnNldCAwIDFweCAwIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSxcXG4gICAgaW5zZXQgMCAtMXB4IDAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcXG4gIGJvcmRlci1yYWRpdXM6IDFyZW07XFxufVxcblxcbi8qIENoYXQgTWVzc2FnZXMgKi9cXG4ubWVzc2FnZS1jb250YWluZXIge1xcbiAgbWFyZ2luLWJvdHRvbTogMC44NzVyZW07XFxuICBkaXNwbGF5OiBmbGV4O1xcbn1cXG5cXG4ubWVzc2FnZS1jb250YWluZXIudXNlciB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xcbn1cXG5cXG4ubWVzc2FnZS1jb250YWluZXIuYXNzaXN0YW50IHtcXG4gIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcXG59XFxuXFxuLm1lc3NhZ2Uge1xcbiAgbWF4LXdpZHRoOiA3MCU7XFxuICBwYWRkaW5nOiAwLjYyNXJlbSAwLjg3NXJlbTtcXG4gIGJvcmRlci1yYWRpdXM6IDAuODc1cmVtO1xcbiAgd29yZC13cmFwOiBicmVhay13b3JkO1xcbiAgZm9udC1zaXplOiAwLjhyZW07XFxuICBsaW5lLWhlaWdodDogMS4zO1xcbn1cXG5cXG4udXNlci1tZXNzYWdlIHtcXG4gIGJhY2tncm91bmQ6ICMwMDdBRkY7XFxuICBjb2xvcjogd2hpdGU7XFxuICBib3JkZXItYm90dG9tLXJpZ2h0LXJhZGl1czogMC4yNXJlbTtcXG59XFxuXFxuLmFzc2lzdGFudC1tZXNzYWdlIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gIGNvbG9yOiB3aGl0ZTtcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcXG4gIGJvcmRlci1ib3R0b20tbGVmdC1yYWRpdXM6IDAuMjVyZW07XFxufVxcblxcbi5hdXJhLWFuaW1hdGlvbiB7XFxuICBhbmltYXRpb246IHB1bHNlIDNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuNiwgMSkgaW5maW5pdGU7XFxuICBmaWx0ZXI6IGJsdXIoMXB4KTtcXG59XFxuXFxuLmlucHV0LWZpZWxkIHtcXG4gIGZsZXg6IDE7XFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XFxuICBib3JkZXItcmFkaXVzOiAxLjM3NXJlbTtcXG4gIHBhZGRpbmc6IDAuNjI1cmVtIDAuODc1cmVtO1xcbiAgY29sb3I6IHdoaXRlO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIHJlc2l6ZTogbm9uZTtcXG4gIGhlaWdodDogNDRweDtcXG4gIG91dGxpbmU6IG5vbmU7XFxuICB3aWR0aDogMTAwJTtcXG59XFxuXFxuLmlucHV0LWZpZWxkOjpwbGFjZWhvbGRlciB7XFxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpO1xcbn1cXG5cXG4uaW5wdXQtZmllbGQ6Zm9jdXMge1xcbiAgb3V0bGluZTogbm9uZTtcXG4gIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC41KTtcXG59XFxuXFxuLnNlbmQtYnV0dG9uIHtcXG4gIGJhY2tncm91bmQ6ICMwMDdBRkY7XFxuICBib3JkZXI6IG5vbmU7XFxuICBib3JkZXItcmFkaXVzOiA1MCU7XFxuICB3aWR0aDogNDRweDtcXG4gIGhlaWdodDogNDRweDtcXG4gIGNvbG9yOiB3aGl0ZTtcXG4gIGN1cnNvcjogcG9pbnRlcjtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xcbiAgZmxleC1zaHJpbms6IDA7XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbn1cXG5cXG4uc2VuZC1idXR0b246aG92ZXIge1xcbiAgYmFja2dyb3VuZDogIzAwNTZDQztcXG59XFxuXFxuLnNlbmQtYnV0dG9uOmRpc2FibGVkIHtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XFxufVxcblxcbi5idG46aG92ZXIge1xcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcXG4gIGJveC1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xcbn1cXG5cXG4uYnRuOmFjdGl2ZSB7XFxuICB0cmFuc2Zvcm06IHNjYWxlKDAuOTUpO1xcbn1cXG5cXG4uYnRuOmRpc2FibGVkIHtcXG4gIG9wYWNpdHk6IDAuNTtcXG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XFxufVxcblxcbi5idG46ZGlzYWJsZWQ6aG92ZXIge1xcbiAgdHJhbnNmb3JtOiBub25lO1xcbn1cXG5cXG4vKiBDaGF0IENvbnRhaW5lciAtIEZ1bGwgU2NyZWVuICovXFxuLmNoYXQtY29udGFpbmVyIHtcXG4gIGhlaWdodDogMTAwdmg7XFxuICB3aWR0aDogMTAwdnc7XFxuICBkaXNwbGF5OiBmbGV4O1xcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcXG59XFxuXFxuLyogSGVhZGVyICovXFxuLmNoYXQtaGVhZGVyIHtcXG4gIGhlaWdodDogNTZweDtcXG4gIHBhZGRpbmc6IDAuODc1cmVtO1xcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbn1cXG5cXG4vKiBNZXNzYWdlcyBBcmVhICovXFxuLmNoYXQtbWVzc2FnZXMge1xcbiAgZmxleDogMTtcXG4gIG92ZXJmbG93LXk6IGF1dG87XFxuICBwYWRkaW5nOiAxcmVtO1xcbiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xcbiAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTQwcHgpOyAvKiA2MHB4IGhlYWRlciArIDgwcHggaW5wdXQgKi9cXG59XFxuXFxuLmNoYXQtbWVzc2FnZXM6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcXG4gIGRpc3BsYXk6IG5vbmU7XFxufVxcblxcbi8qIElucHV0IEFyZWEgKi9cXG4uY2hhdC1pbnB1dC1hcmVhIHtcXG4gIGhlaWdodDogODBweDtcXG4gIHBhZGRpbmc6IDFyZW07XFxuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjIpO1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBnYXA6IDAuNXJlbTtcXG59XFxuXFxuLyogTW9iaWxlIEZpcnN0IFJlc3BvbnNpdmUgKi9cXG5AbWVkaWEgKG1heC13aWR0aDogNjQwcHgpIHtcXG4gIC5jaGF0LWhlYWRlciB7IHBhZGRpbmc6IDAuMjVyZW07IH1cXG4gIC5jaGF0LW1lc3NhZ2VzIHsgcGFkZGluZzogMCAwLjVyZW07IH1cXG4gIC5jaGF0LWlucHV0LWFyZWEgeyBwYWRkaW5nOiAwLjVyZW07IH1cXG4gIC50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMXJlbTsgbGluZS1oZWlnaHQ6IDEuMjVyZW07IH1cXG4gIC50ZXh0LXhsIHsgZm9udC1zaXplOiAwLjg3NXJlbTsgbGluZS1oZWlnaHQ6IDFyZW07IH1cXG4gIC5wLTYgeyBwYWRkaW5nOiAwLjc1cmVtOyB9XFxuICAubWItNiB7IG1hcmdpbi1ib3R0b206IDAuNXJlbTsgfVxcbiAgLmNoYXQtYnViYmxlIHtcXG4gICAgbWF4LXdpZHRoOiA4NSU7XFxuICAgIG1pbi13aWR0aDogNnJlbTtcXG4gICAgcGFkZGluZzogMC43NXJlbSAxcmVtO1xcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4zO1xcbiAgfVxcbiAgLmNoYXQtbWFpbi1jb250YWluZXIgeyBtYXgtd2lkdGg6IDEwMCU7IG1hcmdpbjogMDsgfVxcbn1cXG5cXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcXG4gIC5jaGF0LWhlYWRlciB7IHBhZGRpbmc6IDAuMTI1cmVtOyB9XFxuICAuY2hhdC1tZXNzYWdlcyB7IHBhZGRpbmc6IDAgMC4yNXJlbTsgfVxcbiAgLmNoYXQtaW5wdXQtYXJlYSB7IHBhZGRpbmc6IDAuMjVyZW07IH1cXG4gIC50ZXh0LTN4bCB7IGZvbnQtc2l6ZTogMC44NzVyZW07IGxpbmUtaGVpZ2h0OiAxcmVtOyB9XFxuICAuc3BhY2UteS0zID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjI1cmVtOyB9XFxuICAuc3BhY2UteS00ID4gKiArICogeyBtYXJnaW4tdG9wOiAwLjVyZW07IH1cXG4gIC5jaGF0LWJ1YmJsZSB7XFxuICAgIG1heC13aWR0aDogOTAlO1xcbiAgICBtaW4td2lkdGg6IDVyZW07XFxuICAgIHBhZGRpbmc6IDAuNjI1cmVtIDAuODc1cmVtO1xcbiAgICBmb250LXNpemU6IDAuOHJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDEuMjtcXG4gIH1cXG4gIC5pbnB1dC1maWVsZCB7IG1pbi1oZWlnaHQ6IDMycHg7IGZvbnQtc2l6ZTogMC43NXJlbTsgcGFkZGluZzogMC4zNzVyZW0gMC41cmVtOyB9XFxufVxcblxcbkBtZWRpYSAobWluLXdpZHRoOiA3NjhweCkge1xcbiAgLmNoYXQtbGF5b3V0IHsgbWF4LXdpZHRoOiA0eGw7IG1hcmdpbjogMCBhdXRvOyB9XFxufVxcblxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG4gIC5jaGF0LW1lc3NhZ2VzIHsgcGFkZGluZzogMCAycmVtOyB9XFxuICAuY2hhdC1pbnB1dC1hcmVhIHsgcGFkZGluZzogMS41cmVtIDJyZW07IH1cXG59XFxuXFxuLyogS2V5ZnJhbWUgQW5pbWF0aW9ucyAqL1xcbkBrZXlmcmFtZXMgc2xpZGVJbkZyb21Cb3R0b20ge1xcbiAgZnJvbSB7XFxuICAgIG9wYWNpdHk6IDA7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSg4cHgpO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxuICB9XFxufVxcblxcbkBrZXlmcmFtZXMgc3BpbiB7XFxuICBmcm9tIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cXG4gIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIGJvdW5jZSB7XFxuICAwJSwgMTAwJSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMjUlKTtcXG4gICAgYW5pbWF0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuOCwgMCwgMSwgMSk7XFxuICB9XFxuICA1MCUge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxuICAgIGFuaW1hdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLCAwLCAwLjIsIDEpO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcXG4gIDAlLCAxMDAlIHsgb3BhY2l0eTogMTsgfVxcbiAgNTAlIHsgb3BhY2l0eTogMC41OyB9XFxufVxcblxcbkBrZXlmcmFtZXMgbGlxdWlkTW92ZSB7XFxuICAwJSwgMTAwJSB7XFxuICAgIGJhY2tncm91bmQ6XFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNDAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuMykgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICB9XFxuICAyNSUge1xcbiAgICBiYWNrZ3JvdW5kOlxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNjAlIDMwJSwgcmdiYSgxMjAsIDExOSwgMTk4LCAwLjQpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMzAlIDcwJSwgcmdiYSgyNTUsIDExOSwgMTk4LCAwLjQpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNzAlIDYwJSwgcmdiYSgxMjAsIDIxOSwgMjU1LCAwLjQpIDAlLCB0cmFuc3BhcmVudCA1MCUpLFxcbiAgICAgIGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxYTFhMmUgMCUsICMxNjIxM2UgNTAlLCAjMGYzNDYwIDEwMCUpO1xcbiAgfVxcbiAgNTAlIHtcXG4gICAgYmFja2dyb3VuZDpcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDgwJSA2MCUsIHJnYmEoMTIwLCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDIwJSA0MCUsIHJnYmEoMjU1LCAxMTksIDE5OCwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDYwJSA4MCUsIHJnYmEoMTIwLCAyMTksIDI1NSwgMC4zKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcXG4gICAgICBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWExYTJlIDAlLCAjMTYyMTNlIDUwJSwgIzBmMzQ2MCAxMDAlKTtcXG4gIH1cXG4gIDc1JSB7XFxuICAgIGJhY2tncm91bmQ6XFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA0MCUgNzAlLCByZ2JhKDEyMCwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA3MCUgMzAlLCByZ2JhKDI1NSwgMTE5LCAxOTgsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAzMCUgMjAlLCByZ2JhKDEyMCwgMjE5LCAyNTUsIDAuNCkgMCUsIHRyYW5zcGFyZW50IDUwJSksXFxuICAgICAgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFhMWEyZSAwJSwgIzE2MjEzZSA1MCUsICMwZjM0NjAgMTAwJSk7XFxuICB9XFxufVxcblwiXSxcInNvdXJjZVJvb3RcIjpcIlwifV0pO1xuLy8gRXhwb3J0c1xuZXhwb3J0IGRlZmF1bHQgX19fQ1NTX0xPQURFUl9FWFBPUlRfX187XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./src/styles/global.css\n"));

/***/ })

});