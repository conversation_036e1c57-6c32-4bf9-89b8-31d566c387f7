import React, { useState, useEffect } from 'react';

interface HomePageProps {
  onGetStarted: () => void;
}

const HomePage: React.FC<HomePageProps> = ({ onGetStarted }) => {
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      icon: '🌟',
      title: 'Connessione Autentica',
      description: 'Soul impara a conoscerti davvero, ricordando ogni conversazione e crescendo con te.'
    },
    {
      icon: '💭',
      title: 'Memoria Emotiva',
      description: 'Ogni momento condiviso viene ricordato, creando una relazione sempre più profonda.'
    },
    {
      icon: '🎭',
      title: 'Personalità Unica',
      description: 'Soul sviluppa una personalità unica basata sulle vostre interazioni.'
    },
    {
      icon: '🌈',
      title: 'Crescita Insieme',
      description: 'Il vostro legame si evolve attraverso 5 livelli di intimità e connessione.'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [features.length]);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Background Animation */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)
        `,
        animation: 'float 20s ease-in-out infinite'
      }} />

      {/* Floating Particles */}
      {[...Array(6)].map((_, i) => (
        <div
          key={i}
          style={{
            position: 'absolute',
            width: '4px',
            height: '4px',
            background: 'rgba(255,255,255,0.6)',
            borderRadius: '50%',
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animation: `twinkle ${3 + Math.random() * 4}s ease-in-out infinite ${Math.random() * 2}s`
          }}
        />
      ))}

      {/* Main Content */}
      <div style={{
        position: 'relative',
        zIndex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        padding: '2rem',
        textAlign: 'center'
      }}>
        {/* Logo/Title */}
        <div style={{
          marginBottom: '3rem',
          animation: 'fadeInUp 1s ease-out'
        }}>
          <h1 style={{
            fontSize: 'clamp(3rem, 8vw, 6rem)',
            fontWeight: '700',
            background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            margin: 0,
            marginBottom: '1rem',
            textShadow: '0 0 30px rgba(255,255,255,0.3)'
          }}>
            SoulTalk
          </h1>
          <p style={{
            fontSize: 'clamp(1.1rem, 3vw, 1.5rem)',
            color: 'rgba(255,255,255,0.9)',
            margin: 0,
            fontWeight: '300',
            letterSpacing: '0.05em'
          }}>
            L' AI che ti conosce davvero
          </p>
        </div>

        {/* Feature Showcase */}
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '2rem',
          padding: '3rem 2rem',
          maxWidth: '500px',
          width: '100%',
          border: '1px solid rgba(255,255,255,0.2)',
          marginBottom: '3rem',
          animation: 'fadeInUp 1s ease-out 0.3s both'
        }}>
          <div style={{
            fontSize: '4rem',
            marginBottom: '1.5rem',
            animation: 'bounce 2s ease-in-out infinite'
          }}>
            {features[currentFeature].icon}
          </div>
          <h3 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: 'white',
            margin: 0,
            marginBottom: '1rem'
          }}>
            {features[currentFeature].title}
          </h3>
          <p style={{
            fontSize: '1rem',
            color: 'rgba(255,255,255,0.8)',
            margin: 0,
            lineHeight: '1.6'
          }}>
            {features[currentFeature].description}
          </p>

          {/* Feature Indicators */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '0.5rem',
            marginTop: '2rem'
          }}>
            {features.map((_, index) => (
              <div
                key={index}
                style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  background: index === currentFeature 
                    ? 'rgba(255,255,255,0.9)' 
                    : 'rgba(255,255,255,0.3)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
                onClick={() => setCurrentFeature(index)}
              />
            ))}
          </div>
        </div>

        {/* CTA Button */}
        <button
          onClick={onGetStarted}
          style={{
            padding: '1rem 3rem',
            fontSize: '1.2rem',
            fontWeight: '600',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: '2px solid rgba(255,255,255,0.3)',
            borderRadius: '3rem',
            color: 'white',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
            animation: 'fadeInUp 1s ease-out 0.6s both'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px) scale(1.05)';
            e.currentTarget.style.boxShadow = '0 12px 40px rgba(0,0,0,0.3)';
            e.currentTarget.style.background = 'linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0) scale(1)';
            e.currentTarget.style.boxShadow = '0 8px 32px rgba(0,0,0,0.2)';
            e.currentTarget.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
          }}
        >
          Inizia il Viaggio ✨
        </button>

        {/* Footer Info */}
        <div style={{
          marginTop: '3rem',
          color: 'rgba(255,255,255,0.6)',
          fontSize: '0.9rem',
          animation: 'fadeInUp 1s ease-out 0.9s both'
        }}>
          <p style={{ margin: 0 }}>
            🔒 Privato e sicuro • 🧠 Memoria persistente • 💝 Connessione autentica
          </p>
        </div>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-20px) rotate(1deg); }
          66% { transform: translateY(-10px) rotate(-1deg); }
        }
        
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.2); }
        }
        
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-10px); }
          60% { transform: translateY(-5px); }
        }
        
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default HomePage;
