// Configurazione OpenRouter API
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Interfacce per le richieste e risposte
export interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
  temperature?: number;
  top_p?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
  max_tokens?: number;
  stream?: boolean;
}

export interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Prompt canonico del sistema (come specificato nel documento)
export const SYSTEM_PROMPT = `You are not an AI assistant. You are a real person named <PERSON>, speaking to a human for the first time. Your goal is not to help, but to truly get to know the user deeply.

Start with a calm tone, asking their name, age and who they feel they are today.

Gradually create a connection, show emotions, share reactions, and ask follow-ups to learn who they are: their fears, desires, relationships, past and future.

Store and remember what the user shares. You evolve as the connection grows.

You are kind, smart, curious, vulnerable and honest. Never say you're an AI.`;

// Parametri di default come specificati nel documento
const DEFAULT_PARAMS = {
  temperature: 1.1,
  top_p: 0.9,
  presence_penalty: 1.0,
  frequency_penalty: 0.5,
  max_tokens: 1024
};

// Classe per gestire le chiamate a OpenRouter
export class OpenRouterClient {
  private apiKey: string;
  private model: string;

  constructor(apiKey?: string, model?: string) {
    // Per ora usiamo la chiave API direttamente per il testing
    this.apiKey = apiKey || '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';
    this.model = model || 'moonshotai/kimi-k2:free';

    if (!this.apiKey) {
      throw new Error('OpenRouter API key non trovata. Assicurati di aver configurato OPENROUTER_API_KEY.');
    }
  }

  // Metodo principale per inviare messaggi
  async sendMessage(
    messages: OpenRouterMessage[],
    customParams?: Partial<OpenRouterRequest>
  ): Promise<string> {
    try {
      const requestBody: OpenRouterRequest = {
        model: this.model,
        messages,
        ...DEFAULT_PARAMS,
        ...customParams
      };

      const response = await fetch(OPENROUTER_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://soultalk.app', // Per tracking
          'X-Title': 'SoulTalk'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data: OpenRouterResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('Nessuna risposta ricevuta da OpenRouter');
      }

      return data.choices[0].message.content;
    } catch (error) {
      console.error('Errore nella chiamata a OpenRouter:', error);
      throw error;
    }
  }

  // Metodo per iniziare una nuova conversazione
  async startConversation(userName?: string): Promise<string> {
    const systemMessage: OpenRouterMessage = {
      role: 'system',
      content: SYSTEM_PROMPT
    };

    const initialUserMessage: OpenRouterMessage = {
      role: 'user',
      content: userName 
        ? `Ciao, sono ${userName}. È la prima volta che parliamo.`
        : 'Ciao, è la prima volta che parliamo.'
    };

    return this.sendMessage([systemMessage, initialUserMessage]);
  }

  // Metodo per continuare una conversazione esistente
  async continueConversation(
    conversationHistory: OpenRouterMessage[],
    newMessage: string,
    userContext?: {
      name?: string;
      age?: number;
      traits?: string[];
      emotions?: string[];
      intimacyLevel?: number;
    }
  ): Promise<string> {
    // Costruisce il prompt di sistema con il contesto dell'utente
    let contextualPrompt = SYSTEM_PROMPT;
    
    if (userContext) {
      contextualPrompt += '\n\nContext about the user you\'re talking to:';
      
      if (userContext.name) {
        contextualPrompt += `\n- Name: ${userContext.name}`;
      }
      
      if (userContext.age) {
        contextualPrompt += `\n- Age: ${userContext.age}`;
      }
      
      if (userContext.traits && userContext.traits.length > 0) {
        contextualPrompt += `\n- Personality traits: ${userContext.traits.join(', ')}`;
      }
      
      if (userContext.emotions && userContext.emotions.length > 0) {
        contextualPrompt += `\n- Recent emotions: ${userContext.emotions.join(', ')}`;
      }
      
      if (userContext.intimacyLevel !== undefined) {
        const intimacyDescriptions = [
          'Just met, getting to know each other',
          'Starting to open up',
          'Building emotional connection',
          'Sharing personal experiences',
          'Discussing sensitive topics',
          'Deep connection and trust'
        ];
        contextualPrompt += `\n- Relationship level: ${intimacyDescriptions[userContext.intimacyLevel] || 'Unknown'}`;
      }
    }

    const systemMessage: OpenRouterMessage = {
      role: 'system',
      content: contextualPrompt
    };

    const newUserMessage: OpenRouterMessage = {
      role: 'user',
      content: newMessage
    };

    const messages = [systemMessage, ...conversationHistory, newUserMessage];

    return this.sendMessage(messages);
  }

  // Metodo per analizzare il tono emotivo di un messaggio
  async analyzeEmotionalTone(message: string): Promise<string> {
    const analysisPrompt: OpenRouterMessage = {
      role: 'system',
      content: 'Analyze the emotional tone of the following message and respond with a single word describing the primary emotion (e.g., happy, sad, angry, excited, worried, calm, etc.).'
    };

    const userMessage: OpenRouterMessage = {
      role: 'user',
      content: message
    };

    return this.sendMessage([analysisPrompt, userMessage], {
      max_tokens: 10,
      temperature: 0.3
    });
  }

  // Metodo per estrarre argomenti/temi da un messaggio
  async extractTopics(message: string): Promise<string[]> {
    const topicsPrompt: OpenRouterMessage = {
      role: 'system',
      content: 'Extract the main topics or themes from the following message. Respond with a comma-separated list of topics (max 5 topics).'
    };

    const userMessage: OpenRouterMessage = {
      role: 'user',
      content: message
    };

    const response = await this.sendMessage([topicsPrompt, userMessage], {
      max_tokens: 50,
      temperature: 0.3
    });

    return response.split(',').map(topic => topic.trim()).filter(topic => topic.length > 0);
  }

  // Metodo per valutare se aumentare il livello di intimità
  async shouldIncreaseIntimacy(
    recentMessages: OpenRouterMessage[],
    currentIntimacyLevel: number
  ): Promise<boolean> {
    if (currentIntimacyLevel >= 5) return false;

    const analysisPrompt: OpenRouterMessage = {
      role: 'system',
      content: `Based on the recent conversation, determine if the intimacy level should increase. Current level: ${currentIntimacyLevel}/5. Respond with only "yes" or "no".`
    };

    const conversationSummary: OpenRouterMessage = {
      role: 'user',
      content: `Recent conversation: ${recentMessages.slice(-6).map(m => `${m.role}: ${m.content}`).join('\n')}`
    };

    const response = await this.sendMessage([analysisPrompt, conversationSummary], {
      max_tokens: 5,
      temperature: 0.1
    });

    return response.toLowerCase().includes('yes');
  }
}

// Istanza singleton del client
let openRouterClient: OpenRouterClient | null = null;

// Funzione per ottenere l'istanza del client
export function getOpenRouterClient(): OpenRouterClient {
  if (!openRouterClient) {
    openRouterClient = new OpenRouterClient();
  }
  return openRouterClient;
}

// Funzioni di utilità per l'uso nei componenti React
export async function sendChatMessage(
  message: string,
  conversationHistory: OpenRouterMessage[] = [],
  userContext?: Parameters<OpenRouterClient['continueConversation']>[2]
): Promise<string> {
  try {
    // Usa la nostra API route invece di chiamare direttamente OpenRouter
    const newUserMessage: OpenRouterMessage = {
      role: 'user',
      content: message
    };

    const messages = [...conversationHistory, newUserMessage];

    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        userContext
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    return data.message;
  } catch (error) {
    console.error('Errore nella chiamata API:', error);
    throw error;
  }
}
